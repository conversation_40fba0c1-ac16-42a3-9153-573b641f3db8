<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	
	<parent>
		<groupId>net.poweroak.cloud-saas.bucks-center</groupId>
		<artifactId>bucks-center</artifactId>
		<version>1.0.0-SNAPSHOT</version>
	</parent>
	
	<artifactId>bucks-center-api</artifactId>
	
	<dependencies>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
		</dependency>
		
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-openfeign-core</artifactId>
		</dependency>

        <dependency>
            <groupId>net.poweroak.framework</groupId>
            <artifactId>framework-util</artifactId>
        </dependency>

		<dependency>
			<groupId>net.poweroak.framework</groupId>
			<artifactId>framework-bluetti-app</artifactId>
		</dependency>
    </dependencies>
	
	<build>
		<plugins>
			<!-- 打包、发布时附带源码包 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
			</plugin>
		</plugins>
	</build>
</project>
