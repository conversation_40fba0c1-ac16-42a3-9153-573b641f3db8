package net.poweroak.saas.bucksc.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 光伏发电
 *
 * <AUTHOR>
 * @date 2022/5/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("发放积分-光伏发电")
public class PointsEarnPvPowerRequest extends PointsEarnBaseRequest {

    @ApiModelProperty(value = "发电度数")
    @NotNull(message = "发电度数不能为空")
    private BigDecimal degree;

    @ApiModelProperty(value = "发放门槛")
    private BigDecimal earnLimit;

    @ApiModelProperty(value = "设备sn号")
    @NotBlank(message = "设备SN不能为空")
    private String sn;

    @ApiModelProperty(value = "数据读取开始时间")
    @NotBlank(message = "数据读取开始时间不能为空")
    private String beginTime;

    @ApiModelProperty(value = "数据结束时间")
    @NotBlank(message = "数据读取结束时间不能为空")
    private String endTime;
}
