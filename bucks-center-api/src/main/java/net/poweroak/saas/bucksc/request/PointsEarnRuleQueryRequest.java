package net.poweroak.saas.bucksc.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.saas.bucksc.common.ActivityEarnCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 基础
 *
 * <AUTHOR>
 * @date 2022/5/11 14:09
 */
@Data
public class PointsEarnRuleQueryRequest implements Serializable {

    @ApiModelProperty(value = "国家编码")
    private Long countryId;

    @ApiModelProperty(value = "渠道ID(店铺ID)")
    private String channelCode;

    @ApiModelProperty(value = "金额")
    @NotNull(message = "金额不能为空")
    private BigDecimal amount;

    @ApiModelProperty(value = "活动编码，可不填，默认店铺订单")
    private String activityCode;

    public PointsEarnRuleQueryRequest() {
        this.activityCode = ActivityEarnCode.CURRENCY_EXCHANGE.getCode();
    }
}
