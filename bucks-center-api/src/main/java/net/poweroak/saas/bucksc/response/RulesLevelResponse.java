package net.poweroak.saas.bucksc.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 规则-等级
 *
 * <AUTHOR>
 * @date 2022/6/72 14:31
 */
@Data
@ApiModel("规则-等级-api")
public class RulesLevelResponse implements Serializable {


    @ApiModelProperty(value = "等级名称")
    private String title;

    @ApiModelProperty(value = "等级名称1")
    private String memberTitle;

    @ApiModelProperty(value = "等级")
    private Integer memberLevel;

    @ApiModelProperty(value = "比例")
    private BigDecimal ratio;

    @ApiModelProperty(value = "额外奖励")
    private BigDecimal extraReward;

    @ApiModelProperty(value = "门槛")
    private BigDecimal earnLimit;
}
