package net.poweroak.saas.bucksc.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * Created by zx on 2022/5/10 17:31
 */
@Data
public class PointsSpendRequest {

    @ApiModelProperty(value = "来源")
    private Integer resourceType;

    @ApiModelProperty(value = "用户ID")
    private String uid;

    @ApiModelProperty(value = "积分")
    @NotNull(message = "积分值不能为空")
    private Integer points;

    @ApiModelProperty(value = "渠道标识")
    private String channelCode;

    @ApiModelProperty(value = "活动编码")
    private String activityCode;

    @ApiModelProperty(value = "国家编码")
    @NotNull(message = "所属国家区域不能为空")
    private Long countryId;

    @ApiModelProperty(value = "订单ID")
    private String orderId;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "金额（shopify记录金额增减）")
    private BigDecimal amount;

    @ApiModelProperty(value = "活动Id")
    private String activityId;

    @ApiModelProperty(value = "费金额增减：默认true加、false减")
    private Boolean amountSymbol;
}
