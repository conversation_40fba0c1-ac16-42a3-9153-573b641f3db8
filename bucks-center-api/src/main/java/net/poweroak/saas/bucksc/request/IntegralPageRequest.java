package net.poweroak.saas.bucksc.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.page.PageInfo;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 获取积分明细列表
 * <AUTHOR>
 * @Mailto <EMAIL>
 * @Create 2024/5/10 10:00
 **/
@Data
public class IntegralPageRequest { // extends PageInfo

    @ApiModelProperty(value = "id")
    private String id;

    @NotNull(message = "国家编码不能为空")
    @ApiModelProperty(value = "国家编码")
    private String countryCode;

    @NotNull(message = "渠道编码不能为空")
    @ApiModelProperty(value = "渠道编码")
    private String channelCode;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "结束时间 yyyy-MM-dd hh:mm:ss")
    private Date endTime;

    @ApiModelProperty(value = "开始时间 yyyy-MM-dd hh:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date beginTime;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "每页条数")
    private Integer pageSize;

    @ApiModelProperty(value = "当前页")
    private Integer pageNo;

}
