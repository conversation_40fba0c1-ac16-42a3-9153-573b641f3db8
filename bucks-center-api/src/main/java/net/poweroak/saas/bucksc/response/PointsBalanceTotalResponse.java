package net.poweroak.saas.bucksc.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @date 2022/5/7 9:15
 */
@Data
@ApiModel("积分汇总查询接口-api")
public class PointsBalanceTotalResponse implements Serializable {

    public PointsBalanceTotalResponse(){
        this.earnTotal = 0;
        this.channelTotal = 0;
        this.otherChannelTotal = 0;
        this.spendTotal = 0;
        this.balanceTotal = 0;
        this.validTotal = 0;
    }

    @ApiModelProperty(value = "发放总积分(本渠道总积分+其他渠道)")
    private Integer earnTotal;

    @ApiModelProperty(value = "消耗")
    private Integer spendTotal;

    @ApiModelProperty(value = "余额")
    private Integer balanceTotal;

    @ApiModelProperty(value = "本店铺渠道(排除同个国家其他店铺)+公共")
    private Integer channelAndCommonTotal;

    @ApiModelProperty(value = "本渠道总积分")
    private Integer channelTotal;

    @ApiModelProperty(value = "其他渠道总积分")
    private Integer otherChannelTotal;

    @ApiModelProperty(value = "即将过期")
    private Integer validTotal;
}
