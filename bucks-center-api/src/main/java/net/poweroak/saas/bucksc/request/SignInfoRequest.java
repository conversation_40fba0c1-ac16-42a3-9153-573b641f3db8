package net.poweroak.saas.bucksc.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 基础
 *
 * <AUTHOR>
 * @date 2022/5/11 14:09
 */
@Data
public class SignInfoRequest implements Serializable {

    @ApiModelProperty(value = "用户ID")
    private String uid;

    @ApiModelProperty(value = "国家编码")
    private Long countryId;

    @ApiModelProperty(value = "渠道编码")
    private String channelCode;
}
