package net.poweroak.saas.bucksc.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * 规则
 *
 * <AUTHOR>
 * @date 2022/6/72 14:31
 */
@Data
@ApiModel("规则-api")
public class RulesResponse implements Serializable {


    @ApiModelProperty(value = "问题")
    private String title;

    @ApiModelProperty(value = "答案")
    private String content;
}
