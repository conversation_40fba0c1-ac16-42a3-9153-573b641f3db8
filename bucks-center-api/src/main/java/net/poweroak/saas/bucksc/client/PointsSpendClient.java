package net.poweroak.saas.bucksc.client;

import io.swagger.annotations.ApiOperation;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.saas.bucksc.request.LotteryRuleRequest;
import net.poweroak.saas.bucksc.request.PointsSpendRequest;
import net.poweroak.saas.bucksc.response.LotteryRuleResponse;
import net.poweroak.saas.bucksc.response.PointsSpendResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * Created by zx on 2022/5/10 15:49 积分消耗
 */
@FeignClient(value = "blu-bucks-center" + "${spring.application.feignid:}", path = "/bucksApi/pointsSpendRule")
public interface PointsSpendClient {

    @PostMapping("/lottery")
    @ApiOperation("抽奖")
    UnifyResponse<PointsSpendResponse> lottery(@RequestBody @Valid PointsSpendRequest request);

    @PostMapping("/couponExchange")
    @ApiOperation("卡券兑换")
    UnifyResponse<PointsSpendResponse> couponExchange(@RequestBody @Valid PointsSpendRequest request);

    @PostMapping("/goodsExchange")
    @ApiOperation("积分兑换物品")
    UnifyResponse<PointsSpendResponse> goodsExchange(@RequestBody @Valid PointsSpendRequest request);

    @PostMapping("/backGoodsExchange")
    @ApiOperation("回退 - 积分兑换物品")
    UnifyResponse<PointsSpendResponse> backGoodsExchange(@RequestBody @Valid PointsSpendRequest request);

    @PostMapping("/back")
    @ApiOperation("回退 - 购买商品赠送积分")
    UnifyResponse<PointsSpendResponse> pointsBack(@RequestBody @Valid PointsSpendRequest request);


    @PostMapping("/lotteryRule")
    @ApiOperation("抽奖积分规则")
    UnifyResponse<LotteryRuleResponse> lotteryRule(@RequestBody @Valid LotteryRuleRequest request);


}
