package net.poweroak.saas.bucksc.common;

import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date 2022/6/11 11:45
 */
@ApiOperation("积分消耗活动编码")
public enum ActivitySpendCode {
    COUPON_CARD("HDS01", "卡券兑换", 1),
    CURRENCY_EXCHANGE("HDS02", "购物下单", 2),
    CURRENCY_DEDUCTION("HDS03", "货币抵扣", 3),

    NW("HDS04", "轮盘抽奖", 4),
    VW("HDS05", "情人节轮盘抽奖", 5),
    SC("HDS06", "春季轮盘抽奖", 6),
    HAPPY_APRIL("HDS07", "开心四月轮盘抽奖", 7),
    UAJ_APRIL("HDS08", "美澳日轮盘抽奖", 8),

    JP_2024_07("HDS17", "2024年07月[日本][山海之日]抽奖消耗积分", 9),
    US_2024_07("HDS18", "2024年07月[美国][Prime Day]抽奖中积分", 10),
    AU_2024_07("HDS19", "2024年07月[澳洲][Prime Day]抽奖中积分", 11),
    BLACK_FRIDAY_2024("HDS20", "2024年11月[美日澳英][黑色星期五]积分兑换抽奖机会", 12),
//    CO_CREATE_2024("HDS21", "2024年11月[全球][社区共创]积分兑换抽奖机会", 13),

    M03_2025("HDS30", "2025年3月[美日澳英][M03]积分兑换抽奖机会", 14),
    APEX300_B300_2025("HDS31", "2025年4月[美澳英加][Apex300众筹专题&B300尾货营销]积分兑换抽奖机会", 15),
    APP_PD_PROMOTION_2025("HDS32", "2025年[PD Magic Cabinet]积分兑换抽奖机会", 16),
    ;

    private String code;
    private String name;
    private Integer ruleType;

    ActivitySpendCode(String code, String name, Integer ruleType) {
        this.code = code;
        this.name = name;
        this.ruleType = ruleType;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public Integer getRuleType() {
        return ruleType;
    }
}
