package net.poweroak.saas.bucksc.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Mailto <EMAIL>
 * @Create 2024/5/10 9:51
 **/
@Data
public class IntegralQueryRequest {

    @NotNull(message = "国家编码不能为空")
    @ApiModelProperty(value = "国家编码")
    private String countryCode;

    @NotNull(message = "渠道编码不能为空")
    @ApiModelProperty(value = "渠道编码")
    private String channelCode;


}
