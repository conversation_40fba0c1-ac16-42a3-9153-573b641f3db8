package net.poweroak.saas.bucksc.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * FAQ
 *
 * <AUTHOR>
 * @date 2022/6/18
 */
@Data
public class FaqRequest implements Serializable {

    @ApiModelProperty(value = "国家编码")
    @NotNull(message = "国家编码不能为空")
    private Long countryId;
}
