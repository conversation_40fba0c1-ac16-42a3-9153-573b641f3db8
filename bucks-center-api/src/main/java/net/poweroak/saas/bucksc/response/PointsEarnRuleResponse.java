package net.poweroak.saas.bucksc.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 发放规则兑换比例
 *
 * <AUTHOR>
 * @date 2022/11/18
 */
@Data
@ApiModel("发放规则兑换比例-返回信息")
public class PointsEarnRuleResponse implements Serializable {

    public PointsEarnRuleResponse() {
        this.extraReward = BigDecimal.ZERO;
        this.ratio = BigDecimal.ZERO;
    }

    @ApiModelProperty(value = "兑换比例")
    private BigDecimal ratio;


    @ApiModelProperty(value = "额外积分奖励")
    private BigDecimal extraReward;
}
