package net.poweroak.saas.bucksc.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 发放积分
 *
 * <AUTHOR>
 * @date 2022/5/31 14:09
 */
@Data
@ApiModel("预发放积分-信息")
public class PointsEarnPreResponse implements Serializable {

    @ApiModelProperty(value = "发放积分")
    private Integer points;
}
