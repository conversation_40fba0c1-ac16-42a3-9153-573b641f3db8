package net.poweroak.saas.bucksc.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 规则-等级
 *
 * <AUTHOR>
 * @date 2022/6/72 14:31
 */
@Data
@ApiModel("会员等级-api")
public class MemberLevelResponse implements Serializable {

    @ApiModelProperty(value = "等级名称")
    private String title;

    @ApiModelProperty(value = "等级名称1")
    private String memberTitle;

    @ApiModelProperty(value = "等级")
    private Integer memberLevel;

    @ApiModelProperty(value = "比例")
    private BigDecimal ratio;

    @ApiModelProperty(value = "额外")
    private BigDecimal extraReward;

    @ApiModelProperty(value = "等级需要金额")
    private BigDecimal earnLimit;

    @ApiModelProperty(value = "总积分")
    private Integer totalPoints;

    @ApiModelProperty(value = "总消费金额")
    private BigDecimal shopAmount;

    @ApiModelProperty(value = "总消耗积分")
    private Integer totalSpend;

    @ApiModelProperty(value = "余额")
    private Integer balance;

}
