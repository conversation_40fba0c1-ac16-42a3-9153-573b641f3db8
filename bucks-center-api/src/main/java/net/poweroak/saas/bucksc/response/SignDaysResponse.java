package net.poweroak.saas.bucksc.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 签到天数
 *
 * <AUTHOR>
 * @date 2022/6/2 14:09
 */
@Data
@ApiModel("签到天数-api")
public class SignDaysResponse implements Serializable {

    @ApiModelProperty(value = "日")
    private Integer day;

    @ApiModelProperty(value = "日期格式: yyyy-MM-dd")
    private String date;

}
