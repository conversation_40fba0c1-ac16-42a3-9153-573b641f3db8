package net.poweroak.saas.bucksc.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Mailto <EMAIL>
 * @Create 2024/5/11 11:53
 **/
@Data
public class ShopifyBalanceJournalResponse implements Serializable {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "活动名称")
    private String action;

    @ApiModelProperty(value = "积分")
    private Integer bucks;

    @ApiModelProperty(value = "发生时间")
    private String genTime;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "1=增加  2=减少")
    private Integer symbol;

}
