package net.poweroak.saas.bucksc.client;

import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.saas.bucksc.request.IntegralPageRequest;
import net.poweroak.saas.bucksc.request.IntegralQueryRequest;
import net.poweroak.saas.bucksc.request.IntegralSpendRequest;
import net.poweroak.saas.bucksc.response.ShopifyBalanceJournalResponse;
import net.poweroak.saas.bucksc.response.ShopifyPointsQueryResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * <AUTHOR>
 * @Mailto <EMAIL>
 * @Create 2024/5/9 15:19
 **/
@FeignClient(value = "blu-bucks-center" + "${spring.application.feignid:}", path = "/bucksApi/pointsHandle")
public interface PointsChangeClient {

    /**
     * 积分变动
     * @param integralSpendRequest
     * @return
     */
    @PostMapping("/v1/shopifyChange")
    UnifyResponse<Boolean> shopifyEarn(@RequestBody IntegralSpendRequest integralSpendRequest);


    /**
     * 查询用户积分账户信息
     * @param integralQueryRequest
     * @return
     */
    @PostMapping("/v1/shopifyAccount")
    UnifyResponse<ShopifyPointsQueryResponse> shopifyAccount(@RequestBody IntegralQueryRequest integralQueryRequest);


    /**
     * 获取积分明细分页列表
     * @param integralListRequest
     * @return
     */
    @PostMapping("/v1/shopifyPage")
    UnifyResponse<Pagination<ShopifyBalanceJournalResponse>> shopifyPage(@RequestBody IntegralPageRequest integralListRequest);


    /**
     * 获取单笔积分明细
     * @param id
     * @return
     */
    @GetMapping("/v1/shopifyDetail")
    UnifyResponse<ShopifyBalanceJournalResponse> shopifyDetail(@RequestParam("id") String id);




}
