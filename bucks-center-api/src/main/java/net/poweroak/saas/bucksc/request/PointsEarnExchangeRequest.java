package net.poweroak.saas.bucksc.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 货币兑换
 *
 * <AUTHOR>
 * @date 2022/5/11 14:09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("发放积分-货币兑换")
public class PointsEarnExchangeRequest extends PointsEarnBaseRequest {

    @ApiModelProperty(value = "消费金额")
    @NotNull(message = "消费金额不能为空")
    private BigDecimal amount;

    @ApiModelProperty(value = "累计消费金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "订单Id")
    @NotNull(message = "订单号不能为空")
    private String orderId;

}
