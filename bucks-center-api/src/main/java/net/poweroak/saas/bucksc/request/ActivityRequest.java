package net.poweroak.saas.bucksc.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * FAQ
 *
 * <AUTHOR>
 * @date 2022/6/18
 */
@Data
public class ActivityRequest implements Serializable {

    @ApiModelProperty(value = "渠道编码")
    @NotNull(message = "渠道不能为空")
    private String channelCode;

    @ApiModelProperty(value = "活动编码 发放积分： ActivityEarnCode 枚举   消耗积分：ActivitySpendCode 枚举")
    @NotNull(message = "活动编码不能为空")
    private String activityCode;

    @ApiModelProperty(value = "国家编码")
    @NotNull(message = "国家编码不能为空")
    private Long countryId;
}
