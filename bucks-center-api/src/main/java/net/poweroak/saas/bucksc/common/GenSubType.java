package net.poweroak.saas.bucksc.common;

import lombok.Getter;
import net.poweroak.framework.api.AppMessage;
import net.poweroak.framework.api.util.ApplicationUtil;
import net.poweroak.framework.app.MicroServiceApplication;
import net.poweroak.framework.i18n.Internationalization;
import net.poweroak.framework.interfaces.MessageID;
import net.poweroak.saas.bucksc.BucksCenterApp;

/**
 * <AUTHOR>
 * * @date 2022/6/17 16:28
 */
@Internationalization(app = MicroServiceApplication.bluBucksCenter)
@Getter
public enum GenSubType implements AppMessage, MessageID {


    OTHER(0, "其他", BucksCenterApp.I18N_CODE_PREFIX + "subtype.qt_earn", "181ae72b1f5f79115229d154903"),
    SIGN(1, "签到", BucksCenterApp.I18N_CODE_PREFIX + "subtype.sign_earn", "18170d3fbb6f791157c8280bc7c"),
    OR<PERSON><PERSON>(2, "购物下单", BucksCenterApp.I18N_CODE_PREFIX + "subtype.order", "181ae77099df79115229d154904"),
    PV_POWER(3, "光伏发电", BucksCenterApp.I18N_CODE_PREFIX + "subtype.pv_power", "181ae772c1cf79115229d154905"),
    COUPON(4, "卡券兑换", BucksCenterApp.I18N_CODE_PREFIX + "subtype.coupon_exchange", "181b750fe65f79115229d15492e"),
    EXCHANGE(5, "物品兑换", BucksCenterApp.I18N_CODE_PREFIX + "subtype.spend_exchange", "18170d44faef791157c8280bc80"),
    INVALID(6, "积分清零", BucksCenterApp.I18N_CODE_PREFIX + "subtype.due_clear", "18170d47b30f791157c8280bc82"),
    LOTTERY(7, "轮盘抽奖", BucksCenterApp.I18N_CODE_PREFIX + "subtype.lottery", "18170d48f30f791157c8280bc83"),
    BACK(8, "回退 - 赠送积分", BucksCenterApp.I18N_CODE_PREFIX + "subtype.return_bucks", "181b7535a73f79115229d15492f"),
    EXTRA_REWARD(9, "额外奖励", BucksCenterApp.I18N_CODE_PREFIX + "subtype.extra_reward", "18223e94273f79115229d154aa4"),
    BACK_GOODS_EXCHANGE(10, "回退物品兑换", BucksCenterApp.I18N_CODE_PREFIX + "subtype.back_goods_exchange", "18223e94273f79115229d154bb4"),
    EXCHANGE_ORDER_REGISTRATION(11, "订单兑换登记", BucksCenterApp.I18N_CODE_PREFIX + "subtype.exchange_order_registration", "18ba9158303f79115700ffbf6ad"),
    POST_LIFE(12, "发帖", BucksCenterApp.I18N_CODE_PREFIX + "subtype.post_life_send_points", "18d3031f885f791151b32501773"),
    RECOMMEND_PLAN(13, "推荐计划", BucksCenterApp.I18N_CODE_PREFIX + "subtype.recommend_plan_send_points", "18dc97990a3f791151b32501809"),
    POINTS_LEVEL(14, "积分等级升级赠送", BucksCenterApp.I18N_CODE_PREFIX + "subtype.points_level_send_points", "18ff20f3e98bc91b2060c9d8d2f"),
    APP_SUBSCRIPTION(15, "app首次订阅", BucksCenterApp.I18N_CODE_PREFIX + "subtype.app_subscription", "197a1748d32bc91b27a6edcb39d"),

    /**
     * 小程序积分类型
     */
    APPLET_DEFAULT(100, "默认", BucksCenterApp.I18N_CODE_PREFIX + "subtype.applet_default", "1828bd06699f79115229d154c11"),
    ACTIVITY_CLOCK(101, "活动打卡", BucksCenterApp.I18N_CODE_PREFIX + "subtype.activity_clock", "1828bd079eaf79115229d154c12"),
    ACTIVITY_CLOCK_PIC(102, "打卡图片分", BucksCenterApp.I18N_CODE_PREFIX + "subtype.activity_clock_pic", "1828bd08a1bf79115229d154c13"),
    ACTIVITY_CLOCK_EGG(103, "打卡彩蛋分", BucksCenterApp.I18N_CODE_PREFIX + "subtype.activity_clock_egg", "1828bd0978af79115229d154c14"),
    FOLLOW(104, "关注", BucksCenterApp.I18N_CODE_PREFIX + "subtype.follow", "1828bd0a47cf79115229d154c15"),
    SHARE(105, "分享", BucksCenterApp.I18N_CODE_PREFIX + "subtype.share", "1828bd0b489f79115229d154c16"),
    INVITE(106, "邀请", BucksCenterApp.I18N_CODE_PREFIX + "subtype.invite", "1828bd0c1d5f79115229d154c17"),
    APPLET_SIGN(107, "签到", BucksCenterApp.I18N_CODE_PREFIX + "subtype.applet_sign", "1828bd0d1a4f79115229d154c18"),
    LEASE(108, "租赁", BucksCenterApp.I18N_CODE_PREFIX + "subtype.lease", "1828bd0e119f79115229d154c19");

    private Integer statusCode;
    private int code;
    private String messageZhCn;
    private String i18nCode;
    private String messageId;

    private static final int START_ERROR_CODE = 20112120;


    GenSubType(Integer statusCode, String messageZhCn, String i18nCode, String messageId) {
        this.statusCode = statusCode;
        this.messageZhCn = messageZhCn;
        this.messageId = messageId;
        this.i18nCode = i18nCode;
    }


    public static GenSubType match(Integer status) {
        for (GenSubType genSubType : GenSubType.values()) {
            if (genSubType.statusCode.equals(status)) {
                return genSubType;
            }
        }
        return GenSubType.OTHER;
    }

    /**
     * 获取消息错误码
     *
     * @return
     */
    public int getCode() {
        this.code = this.ordinal() + START_ERROR_CODE;
        return this.code;
    }

    /**
     * 获取消息的国际化Code
     *
     * @return
     */
    @Override
    public String getI18nCode() {
        return this.i18nCode;
    }

    /**
     * 获取消息的默认本地化文本消息
     *
     * @return
     */
    @Override
    public String getMessage() {
        return this.messageZhCn;
    }

    /**
     * 获取应用服务标识
     *
     * @return
     */
    @Override
    public String getAppId() {
        return ApplicationUtil.getApplicationNameReal();
    }

    @Override
    public String getMsgId() {
        return this.getMessageId();
    }

    @Override
    public int getMsgCode() {
        return this.getCode();
    }

    /**
     * 获取消息定义的全局唯一ID
     *
     * @return
     */
    public String getMessageId() {
        return this.messageId;
    }
}
