package net.poweroak.saas.bucksc.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.page.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 基础
 *
 * <AUTHOR>
 * @date 2022/5/11 14:09
 */
@Data
public class PointsQueryPageRequest extends PageInfo implements Serializable {

    @ApiModelProperty(value = "用户ID")
    private String uid;

    @ApiModelProperty(value = "国家编码")
    @NotNull(message = "所属国家区域不能为空")
    private Long countryId;

    @ApiModelProperty(value = "渠道编码")
    private String channelCode;
}
