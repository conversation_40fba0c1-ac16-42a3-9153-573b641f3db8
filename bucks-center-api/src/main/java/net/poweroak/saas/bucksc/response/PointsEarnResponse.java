package net.poweroak.saas.bucksc.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 发放积分
 *
 * <AUTHOR>
 * @date 2022/5/11 14:09
 */
@Data
@ApiModel("发放积分-返回信息")
public class PointsEarnResponse implements Serializable {

    public PointsEarnResponse() {
        this.points = 0;
        this.balance = 0;
        this.ratio = BigDecimal.ZERO;
    }

    @ApiModelProperty(value = "流水ID")
    private String journalId;

    @ApiModelProperty(value = "兑换比例")
    private BigDecimal ratio;

    @ApiModelProperty(value = "发放积分")
    private Integer points;

    @ApiModelProperty(value = "用户积分余额")
    private Integer balance = 0;

    private String date;
}
