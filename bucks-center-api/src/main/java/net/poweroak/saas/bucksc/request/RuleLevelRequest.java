package net.poweroak.saas.bucksc.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * FAQ
 *
 * <AUTHOR>
 * @date 2022/6/18
 */
@Data
public class RuleLevelRequest implements Serializable {

    @ApiModelProperty(value = "国家编码")
    @NotNull(message = "所属国家区域不能为空")
    private Long countryId;

    @ApiModelProperty(value = "渠道编码")
    @NotBlank(message = "所属国家区域不能为空")
    private String channelCode;

    @ApiModelProperty(value = "活动编码")
    @NotBlank(message = "所属国家区域不能为空")
    private String activityCode;
}
