package net.poweroak.saas.bucksc.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 签到返回信息
 *
 * <AUTHOR>
 * @date 2022/6/6
 */
@Data
@ApiModel("发放积分-签到-返回信息")
public class PointsEarnSignResponse implements Serializable {

//    public PointsEarnSignResponse(){
//        this.points  = 0;
//        this.signDays = 0;
//    }

//    @ApiModelProperty(value = "发放积分")
//    private Integer points;
//
//    @ApiModelProperty(value = "连续签到天数")
//    private Integer signDays;

    @ApiModelProperty(value = "签到状态0= 签到失败 1=签到成功")
    private Integer status;
}
