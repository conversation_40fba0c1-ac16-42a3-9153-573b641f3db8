package net.poweroak.saas.bucksc.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by zx on 2022/7/20 15:32
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LotteryRuleResponse {

    @ApiModelProperty(value = "积分")
    private Integer points;

    @ApiModelProperty(value = "是否黑名单")
    private Boolean isBlack;
}
