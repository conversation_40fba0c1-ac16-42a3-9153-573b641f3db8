package net.poweroak.saas.bucksc;

import net.poweroak.PropertyPrefix;
import net.poweroak.framework.app.MicroServiceApplication;

/**
 * Bluetti SaaS 积分中心服务名称定义
 *
 * <AUTHOR>
 * @Mailto <EMAIL>
 * @Create 2022-05-04 17:20:51
 */
public class BucksCenterApp {
    /**
     * Bluetti SaaS 积分中心微服务的服务名
     */
    public static final String SERVICE_NAME = MicroServiceApplication.bluBucksCenter.getAppKey();
    /**
     * Bluetti SaaS 积分中心微服务的服务标签<br/>
     * 查找服务时请使用`SERVICE_NAME`变量
     */
    public static final String SERVICE_TAG = MicroServiceApplication.bluBucksCenter.getAppTag();
    /**
     * Bluetti SaaS 积分中心微服务的自定义配置前缀
     */
    public static final String APP_PROPS_ROOT_PREFIX = PropertyPrefix.PROPERTY_PREFIX_OF_SAAS + "." + SERVICE_TAG;
    /**
     * Bluetti SaaS 积分中心微服务的国际化Code前缀
     */
    public static final String I18N_CODE_PREFIX = PropertyPrefix.I18N_CODE_PREFIX + MicroServiceApplication.bluBucksCenter.getAppKey().toLowerCase() + ".";
}
