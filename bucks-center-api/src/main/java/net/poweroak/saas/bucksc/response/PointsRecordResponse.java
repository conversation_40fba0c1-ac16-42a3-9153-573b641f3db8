package net.poweroak.saas.bucksc.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.page.Pagination;

/**
 * Created by zx on 2022/3/9 10:39
 */
@Data
public class PointsRecordResponse {

    @ApiModelProperty(value = "我的积分")
    private Long integral ;

    @ApiModelProperty(value = "活动积分")
    private Long activityIntegral;

    @ApiModelProperty(value = "租赁积分")
    private Long leaseIntegral;

    @ApiModelProperty(value = "签到积分")
    private Long  signIntegral;

    @ApiModelProperty(value = "消费积分")
    private Long consumptionIntegral;

    @ApiModelProperty(value = "积分明细分页")
    private Pagination<PointsDetailResponse> integralDetailPage;

}
