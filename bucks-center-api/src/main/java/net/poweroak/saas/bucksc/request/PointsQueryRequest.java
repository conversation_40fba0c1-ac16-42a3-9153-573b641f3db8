package net.poweroak.saas.bucksc.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.page.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 基础
 *
 * <AUTHOR>
 * @date 2022/5/11 14:09
 */
@Data
public class PointsQueryRequest implements Serializable {

    @ApiModelProperty(value = "用户ID")
    private String uid;

    @ApiModelProperty(value = "国家编码")
    private Long countryId;

    @ApiModelProperty(value = "渠道编码")
    private String channelCode;

    @ApiModelProperty(value = "排除同个国家其他店铺的积分 0=否 1=是(渠道编码必须存在并且是渠道类型是店铺)")
    private Integer excludeOtherShops;

    public PointsQueryRequest() {
        this.excludeOtherShops = 0;
    }
}
