package net.poweroak.saas.bucksc.client;

import io.swagger.annotations.ApiOperation;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.saas.bucksc.request.PointsChannelRequest;
import net.poweroak.saas.bucksc.request.PointsEarnSignRequest;
import net.poweroak.saas.bucksc.response.PointsEarnSignResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/7/25 11:35
 * @description
 */
@FeignClient(value = "blu-bucks-center" + "${spring.application.feignid:}", path = "/bucksApi/PointsChannel")
public interface PointsChannelClient {

    /**
     * 签到
     *
     * @param request
     * @return
     */
    @PostMapping("/v1/add")
    @ApiOperation("注册渠道")
    UnifyResponse<Boolean> add(@RequestBody PointsChannelRequest request);
}
