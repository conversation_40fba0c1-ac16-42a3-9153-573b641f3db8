package net.poweroak.saas.bucksc.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.page.Pagination;

import java.util.Date;

/**
 * Created by zx on 2022/3/9 15:01
 */
@Data
public class ActivityResponse {


    @ApiModelProperty(value = "渠道标识")
    private String channelCode;

    @ApiModelProperty(value = "国家Id")
    private Long countryId;

    @ApiModelProperty(value = "活动名称")
    private String name;

    @ApiModelProperty(value = "活动编码")
    private String code;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date beginTime;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date endTime;
}
