package net.poweroak.saas.bucksc.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * Created by zx on 2022/7/20 15:33
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LotteryRuleRequest {

    @ApiModelProperty(value = "用户ID")
    private String uid;

    @ApiModelProperty(value = "渠道标识")
    @NotBlank(message = "渠道标识不能为空")
    private String channelCode;

    @ApiModelProperty(value = "活动编码")
    @NotBlank(message = "活动编码不能为空")
    private String activityCode;

    @ApiModelProperty(value = "国家编码")
    @NotNull(message = "国家编码不能为空")
    private Long countryId;

}
