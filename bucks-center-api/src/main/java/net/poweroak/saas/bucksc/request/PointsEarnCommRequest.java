package net.poweroak.saas.bucksc.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.poweroak.saas.bucksc.common.ActivityEarnCode;
import net.poweroak.saas.bucksc.common.GenSubType;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 通用
 *
 * <AUTHOR>
 * @date 2022/5/11 14:09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("发放积分-通用")
public class PointsEarnCommRequest extends PointsEarnBaseRequest {
    public PointsEarnCommRequest() {
        this.earnLimit = BigDecimal.ZERO;
        this.points = 0;
    }

    @ApiModelProperty(value = "门槛 如果没有门槛，不填写")
    private BigDecimal earnLimit;


    @ApiModelProperty(value = "积分,传了按传的参数发放积分，没传用配置默认的发放")
    private Integer points;

    @ApiModelProperty("活动类型，不填默认OTHER； 从枚举：GenSubType 取值")
    private Integer genSubTypeCode;
}
