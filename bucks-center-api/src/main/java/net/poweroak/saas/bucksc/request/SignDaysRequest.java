package net.poweroak.saas.bucksc.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.page.PageInfo;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 签到天数
 *
 * <AUTHOR>
 * @date 2022/6/2
 */
@Data
public class SignDaysRequest extends PointsQueryRequest implements Serializable {

    @ApiModelProperty(value = "月份，格式：yyyy-MM-dd")
    @NotBlank(message = "签到日期不能为空")
    private String date;
}
