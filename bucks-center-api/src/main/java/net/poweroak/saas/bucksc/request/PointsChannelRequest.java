package net.poweroak.saas.bucksc.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/7/25 11:58
 * @description
 */
@Data
public class PointsChannelRequest {

    /**
     * 渠道名称
     */
    @ApiModelProperty(value = "渠道名称")
    private String name;

    /**
     * 渠道标识
     */
    @ApiModelProperty(value = "渠道标识")
    private String code;




}
