package net.poweroak.saas.bucksc.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Mailto <EMAIL>
 * @Create 2024/5/10 9:54
 **/
@Data
public class ShopifyPointsQueryResponse implements Serializable {

    @ApiModelProperty(value = "余额")
    private Integer bucks;

    @ApiModelProperty(value = "发放总积分")
    private Integer earnBucks;

    @ApiModelProperty(value = "消耗总积分")
    private Integer spendBucks;

    @ApiModelProperty(value = "即将过期积分")
    private Integer validBucks;

}
