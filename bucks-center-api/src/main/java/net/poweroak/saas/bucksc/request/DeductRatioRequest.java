package net.poweroak.saas.bucksc.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * Created by zx on 2022/5/13 12:07
 */
@Data
public class DeductRatioRequest {

   @ApiModelProperty(value = "抵扣积分")
   @NotNull(message = "抵扣积分不能为空")
   private Integer deductRatioPoints;

   @ApiModelProperty(value = "渠道标识")
   private String channelCode;

   @ApiModelProperty(value = "活动ID")
   private String activityId;

   @ApiModelProperty(value = "活动ID")
   private String activityCode;

   @ApiModelProperty(value = "国家编码")
   @NotNull(message = "所属国家区域不能为空")
   private Long countryId;

}
