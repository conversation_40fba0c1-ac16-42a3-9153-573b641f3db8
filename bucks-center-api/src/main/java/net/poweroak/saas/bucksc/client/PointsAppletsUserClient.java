package net.poweroak.saas.bucksc.client;

import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.saas.bucksc.request.PointsChangeRequest;
import net.poweroak.saas.bucksc.request.PointsRecordPageRequest;
import net.poweroak.saas.bucksc.request.TotalPointsRequest;
import net.poweroak.saas.bucksc.response.ActivityPointsRecordResponse;
import net.poweroak.saas.bucksc.response.PointsRecordResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


/**
 * Created by zx on 2022/3/8 16:54 用户小程序相关积分
 */

@FeignClient(value = "blu-bucks-center" + "${spring.application.feignid:}", path = "/bucksApi/pointsApplets")
public interface PointsAppletsUserClient {

    /**
     * @return 积分变动
     */
    @PostMapping("/v1/pointsChange")
    UnifyResponse<Boolean> pointsChange(@RequestBody List<PointsChangeRequest> changeTOS);

    /**
     * @return 根据类型查询不同类型总积分
     */
    @PostMapping("/v1/totalPointsByType")
    UnifyResponse<Long> totalPointsByType(@RequestBody TotalPointsRequest totalPointsRequest);


    /**
     *   我的积分(活动积分、租赁积分、消费积分)、积分明细分页
     * @return
     */
    @PostMapping("/v1/pointsRecord")
    UnifyResponse<PointsRecordResponse> pointsRecord(@RequestBody PointsRecordPageRequest recordPageTO);

    /**
     *  活动积分(打卡积分、互动积分、老带新积分)、积分明细分页
     * @return
     */
    @PostMapping("/v1/activityPointsRecord")
    UnifyResponse<ActivityPointsRecordResponse> activityPointsRecord(@RequestBody PointsRecordPageRequest recordPageTO);

}
