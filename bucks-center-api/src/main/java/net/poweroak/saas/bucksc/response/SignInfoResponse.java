package net.poweroak.saas.bucksc.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 签到信息
 *
 * <AUTHOR>
 * @date 2022/6/2 14:09
 */
@Data
@ApiModel("签到信息-api")
public class SignInfoResponse implements Serializable {

    public SignInfoResponse() {
        this.todaySignStatus = 0;
        this.signPoints = 0;
        this.extraPoints = 0;
        this.continueDays = 0;
        this.days = new ArrayList<>(1);
    }

    @ApiModelProperty(value = "今天签到状态 0=未签到 1=已签到")
    private Integer todaySignStatus;

    @ApiModelProperty(value = "签到获得额外积分")
    private Integer extraPoints;

    @ApiModelProperty(value = "签到获得基础多少积分")
    private Integer signPoints;

    @ApiModelProperty(value = "连续签到天数")
    private Integer continueDays;

    @ApiModelProperty(value = "提示状态 1=展示今天签到获得的额外积分 2=展示下次获得积分 3=什么都不展示")
    private Integer tipStatus;

    @ApiModelProperty(value = "当前是一周中的第几天")
    private Integer currentDay;

    @ApiModelProperty(value = "日期节点")
    private List<Day> days;


    @Data
    @AllArgsConstructor
    public static class Day {
        public Day() {
        }

        @ApiModelProperty(value = "一周第几天")
        private Integer no;

        @ApiModelProperty(value = "周几")
        private String name;

        @ApiModelProperty(value = "积分")
        private Integer points;

        @ApiModelProperty(value = "签到状态 0=未签到 1=已签到")
        private Integer signStatus;

        @ApiModelProperty(value = "是否有额外 0=否 1=有")
        private Integer extraStatus;
    }
}
