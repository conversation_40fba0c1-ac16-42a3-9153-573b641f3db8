package net.poweroak.saas.bucksc.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 签到
 *
 * <AUTHOR>
 * @date 2022/5/11 14:09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("发放积分-签到")
public class PointsEarnSignRequest extends PointsEarnBaseRequest {

}
