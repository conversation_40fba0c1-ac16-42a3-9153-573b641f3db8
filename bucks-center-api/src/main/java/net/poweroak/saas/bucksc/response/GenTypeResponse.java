package net.poweroak.saas.bucksc.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 发放积分
 * <AUTHOR>
 * @date 2022/5/11 14:09
 */
@Data
@ApiModel("产生类型-api")
public class GenTypeResponse implements Serializable {

    @ApiModelProperty(value = "产生类型ID")
    private Integer genType;

    @ApiModelProperty(value = "产生类型名称")
    private String genTypeName;
}
