package net.poweroak.saas.bucksc.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 基础
 *
 * <AUTHOR>
 * @date 2022/5/11 14:09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointsQueryJournalRequest extends PointsQueryPageRequest implements Serializable {

    public PointsQueryJournalRequest() {
        this.genType = 0;
        this.isOnlySelfChannel = 0;
    }

    @ApiModelProperty(value = "产生类型(0或者null-全部 1-增加；2-消耗；3-回退；4-回收清零)")
    private Integer genType;

    @ApiModelProperty(value = "细分类型0=其他 1=签到 2=订单 3=光伏发电 4=优惠券兑换 5=物品兑换 6=积分清零 7=大转盘 8=回退 9额外  小程序积分类型（101-活动打卡 102-打卡图片分  103-打卡彩蛋分 104-关注 105-分享 106-邀请 107-签到 108-租赁） ")
    private Integer subType;

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "orderId")
    private String orderId;

    @ApiModelProperty(value = "渠道分类 0=所有渠道 1=本渠道 2=本渠道+公共")
    private Integer isOnlySelfChannel;

    @ApiModelProperty(value = "语言")
    private String lang;

    @ApiModelProperty(value = "开始时间 yyyy-MM-dd hh:mm:ss")
    private String beginTime;

    @ApiModelProperty(value = "结束时间  yyyy-MM-dd hh:mm:ss")
    private String endTime;

    @ApiModelProperty(value = "活动Id")
    private String activityId;

    @ApiModelProperty(value = "orderNo")
    private String orderNo;
}
