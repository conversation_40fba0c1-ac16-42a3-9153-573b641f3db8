package net.poweroak.saas.bucksc.client;

import io.swagger.annotations.ApiOperation;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.saas.bucksc.request.PointsQueryUserRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2022/5/11 14:09
 */
@ApiOperation("积分用户-api")
@FeignClient(value = "blu-bucks-center" + "${spring.application.feignid:}", path = "/bucksApi/user")
public interface PointsUserClient {

    /**
     * 积分明细查询接口
     * @param request
     * @return
     */
    @PostMapping("/reg")
    @ApiOperation("积分用户注册")
    UnifyResponse<Boolean> reg(@RequestBody PointsQueryUserRequest request);

}
