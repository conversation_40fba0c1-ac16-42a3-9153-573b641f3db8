package net.poweroak.saas.bucksc.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 基础
 *
 * <AUTHOR>
 * @date 2022/5/11 14:09
 */
@Data
public class PointsEarnBaseRequest implements Serializable {

    @ApiModelProperty(value = "用户ID")
    private String uid;

    @ApiModelProperty(value = "渠道标识 零售商城为店铺ID，APP和其他服务为应用管理对应的APPKEY")
    private String channelCode;

    @ApiModelProperty(value = "活动编码 可不传,通用必须传")
    private String activityCode;

    @ApiModelProperty(value = "国家ID")
    private Long countryId;

}
