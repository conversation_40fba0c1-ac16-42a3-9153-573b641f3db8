package net.poweroak.saas.bucksc.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Created by zx on 2022/5/30 10:09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PointsSpendResponse {

    @ApiModelProperty(value = "流水ID")
    private String journalId;

    @ApiModelProperty(value = "积分抵扣返回货币值")
    private BigDecimal ratioValue;
}
