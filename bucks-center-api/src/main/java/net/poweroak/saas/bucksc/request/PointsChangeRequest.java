package net.poweroak.saas.bucksc.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * Created by zx on 2022/3/8 17:25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PointsChangeRequest {


   @ApiModelProperty(value = "来源")
   private Integer resourceType;

   @ApiModelProperty(value = "积分类型")
   private  Integer integralType;

   @ApiModelProperty(value = "积分类型说明")
   private String integralTypeDesc;

   @ApiModelProperty(value = "分值")
   private Integer score;

   @ApiModelProperty(value = "积分具体说明")
   private String desc;

   @ApiModelProperty(value = "积分加减(true-加，false-减)")
   private Boolean flag;

   @ApiModelProperty(value = "用户ID")
   private String userId;

   @ApiModelProperty(value = "渠道标识")
   private String channelCode;

   @ApiModelProperty(value = "活动编码")
   private String activityCode;

   @ApiModelProperty(value = "国家编码")
   @NotNull(message = "所属国家区域不能为空")
   private Long countryId;

}
