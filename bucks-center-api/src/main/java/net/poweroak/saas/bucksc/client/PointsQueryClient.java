package net.poweroak.saas.bucksc.client;

import io.swagger.annotations.ApiOperation;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.saas.bucksc.request.*;
import net.poweroak.saas.bucksc.response.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2022/5/11 14:09
 */
@ApiOperation("积分询接口-api")
@FeignClient(value = "blu-bucks-center" + "${spring.application.feignid:}", path = "/bucksApi/query")
public interface PointsQueryClient {


    /**
     * 积分汇总查询接口
     *
     * @param request
     * @return
     */
    @PostMapping("/v1/summary")
    @ApiOperation("积分汇总查询接口")
    UnifyResponse<PointsBalanceTotalResponse> summary(@RequestBody @Valid PointsQueryRequest request);


    /**
     * 获取活动
     *
     * @param request
     * @return
     */
    @PostMapping("/v1/getActivity")
    @ApiOperation("获取活动")
    UnifyResponse<ActivityResponse> getActivity(@RequestBody @Valid ActivityRequest request);


    /**
     * 根据用户消费金额查相应的等级
     *
     * @param request
     * @return
     */
    @PostMapping("/v1/getEarnRule")
    @ApiOperation("根据用户消费金额查相应的等级")
    UnifyResponse<PointsEarnRuleResponse> getEarnRule(@RequestBody @Valid PointsEarnRuleQueryRequest request);

    /**
     * 积分明细查询接口
     *
     * @param request
     * @return
     */
    @PostMapping("/v1/journal")
    @ApiOperation("积分明细查询接口")
    UnifyResponse<Pagination<PointsBalanceJournalResponse>> journal(@RequestBody @Valid PointsQueryJournalRequest request);


    @PostMapping("/deductRatio")
    @ApiOperation("积分抵扣比例查询接口")
    UnifyResponse<PointsSpendResponse> deductRatio(@RequestBody DeductRatioRequest request);

    /**
     * 产生类型列表
     *
     * @param lang
     * @return
     */
    @GetMapping("/genTypeList")
    @ApiOperation("产生类型列表")
    UnifyResponse<List<GenTypeResponse>> genTypeList(@RequestParam("lang") String lang);


    /**
     * 签到天数
     *
     * @param request
     * @return
     */
//    @PostMapping("/v1/signDays")
//    @ApiOperation("签到天数")
//    UnifyResponse<List<SignDaysResponse>> signDays(@RequestBody SignDaysRequest request);


    /**
     * 签到信息
     *
     * @param request
     * @return
     */
    @PostMapping("/v1/signInfo")
    @ApiOperation("签到信息")
    UnifyResponse<SignInfoResponse> signInfo(@RequestBody SignInfoRequest request);

    /**
     * 签到记录
     *
     * @param request
     * @return
     */
    @PostMapping("/v1/signRecord")
    @ApiOperation("签到记录")
    UnifyResponse<Pagination<PointsBalanceJournalResponse>> signRecord(@RequestBody @Valid SignRecordRequest request);


    /**
     * 积分规则
     *
     * @return
     */
    @PostMapping("/v1/rules")
    @ApiOperation("积分规则")
    UnifyResponse<List<RulesResponse>> rules(FaqRequest faqRequest);


    /*   *//**
     * 文案  文案编码和活动编码二选一必须填写其中一个
     *
     * @param documentRequest
     * @return
     *//*
    @PostMapping("/v1/document")
    @ApiOperation("文案")
    UnifyResponse<DocumentResponse> document(@RequestBody DocumentRequest documentRequest, @RequestHeader("Authorization") HttpServletRequest req);*/

}
