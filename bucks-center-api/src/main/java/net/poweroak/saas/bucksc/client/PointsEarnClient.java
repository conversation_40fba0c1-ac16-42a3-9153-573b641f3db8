package net.poweroak.saas.bucksc.client;

import io.swagger.annotations.ApiOperation;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.saas.bucksc.request.*;
import net.poweroak.saas.bucksc.response.PointsEarnPreResponse;
import net.poweroak.saas.bucksc.response.PointsEarnResponse;
import net.poweroak.saas.bucksc.response.PointsEarnSignResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * <AUTHOR>
 * @date 2022/5/11 14:09
 */
@FeignClient(value = "blu-bucks-center" + "${spring.application.feignid:}", path = "/bucksApi/pointsEarn")
public interface PointsEarnClient {


    /**
     * 签到
     *
     * @param request
     * @return
     */
    @PostMapping("/v1/sign")
    @ApiOperation("签到")
    UnifyResponse<PointsEarnSignResponse> sign(@RequestBody PointsEarnSignRequest request);


    /**
     * 光伏发电
     *
     * @param request
     * @return
     */
    @PostMapping("/v1/pvPower")
    @ApiOperation("光伏发电")
    UnifyResponse<PointsEarnResponse> pvPower(@RequestBody PointsEarnPvPowerRequest request);

    /**
     * 购物下单
     *
     * @param request
     * @return
     */
    @PostMapping("/v1/exchange")
    @ApiOperation("购物下单")
    UnifyResponse<PointsEarnResponse> exchange(@RequestBody PointsEarnExchangeRequest request);


    /**
     * 货币兑换预先查询
     *
     * @param request
     * @return
     */
    @PostMapping("/v1/preExchange")
    @ApiOperation("预货币兑换信息(下单)")
    UnifyResponse<PointsEarnResponse> preExchange(@RequestBody PointsEarnExchangeRequest request);

    /**
     * 抽奖增加积分
     *
     * @param request
     * @return
     */
    @PostMapping("/v1/lottery")
    @ApiOperation("抽奖增加积分")
    UnifyResponse<PointsEarnResponse> lottery(@RequestBody PointsEarnCommRequest request);


    /**
     * 发life
     *
     * @param request
     * @return
     */
    @PostMapping("/v1/postLife")
    @ApiOperation("发life")
    UnifyResponse<PointsEarnResponse> postLife(@RequestBody PointsEarnCommRequest request);


    /**
     * 通用
     *
     * @param request
     * @return
     */
    @PostMapping("/v1/common")
    @ApiOperation("其他")
    UnifyResponse<PointsEarnResponse> common(@RequestBody PointsEarnCommRequest request);

}
