package net.poweroak.saas.bucksc.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.page.PageInfo;

import javax.validation.constraints.NotNull;

/**
 * Created by zx on 2022/3/9 11:55
 */
@Data
public class PointsRecordPageRequest extends PageInfo {

    @ApiModelProperty(value = "渠道标识")
    private String channelCode;

    @ApiModelProperty(value = "活动编码")
    private String activityCode;

    @ApiModelProperty(value = "国家编码")
    @NotNull(message = "所属国家区域不能为空")
    private Long countryId;

    @ApiModelProperty(value = "赛季开始时间")
    private String startTime;

    @ApiModelProperty(value = "赛季结束时间")
    private String endTime;

}
