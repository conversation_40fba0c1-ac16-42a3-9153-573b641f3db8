package net.poweroak.saas.bucksc.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 2022/5/7 9:15
 */
@Data
@ApiModel("积分明细查询接口-api")
public class PointsBalanceJournalResponse implements Serializable {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "发生时间")
    private String genTime;

    @ApiModelProperty(value = "产生类型 1-增加；2-消耗；3-回退；4-回收清零")
    private Integer genType;

    @ApiModelProperty(value = "产生类型名称")
    private String genTypeName;

    @ApiModelProperty(value = "0=其他 1=签到 2=订单 3=光伏发电 4=优惠券兑换 5=物品兑换 6=积分清零 7=大转盘 8=回退")
    private Integer subType;

    @ApiModelProperty(value = "细分类型名称")
    private String subTypeName;

    @ApiModelProperty(value = "图标")
    private String icon;

    @ApiModelProperty(value = "1=增加  2=减少")
    private Integer symbol;

    @ApiModelProperty(value = "积分")
    private Integer balance;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "失效时间")
    private String invalidTime;

    @ApiModelProperty(value = "订单ID")
    private String orderId;

    @ApiModelProperty(value = "渠道")
    private String channelCode;

    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    @ApiModelProperty(value = "比例")
    private String ratio;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;
}
