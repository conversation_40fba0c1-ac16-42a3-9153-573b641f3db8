package net.poweroak.saas.bucksc.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Mailto <EMAIL>
 * @Create 2024/5/15 19:51
 **/
@Data
public class ShopifyPointsResponse implements Serializable {

    @ApiModelProperty(value = "是否成功")
    private Boolean isSuccess;

    @ApiModelProperty(value = "错误消息")
    private String errorMsg;

    @ApiModelProperty(value = "积分发放返回")
    private PointsEarnResponse pointsEarnResponse;

    @ApiModelProperty(value = "积分消耗返回")
    private PointsSpendResponse pointsSpendResponse;

}
