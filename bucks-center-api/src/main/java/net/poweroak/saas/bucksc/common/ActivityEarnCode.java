package net.poweroak.saas.bucksc.common;

import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date 2022/6/11 11:45
 */
@ApiOperation("积分获取活动编码")
public enum ActivityEarnCode {
    SIGN("HDE01", "签到", 1, "sign"),
    CURRENCY_EXCHANGE("HDE02", "购物下单", 2, "exchange"),
    PV_POWER("HDE03", "光伏兑换", 3, "pvPower"),
    NW("HDE04", "原生轮盘抽奖", 4, "common"),
    APPLETS("HDE05", "铂陆帝小程序", 5, "applets"),
    REGISTRATION("HDE06", "订单兑换登记", 6, "registration"),
    POST_LIFE("HDE07", "社区发表Life", 7, "common"),
    VW("HDE08", "情人节轮盘抽奖", 8, "common"),
    COMMON("HDE99", "其他", 9, "common"),
    RECOMMEND_PLAN("HDE09", "推荐计划", 10, "common"),
    SC("HDE10", "春季轮盘抽奖", 11, "common"),
    SC_GIFT("HDE11", "春季轮盘抽奖首次赠送积分", 12, "common"),
    HAPPY_APRIL("HDE12", "开心四月轮盘抽奖", 13, "common"),
    HAPPY_APRIL_GIFT("HDE13", "开心四月轮盘活动首次赠送积分", 14, "common"),
    UAJ_APRIL_GIFT("HDE14", "美澳日轮盘抽奖", 15, "common"),
    UAJ_PRESENT_GIFT("HDE15", "美澳日轮盘抽奖首次赠送积分", 17, "common"),
    POINTS_LEVEL_GIFT("HDE16", "积分等级升级赠送积分", 18, "common"),

    JP_2024_07("HDE17", "2024年07月日本山海活动送300积分", 19, "common"),
    JP_2024_07_01("HDE20", "2024年07月日本山海活动抽奖得积分", 20, "common"),

    US_2024_07("HDE18", "美国2024年07月[Prime Day]评分送200积分", 21, "common"),
    US_2024_07_01("HDE21", "美国2024年07月[Prime Day]抽奖中积分", 23, "common"),

    AU_2024_07("HDE19", "澳大利亚2024年07月[Prime Day]抽奖中积分", 22, "common"),
    US_2024_08_29("HDS22", "2024年09月[美国H5]抽奖中积分", 24, "common"),
    APP_NEW_USER("HDS23", "app新注册用户活动大转盘", 25, "common"),

    BLACK_FRIDAY_2024("HDE24", "[2024年][黑色星期五]抽奖中积分", 26, "common"),
    SHOPIFY_ACTIVITY("HDE999", "SHOPIFY积分变化", 16, "common"),

    CO_CREATE_2024("HDE25", "共创大转盘", 27, "common"),

    M03_2025_GIFT("HDE26", "2025年3月[美日澳英][M03]活动送500积分", 28, "common"),
    M03_2025_DRAW("HDE27", "2025年3月[美日澳英][M03]抽奖中积分", 29, "common"),

    CO_CREATE_2025("HDE28", "共创提交投票送积分", 30, "common"),
    APEX300_B300_2025_DRAW("HDE29", "2025年4月[美澳英加][Apex300众筹专题&B300尾货营销]抽奖中积分", 31, "common"),
    EOFY_SALES_2025_DRAW("HDE30", "2025年5月[澳][AU-APP EOFY SALES 需求]抽奖中积分", 32, "common"),
    APP_PD_PROMOTION_2025_DRAW("HDE31", "2025年[PD Magic Cabinet 需求]抽奖中积分", 33, "common"),

    APP_KLAVIYO_SUBSCRIBE("HDE32", "app用户Klaviyo订阅送积分", 34, "common"),
    ;

    private String code;
    private String name;
    private Integer ruleType;
    private String model;

    ActivityEarnCode(String code, String name, Integer ruleType, String model) {
        this.code = code;
        this.name = name;
        this.ruleType = ruleType;
        this.model = model;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public Integer getRuleType() {
        return ruleType;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public static String getNameByRule(Integer ruleType) {
        ActivityEarnCode[] activityEarnCodes = ActivityEarnCode.values();
        for (ActivityEarnCode earnRuleType : activityEarnCodes) {
            if (earnRuleType.getRuleType().equals(ruleType)) {
                return earnRuleType.name;
            }
        }
        return "";
    }

    public static ActivityEarnCode get(Integer ruleType) {
        ActivityEarnCode[] activityEarnCodes = ActivityEarnCode.values();
        for (ActivityEarnCode activityEarnCode : activityEarnCodes) {
            if (activityEarnCode.getRuleType().equals(ruleType)) {
                return activityEarnCode;
            }
        }
        return null;
    }

    public static ActivityEarnCode getByCode(String code) {
        ActivityEarnCode[] activityEarnCodes = ActivityEarnCode.values();
        for (ActivityEarnCode activityEarnCode : activityEarnCodes) {
            if (activityEarnCode.getCode().equals(code)) {
                return activityEarnCode;
            }
        }
        return null;
    }

}
