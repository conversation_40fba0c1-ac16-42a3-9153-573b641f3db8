package net.poweroak.saas.bucksc.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Created by zx on 2022/3/9 11:34
 */
@Data
public class PointsDetailResponse {

    @ApiModelProperty(value = "积分具体说明")
    private String desc;

    @ApiModelProperty(value = "积分类型说明")
    private String integralTypeDesc;

    @ApiModelProperty(value = "分值")
    private Long score;

    @ApiModelProperty(value = "积分加减(true-加，false-减)")
    private Boolean flag;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date creTime;

}
