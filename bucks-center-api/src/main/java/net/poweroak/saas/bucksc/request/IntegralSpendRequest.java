package net.poweroak.saas.bucksc.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Mailto <EMAIL>
 * @Create 2024/5/14 15:33
 **/
@Data
public class IntegralSpendRequest {
    public IntegralSpendRequest() {
        this.amountSymbol = true;
        this.bucksSymbol = true;
        this.bucks = 0;
        this.amount = new BigDecimal(0);
    }

    @NotNull(message = "订单编号不能为空")
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @NotNull(message = "国家编码不能为空")
    @ApiModelProperty(value = "国家编码")
    private String countryCode;

    @NotNull(message = "渠道编码不能为空")
    @ApiModelProperty(value = "渠道编码")
    private String channelCode;

    @NotNull(message = "活动名称不能为空")
    @ApiModelProperty(value = "活动名称")
    private String action;

    @ApiModelProperty(value = "消费金额增减：默认 true 加、false 减")
    private Boolean amountSymbol;

    @ApiModelProperty(value = "消费金额：默认 0")
    private BigDecimal amount;

    @ApiModelProperty(value = "积分增减：默认 true 加、false 减")
    private Boolean bucksSymbol;

    @ApiModelProperty(value = "积分：默认 0")
    private Integer bucks;

    @ApiModelProperty(value = "是否增量：true 增量")
    private Boolean incrementalSymbol;

    private String uid;

}
