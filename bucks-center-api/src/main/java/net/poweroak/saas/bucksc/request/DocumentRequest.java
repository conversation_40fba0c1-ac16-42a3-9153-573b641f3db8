package net.poweroak.saas.bucksc.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 文案
 *
 * <AUTHOR>
 * @date 2022/7/27
 */
@Data
public class DocumentRequest implements Serializable {

    @ApiModelProperty(value = "渠道标识")
    @NotBlank(message = "渠道标识不能为空")
    private String channelCode;

    @ApiModelProperty(value = "国家编码")
    @NotNull(message = "所属国家区域不能为空")
    private Long countryId;

    @ApiModelProperty(value = "活动编码 参考ActivityEarnCode和ActivitySpendCode 文案编码和活动编码二选一必须填写其中一个")
    private String activityCode;

    @ApiModelProperty(value = "文案编码 文案编码和活动编码二选一必须填写其中一个")
    private String documentCode;
}
