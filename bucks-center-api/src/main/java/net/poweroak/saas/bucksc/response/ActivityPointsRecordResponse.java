package net.poweroak.saas.bucksc.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.page.Pagination;

/**
 * Created by zx on 2022/3/9 15:01
 */
@Data
public class ActivityPointsRecordResponse {


    @ApiModelProperty(value = "活动积分")
    private Long activityIntegral;

    @ApiModelProperty(value = "打卡积分")
    private Long clockIntegral;

    @ApiModelProperty(value = "互动积分")
    private Long interactiveIntegral;

    @ApiModelProperty(value = "老带新积分")
    private Long inviteIntegral;

    @ApiModelProperty(value = "我的排行")
    private Long rank;

    @ApiModelProperty(value = "是否显示排名")
    private Boolean showRank;

    @ApiModelProperty(value = "是否显示积分排名")
    private Boolean showIntegralRank;

    @ApiModelProperty(value = "积分明细分页")
    private Pagination<PointsDetailResponse> integralDetailPage;

}
