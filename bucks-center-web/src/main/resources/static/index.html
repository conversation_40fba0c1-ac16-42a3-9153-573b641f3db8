<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>积分中心</title>
    <script type="text/javascript" src="/webjars/plugins/jquery/jquery-1.12.4.js"></script>
    <script type="text/javascript" src="/webjars/layout/bluetti/module.js"></script>
    <script type="text/javascript" src="/webjars/layout/uideploy.js"></script>
</head>
<body>

<div id="app">
    <template><main-layout/></template>
</div>

<script>
    new Vue({
        el: '#app',
        // 注入路由配置
        router: appRouter
    });

    // 登录之后再去请求权限
    if (location.pathname.indexOf('login') <= -1) {
        win.PermissionUtil.getPermissionList();
        win.PermissionUtil.initDirective();
    }
</script>
</body>
</html>
