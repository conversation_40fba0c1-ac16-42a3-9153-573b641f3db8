<template>
  <div>
    <el-dialog :title="title" :visible.sync="dialogVisible" width="50%" @close="close()" center :append-to-body="true" :close-on-click-modal="false">
      <el-card class="box-card" shadow="none">
        <el-form ref="form" :rules="rules" :model="rule" :inline="true" label-width="100px">
          <el-row>

            <el-col :span="12">
              <el-form-item label="活动：" prop="activityName" >
                <el-select disabled filterable clearable v-model="rule.activityId" class="input2"  placeholder="国家/地区-活动-渠道">
                  <el-option
                      v-for="item in activityList"
                      :key="item.activityId"
                      :label="`${item.countryName}-${item.activityName}-${item.channelName}`"
                      :value="item.activityId">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="积分值:" prop="points">
                <el-input-number class="input2" v-model="rule.points" :min="0" :precision="0"></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
      <span slot="footer" class="dialog-footer">

            <el-button
                v-if="rule.enable === 1&&rule.ruleType!==''"
                 type="danger"
                @click="enableOrDisable()" v-has="'18242ec912ef79115229d154afa'">禁用
                </el-button>

                <el-button
                    v-if="rule.enable === 0&&rule.ruleType!==''"
                     type="success"
                    @click="enableOrDisable()" v-has="'18242ec912ef79115229d154afa'">启用
                </el-button>

          <el-button @click="save()" type="primary" v-has="'18242eb0d0cf79115229d154af9'">保 存</el-button>
          <el-button @click="close()" >取 消</el-button>
        </span>
    </el-dialog>
  </div>
</template>
<script>
module.exports = {
  data: function () {

    const pointsNum = (rule, value, callback) => {

      if(Number(value)<=0){
        callback(new Error('积分不能小于等于零'));
      }

      const age1 = /^[0-9]+\.[0-9]{2}$/
      const age2 = /^[0-9]+\.[0-9]{1}$/
      const age3 = /^[0-9]*$/
      if(!(age1.test(value) || age2.test(value) || age3.test(value))){
        callback(new Error('最多输入两位小数'))
      }else {
        callback()
      }
    }

    return {
      rule:{points:0,ruleType:''},
      title:"消耗规则",
      channelName:'',
      form: {
        code:"",
        pageNo: 1,
        pageSize: 10,
        totalPage: 0,
        ruleType: "",
        activityId: "",
        channelCode: "",
        countryId:""

      },
      dialogVisible: false,
      dataListLoading: false,
      dataList: [],
      activityList: [],
      countryList: [],
      rules: {
        activityId: [
          {required: true, message: '活动不能为空', trigger: 'change'}
        ],
        countryId: [
          {required: true, message: '区域不能为空', trigger: 'change'},
        ],
        spendLimit: [
          {required: true, message: '门槛不能为空', trigger: 'change'},
        ],
        points: [
          {required: true, validator: pointsNum, trigger: ["blur", 'change']}
        ],
      }

    }
  },
  methods: {
    loadingFunc() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },
    save() {
      console.log(this.form)
      let _that = this;
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$confirm('是否确定操作?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.loadingFunc();
            win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/activity/updateSpend"), this.rule).then(res => {
              if (res.msgCode == 0) {
                this.$emit("success");
                this.$message.success("修改成功");
                this.dialogVisible = false;
              }
            }).finally(function () {
              _that.loading.close();
            })
          }).catch(() => {
            this.$message.info('已取消编辑');
          });
        }
      })
    },


    countryFormat(row) {
      if ( row.countryId === null || row.countryId === '') return '';
      let obj = this.countryList.find((item) => {
        return item.geoNameId == row.countryId;
      });
      if(obj){
        return obj.countryName;
      }
      return '';
    },
    getCountry(){
      win.OkHttp.get($.api($.apiModule.midpMasterData, "~/iso/country/v1/localized/all-countries")).then(res => {
        if (res.msgCode == 0) {
          this.countryList = res.data.countries;
        }
      })
    },

    getActivity(){
      win.OkHttp.get($.api($.apiModule.bluBucksCenter, "/mgt/activity/commonList")).then(res => {
        if (res.msgCode == 0) {
          this.activityList = res.data;

        }
      })
    },

    enableOrDisable() {
      win.OkHttp.get($.api($.apiModule.bluBucksCenter, "/mgt/pointsSpendRule/enableOrDisable/") + this.rule.id).then(res => {
        if (res.msgCode == 0) {
          this.$message.success("操作成功");
          this.getList(this.form.id);
        }
      })
    },
    close() {
      this.dialogVisible = false
      this.$refs.form.resetFields();
      this.$emit("close")
    },

    search() {
      this.form.pageNo = 1;
      this.getList();
    },
    getList() {
      this.dataListLoading = true
      win.OkHttp.post($.api($.apiModule.bluBucksCenter, '/mgt/activity/pageSpend'), this.form).then(res => {
        if (res.msgCode == 0) {
          this.dataList = res.data.content;
          for(let i=0; i<this.dataList.length; i++){
              this.dataList[i].channelName=this.channelName;
          }
          this.form.totalPage = Number(res.data.totalElements)
          if(this.dataList.length>0){
            this.rule=this.dataList[0]
          }

        }
      })
      this.dataListLoading = false
    },
    // 当前页
    currentChangeHandle(val) {
      this.form.pageNo = val
      this.getList()
    },
    // 每页数
    sizeChangeHandle(val) {
      this.form.pageSize = val
      this.form.pageNo = 1
      this.getList()
    },

    addItem() {
      this.$refs.add.visible(this.form);
    },
    update(row) {
      this.$refs.edit.visible(row);
    },

    resetForm() {
      this.form.ruleType=""
      this.getList();
    },


    visible(row){
      this.form.code=row.code;
      this.form.activityId=row.id;
      this.form.channelCode=row.channelCode;
      this.channelName=row.channelName;
      this.form.countryId=row.countryId;
      this.rule.ruleType=row.spendRule
      this.rule.code=row.code;
      this.rule.activityId=row.id;
      this.rule.channelCode=row.channelCode;
      this.rule.countryId=row.countryId;
      this.getList();
      this.getCountry();
      this.getActivity();
      // this.getChannel();
      this.dialogVisible=true;
    }
  },

}
</script>
