<template>
  <div>
    <el-dialog :title="title" center :visible.sync="dialogVisible" width="40%" :close-on-click-modal="false"
               @close="resetForm('form')">
      <el-form ref="form" :rules="formRules" :model="form" :inline="true" size="mini" label-width="125px">
        <el-row>
          <el-col>
            <el-form-item label="活动名称" prop="name">
              <el-input class="input1" v-model="form.name" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="来源渠道" prop="channelCode">
              <el-select v-model="form.channelCode" placeholder="请选择" filterable clearable>
                <el-option
                    v-for="item in channel"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>


          </el-col>
        </el-row>


        <el-row>


          <el-col>
            <el-form-item label="国家/地区" prop="countryId">
              <el-select v-model="form.countryId" placeholder="请选择" clearable filterable>
                <el-option
                    v-for="item in countryList"
                    :key="item.geoNameId"
                    :label="item.countryName"
                    :value="item.geoNameId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>


          <el-col>
            <el-form-item label="积分规则类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择" @change="changeRuleType()">
                <el-option
                    v-for="item in ruleDataList4"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>


          <el-col>
            <el-form-item label="发放活动编码" prop="code" v-if="form.type===1">
              <el-select v-model="form.earnRule" placeholder="请选择" @change="setActCode()" clearable filterable>
                <el-option
                    v-for="item in ruleDataList1"
                    :key="item.ruleType"
                    :label="item.name+'/'+ item.code"
                    :value="item.ruleType">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>


          <el-col>
            <el-form-item label="消耗活动编码" prop="code" v-if="form.type===2">
              <el-select v-model="form.spendRule" placeholder="请选择" clearable filterable @change="setAct2Code()">
                <el-option
                    v-for="item in ruleDataList2"
                    :key="item.ruleType"
                    :label="item.name+'/'+ item.code"
                    :value="item.ruleType">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col  v-if="form.type===1">
            <el-form-item label="活动时间" prop="time">
              <el-date-picker
                  style="width: 20vw"
                  v-model="form.date"
                  clearable filterable
                  type="datetimerange"
                  :default-time="['00:00:00', '23:59:59']"
                  range-separator="-" value-format="yyyy-MM-dd HH:mm:ss"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"/>
            </el-form-item>
          </el-col>
          <!--          <el-col>-->
          <!--            <el-form-item label="活动编码" prop="code">-->
          <!--              <el-input class="input1" v-model="form.code" disabled clearable></el-input>-->
          <!--            </el-form-item>-->
          <!--          </el-col>-->
          <!--        </el-row>-->


          <!--        <el-row>-->
          <!--          <el-col >-->
          <!--            <el-form-item label="单次获取上限" prop="earnLimit" >-->
          <!--              <el-input class="input1" v-model="form.earnLimit"></el-input>-->
          <!--            </el-form-item>-->
          <!--          </el-col>-->
          <!--          <el-col>-->
          <!--            <el-form-item label="是否允许超限获取" prop="earnOverLimit" >-->
          <!--              <el-select v-model="form.earnOverLimit" placeholder="请选择">-->
          <!--                <el-option-->
          <!--                    v-for="item in ruleDataList3"-->
          <!--                    :key="item.code"-->
          <!--                    :label="item.name"-->
          <!--                    :value="item.code">-->
          <!--                </el-option>-->
          <!--              </el-select>-->
          <!--            </el-form-item>-->
          <!--          </el-col>-->
          <!--        </el-row>-->

        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" content="">

        <el-button type="primary" @click="submit">新增</el-button>
        <el-button @click="resetForm('form')">取 消</el-button>
      </div>
    </el-dialog>


  </div>
</template>
<script>


module.exports = {
  components: {},
  data: function () {
    var checkform = (rule, value, callback) => {
      if (!value) callback();
      // const reg = /^(?!(0[0-9]{0,}$))[0-9]{1,}[.]{0,}[0-9]{0,}$/
      const reg = /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/;
      if (!reg.test(value)) {
        callback(new Error("请输入非负数"));
      } else {
        callback();
      }
    };
    return {
      countryList: [],
      ruleDataList1: [],
      ruleDataList2: [],
      ruleDataList3: [],
      ruleDataList4: [],
      channel: [],
      form: {
        type: 1,
        name: '',
        channelCode: '',
        countryId: null,
        earnRule: '',
        spendRule: '',
        code: '',
        date:[]
      },
      dialogVisible: false,
      title: "新增活动",
      loading: null,

      formRules: {
        channelCode: [
          {required: true, message: '必须选择来源渠道', trigger: ["blur", 'change']}
        ],
        name: [
          {required: true, message: '必须输入活动名称', trigger: 'blur'}
        ],
        countryId: [
          {required: true, message: '必须选择国家/地区', trigger: ["blur", 'change']}
        ],
        spendRule: [
          {required: true, message: '必须选择消耗规则', trigger: ["blur", 'change']}
        ],
        earnRule: [
          {required: true, message: '必须选择发放规则', trigger: ["blur", 'change']}
        ],
        code: [
          {required: true, message: '必须生成活动编码', trigger: ["blur", 'change']}
        ],
        earnLimit: [{validator: checkform, trigger: "blur"}]
      },
    }
  },
  props: {},
  methods: {
    changeRuleType() {
      this.form.earnRule = '';
      this.form.spendRule = '';
      this.form.code = '';
    },
    loadingFunc() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },
    visible(row, ruleDataList1, ruleDataList2, ruleDataList3, countryList) {
      this.ruleDataList4 = [{code: 1, name: '积分发放规则'}, {code: 2, name: '积分消耗规则'}]
      this.ruleDataList1 = ruleDataList1;
      this.ruleDataList2 = ruleDataList2;
      this.ruleDataList3 = ruleDataList3;
      this.countryList = countryList;


      win.OkHttp.get($.api($.apiModule.bluBucksCenter, "/mgt/channel/existsList")).then(res => {
        if (res.msgCode == 0) {
          this.channel = res.data;
        }
      })
      this.dialogVisible = true;


    },
    resetForm(formName) {
      this.form.earnRule = ''
      this.form.spendRule = ''
      this.$refs[formName].resetFields();
      this.dialogVisible = false;
    },


    setAct2Code() {
      let that = this;
      this.ruleDataList2.forEach(function (item) {
        if (item.ruleType == that.form.spendRule) {
          that.form.code = item.code;
        }
      })

    },

    setActCode() {
      let that = this;
      this.ruleDataList1.forEach(function (item) {
        if (item.ruleType == that.form.earnRule) {
          that.form.code = item.code;
        }
      })
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {

          this.$confirm('是否确定新增?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.loadingFunc();
            win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/activity/add"), this.form).then(res => {
              this.loading.close();

              if (res.msgCode == 0) {
                this.$message.success("新增成功");
                this.resetForm("form");
                this.$emit("success");
              }
            }).catch(e => {
              this.loading.close();
            })
          }).catch(() => {
            this.$message.info('已取消新增');
          });


        } else {
          return false;
        }
      })
    }
  }
}
</script>
<style>


</style>