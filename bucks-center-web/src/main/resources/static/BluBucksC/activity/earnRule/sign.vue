<template>
  <div>
    <el-dialog :title="title" center :visible.sync="dialogVisible" width="750px" :close-on-click-modal="false"
               @close="resetForm('form')">
      <el-card class="box-card" shadow="none">
        <el-form ref="form" :model="form" :inline="true" size="mini" label-width="150px">
          <el-row>
            <el-table :data="dataList" border>
              <el-table-column prop="earnLimit" label="第几天">
                <template slot-scope="scope">
                  <el-input type="hidden" class="input2" :disabled="true"
                            onkeyup="this.value=this.value.replace(/[^\d]/g,'')"
                            v-model="scope.row.earnLimit"></el-input>
                  {{earnNameList[scope.row.earnLimit - 1] }}
                </template>
              </el-table-column>
              <el-table-column prop="points" label="基础赠送积分">
                <template slot-scope="scope">
                  <el-input class="input2" onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
                            v-model="scope.row.points"></el-input>
                </template>
              </el-table-column>
              <el-table-column  label=" ">
              </el-table-column>
              <el-table-column label="连续天数">
                <template slot-scope="scope">
                  {{scope.row.earnLimit}}
                </template>
              </el-table-column>
              <el-table-column prop="extraReward" label="额外奖励">
                <template slot-scope="scope">
                  <el-input class="input2" onkeyup="this.value=this.value.replace(/[^\d]/g,'')"
                            v-model="scope.row.extraReward"></el-input>
                </template>
              </el-table-column>

            </el-table>
          </el-row>
        </el-form>
      </el-card>
      <div slot="footer" class="dialog-footer" content="">
        <el-button type="primary" v-has="'18204fbe641f79115229d154a56'" @click="submit()">保存</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
module.exports = {
  data: function () {
    return {
      dialogVisible: false,
      title: "",
      form: {
        extraReward: 0,
        earnLimit: '',
        activityId: '',
        earnType: 2,
        dailyNum: 1,
        enable: 1,
        ruleType: 1,
        earnLimitName: ''
      },
      batchPoints: '',
      earnLimitPointsExtraReward: '',
      dailyNumDisable: false,
      loading: null,
      earnTypeList: [{id: 1, name: '一次性'}, {id: 2, name: '无限次'}],
      earnNameList: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
      activityList: [],
      dayList: [],
      dataList: [],
      limitDays: 7,
    }
  },
  props: {},
  methods: {
    visible(row, countryName) {
      this.dataList = [];
      this.form = {...row};
      this.dialogVisible = true;
      this.title = countryName + row.name;
      this.getList();
    },
    updatePoints() {
      let points = this.batchPoints.replace(/[^\d]/g, '');
      $("input[name='points']").each(function () {
        $(this).val(points);
      })
    },
    initDays() {
      this.dayList = [];
      this.dataList = [];
      for (let i = 1; i <= this.limitDays; i++) {
        this.dataList.push({
          activityId: this.form.id,
          points: 0,
          earnLimit: i,
          extraReward: 0,
          dailyNum: 1,
          enable: 1,
          ruleType: 1,
          earnType: 2
        });
      }
    },
    loadingFunc() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },
    getList() {
      this.dataListLoading = true
      win.OkHttp.post($.api($.apiModule.bluBucksCenter, '/mgt/activity/getEarnRuleList'), {activityId: this.form.id}).then(res => {
        if (res.msgCode == 0) {
          if (res.data.length == 0) {
            this.initDays();
          } else if (res.data.length > this.limitDays) {
            let limitDays = this.limitDays;
            let data = [];
            res.data.forEach(function (item, index) {
              if (index < limitDays) {
                data.push(item);
              }
            })
            this.dataList = data;
          } else {
            this.dataList = res.data;
          }
        }
      })
      this.dataListLoading = false;
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loadingFunc();
          if (!this.form.extraReward) {
            this.form.extraReward = 0;
          }
          let actId = this.form.id;
          this.dataList.forEach(function (item) {
            item.activityId = actId;
          });
          win.OkHttp.post($.api($.apiModule.bluBucksCenter, '/mgt/activity/updateEarn'), {
            "items": this.dataList,
            ruleType: this.form.earnRule
          }).then(res => {
            this.loading.close();
            if (res.msgCode == 0) {
              this.$message.success("保存成功")
              this.resetForm("form");
              this.$emit("success");
            }
          }).catch(e => {
            this.loading.close();
          })
        } else {
          return false;
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.dialogVisible = false;
    }
  }
}
</script>