<template>
  <div>
    <el-dialog :title="title" center :visible.sync="dialogVisible" width="1280px" :close-on-click-modal="false"
               @close="resetForm('form')">
      <el-form ref="form"  :model="form" :inline="true" size="mini" label-width="150px">
        <el-row>
          <el-table :data="dataList" border style="width: 100%">
            <el-table-column prop="earnLimit" label="兑换门槛">
              <template slot-scope="scope">
                <el-input class="input2" max="11" onkeyup="this.value=this.value.replace(/[^\d]/g,'')"
                          v-model="scope.row.earnLimit"></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="points" label="兑换比例">
              <template slot-scope="scope">
                <el-input class="input2" max="11" onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
                          v-model="scope.row.points"></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="extraReward" label="额外奖励">
              <template slot-scope="scope">
                <el-input class="input2" max="11" onkeyup="this.value=this.value.replace(/[^\d]/g,'')"
                          v-model="scope.row.extraReward"></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="earnType" label="发放类型">
              <template slot-scope="scope">
                <el-select v-model="scope.row.earnType" placeholder="请选择" @change="changeEarnType(scope.row)" clearable>
                  <el-option
                      v-for="item in earnTypeList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id">
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="address" label="单用户每日次数">
              <template slot-scope="scope">
                <el-input class="input2" max="11" :disabled="scope.row.dailyNumDisable"
                          onkeyup="this.value=this.value.replace(/[^\d]/g,'')"
                          v-model="scope.row.dailyNum"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="180">
              <template slot-scope="scope">
                <el-button type="danger" v-has="'18204fc1e89f79115229d154a57'" @click="deleteRow(scope.row)">删除</el-button>
                <el-button type="warning" v-has="'18204fbe641f79115229d154a56'"  v-show="scope.$index === (dataList.length-1)"
                           @click="addRow()">新增
                </el-button>
              </template>
            </el-table-column>

          </el-table>
        </el-row>
      </el-form>
      <div style="text-align: center;width: 100%">
        <el-button type="warning" v-has="'18204fbe641f79115229d154a56'"  v-show="addShow"
                   @click="addRow()">新增</el-button>
      </div>
      <div slot="footer" class="dialog-footer" content="">
        <el-button type="primary" v-has="'18204fbe641f79115229d154a56'" @click="submit()">保存</el-button>
        <el-button @click="resetForm('form')">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
module.exports = {
  data: function () {
    return {
      dialogVisible: false,
      title: "",
      form: {},
      dataList:[],
      loading: null,
      rowId: 0,
      addShow: false,
      earnTypeList: [{id: 1, name: '一次性'}, {id: 2, name: '无限次'}],
    }
  },
  props: {},
  methods: {
    visible(row, countryName) {
      this.form = {...row};
      this.dialogVisible = true;
      this.rowId = 0;
      this.dataList  = [];
      this.title = countryName + row.name;
      this.getList();
    },
    changeEarnType(row) {
      if (row.earnType == '1') {
        row.dailyNumDisable = true;
        row.dailyNum = 1;
      } else {
        row.dailyNumDisable = false;
        row.dailyNum = 99999;
      }
    },
    checkShow(){
      this.addShow = this.dataList.length === 0;
    },
   getList() {
    this.dataListLoading = true
    win.OkHttp.post($.api($.apiModule.bluBucksCenter, '/mgt/activity/getEarnRuleList'),{activityId: this.form.id}).then(res => {
      if (res.msgCode == 0) {
        this.rowId = 0;
        let that = this;
        this.dataList = res.data;
        this.dataList.forEach(function (item) {
          that.rowId ++ ;
          item.rowId = that.rowId;
          item.dailyNumDisable = item.earnType == '1';
        })
        this.checkShow();
      }
    })
    this.dataListLoading = false;
  },
  addRow() {
    this.rowId++;
    let initData = {
      id:'',
      rowId: this.rowId,
      earnType: 2,
      extraReward: 0,
      earnLimit: 0,
      activityId: this.form.id,
      dailyNum: 99999,
      enable: 1,
      ruleType: 3,
      points: 1,
      title: '',
    }
    this.dataList.push(initData);
    this.checkShow();
  },
  deleteRow(row) {
    this.$confirm('确认删除? ', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      if (row.id) {
        win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/activity/deleteEarn/" + row.id)).then(res => {
          if (res.msgCode == 0) {
            this.$message.success("删除成功")
          }
        })
      }
      let newTableData = [];
      this.dataList.forEach(function (item) {
        if (item.rowId != row.rowId) {
          newTableData.push(item);
        }
      })
      this.dataList = newTableData;
      this.checkShow();
    }).catch(() => {
    });
  },
  loadingFunc() {
    this.loading = this.$loading({
      lock: true,
      text: 'Loading',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });
  },
  submit() {
    this.$refs.form.validate((valid) => {
      if (valid) {
        this.loadingFunc();
        win.OkHttp.post($.api($.apiModule.bluBucksCenter, '/mgt/activity/updateEarn'), {"items":this.dataList, ruleType: this.form.earnRule}).then(res => {
          this.loading.close();
          if (res.msgCode == 0) {
            this.$message.success("保存成功");
            this.getList();
          }
        }).catch(e => {
          this.loading.close();
        })
      } else {
        return false;
      }
    })
  },
    selectAll(activityId){
      this.dataList.forEach(function(item){
        item.activityId = activityId;
      })
    },
  resetForm(formName) {
    this.$refs[formName].resetFields();
    this.dialogVisible = false;
  }
}
}
</script>