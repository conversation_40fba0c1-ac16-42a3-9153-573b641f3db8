<template>
  <div>
    <el-form :model="searchForm" ref="form" :inline="true" size="mini" label-width="110px">
      <el-row>
        <el-col :span="8">
          <el-form-item label="来源渠道" prop="channelCode">
            <el-select v-model="searchForm.channelCode" placeholder="请选择" filterable clearable>
              <el-option
                v-for="item in channel"
                :key="item.id"
                :label="item.name"
                :value="item.code"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="活动名称" prop="name">
            <el-input type="text" v-model="searchForm.name" placeholder="活动名称" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="国家/地区" prop="countryId" >
            <el-select v-model="searchForm.countryId" placeholder="请选择" clearable filterable>
              <el-option
                v-for="item in countryList"
                :key="item.geoNameId"
                :label="item.countryName +'/'+ item.countryCode"
                :value="item.geoNameId"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row type="flex">
        <el-col :span="10">
          <el-form-item label=" ">
            <el-button type="primary" @click="search()">查询</el-button>
            <el-button type="primary" @click="resetForm()">重置</el-button>
            <!--              <el-button type="primary" v-has="'182052e97f8f79115229d154a60'" @click="add()">新增</el-button>-->
            <el-button type="primary" @click="add()" v-has="'1826cd80c45f79115229d154bae'">新增</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-tabs v-model="searchForm.activeType" type="card" @tab-click="handleClick">
      <el-tab-pane label="积分发放" name="1">
        <el-table :data="dataList" border stripe  size="small">
          <el-table-column align="center" show-overflow-tooltip header-align="center" type="index" label="序号"></el-table-column>
          <!--          <el-table-column align="center" show-overflow-tooltip prop="id" label="活动标识id" ></el-table-column>-->
          <el-table-column align="center" show-overflow-tooltip prop="name" label="活动名称" sortable></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="channelName" label="来源渠道名称" sortable>
            <template slot-scope="{row}">
              {{ row.channelName || '--' }}
            </template>
          </el-table-column>
          <el-table-column align="center" show-overflow-tooltip  label="活动编码">
            <template slot-scope="scope">
              {{statusFormat1(scope.row)+"/"+scope.row.code}}
            </template>
          </el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="country" label="国家/地区" sortable :formatter="countryFormat"></el-table-column>
          <el-table-column align="center" label="操作">
            <template slot-scope="{row}">
              <el-button size="mini" type="primary" v-has="'182052a0a8ef79115229d154a5f'" @click="edit(row)">编辑</el-button>
              <el-button v-if="isEligibleForPoints(row)" size="mini" type="primary" v-has="'180a763a177f791157c8280ba22'"  @click="showEarnRule(row)" >规则</el-button>
              <el-button size="mini" type="danger" @click="remove(row)"  v-has="'182399d7887f79115229d154ade'">删除</el-button>
            </template>
          </el-table-column>

        </el-table>
        <el-pagination
          background
          :current-page="searchForm.pageNo"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="searchForm.pageSize"
          :total="searchForm.totalPage"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
          layout="total, sizes, prev, pager, next, jumper"
        >
        </el-pagination>
      </el-tab-pane>
      <el-tab-pane label="积分消耗" name="2">
        <el-table :data="dataList" border stripe  size="small">
          <el-table-column align="center" show-overflow-tooltip label="序号" type="index"></el-table-column>
          <!--          <el-table-column align="center" show-overflow-tooltip prop="id" label="活动标识id" ></el-table-column>-->
          <el-table-column align="center" show-overflow-tooltip prop="name" label="活动名称" sortable></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="channelName" label="来源渠道名称" sortable>
            <template slot-scope="{row}">
              {{ row.channelName || '--' }}
            </template>
          </el-table-column>
          <el-table-column align="center" show-overflow-tooltip  label="活动编码">
            <template slot-scope="scope">
              {{statusFormat2(scope.row)+"/"+scope.row.code}}
            </template>
          </el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="country" label="国家/地区" sortable :formatter="countryFormat"></el-table-column>
          <!--              <el-table-column align="center" show-overflow-tooltip prop="earnRule" label="积分发放规则" :formatter="statusFormat1"></el-table-column>-->
          <!--              <el-table-column align="center" show-overflow-tooltip prop="spendRule" label="积分消耗规则" :formatter="statusFormat2" ></el-table-column>-->
          <el-table-column align="center" label="操作">
            <template slot-scope="scope">
              <el-button size="mini" type="primary" v-has="'182052a0a8ef79115229d154a5f'" @click="edit(scope.row)">编辑</el-button>
              <el-button size="mini" type="primary"  v-has="'180a7f0507ff791157c8280ba5d'" @click="rule(scope.row)" v-if="formatRuleButton(scope.row.code)">规则</el-button>
              <el-button size="mini" type="danger" @click="remove(scope.row)" v-has="'182399d7887f79115229d154ade'">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
          background
          :current-page="searchForm.pageNo"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="searchForm.pageSize"
          :total="searchForm.totalPage"
          layout="total, sizes, prev, pager, next, jumper"
        >
        </el-pagination>
      </el-tab-pane>
    </el-tabs>
    <add ref="add" @success="getList()"></add>
    <edit ref="edit" @success="getList()"></edit>
    <earn-comm ref="earnComm" @success="getList()"></earn-comm>
    <earn-order ref="earnOrder" @success="getList()"></earn-order>
    <earn-pv-power ref="earnPvPower" @success="getList()"></earn-pv-power>
    <earn-sign ref="earnSign" @success="getList()"></earn-sign>
    <spend-list ref="spendList" @success="getList()"></spend-list>
  </div>
</template>
<script>
//const ruleDataList1=  [{code: '1', name: "签到", actCode: "HD01"}, {code: '2', name: "货币兑换", actCode: "HD02"}, { code: '3', name: "光伏发电", actCode: "HD03"}, {code: '9', name: "通用", actCode: "HD99"}];
//const ruleDataList2 = [{code:'1',name:"卡券兑换", actCode: "HD01"},{code: '2', name: "购物下单", actCode: "HD02"},{code: '3', name: "货币抵扣", actCode: "HD03"},{code: '4', name: "轮盘抽奖", actCode: "HD04"}];
module.exports = {
  components: {
    "add": httpVueLoader("/BluBucksC/activity/add.vue"),
    "edit": httpVueLoader("/BluBucksC/activity/edit.vue"),
    "spend-list": httpVueLoader("/BluBucksC/activity/spendList.vue"),
    "earn-comm": httpVueLoader("/BluBucksC/activity/earnRule/comm.vue"),
    "earn-order": httpVueLoader("/BluBucksC/activity/earnRule/order.vue"),
    "earn-pv-power": httpVueLoader("/BluBucksC/activity/earnRule/pvPower.vue"),
    "earn-sign": httpVueLoader("/BluBucksC/activity/earnRule/sign.vue"),
  },
  data: function () {
    return {
      channel:[],
      searchForm:{
        pageNo: 1,
        pageSize: 10,
        totalPage: 0,
        countryId: null,
        name:'',
        channelCode:'',
        activeType:'1',
      },
      ruleDataList1:[],
      ruleDataList2:[],
      dataList:[],
      countryList:[],
      ruleDataList3 :[{code:'Y',name:"开"},{code: 'N', name: "关"}],
    }
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.getChannelList()
      this.getAllCountry();
      this.getList();
      this.getSpendRule();
      this.getEarnRule();
    },
    getList(){
      win.OkHttp.post($.api($.apiModule.bluBucksCenter, '/mgt/activity/pageList'), this.searchForm).then(res => {
        if (res.msgCode == 0) {
          this.dataList = res.data.content;
          this.searchForm.totalPage = Number(res.data.totalElements)
        }
      })
    },
    getChannelList() {
      win.OkHttp.get($.api($.apiModule.bluBucksCenter, "/mgt/channel/existsList")).then(res => {
        if (res.msgCode === 0) {
          this.channel = res.data;
        }
      })
    },
    getAllCountry() {
      win.CommonApiUtil.getCountryList().then(res => {
        if (res) {
          this.countryList = res.countries;
        }
      })
    },
    getEarnRule(){
      win.OkHttp.get($.api($.apiModule.bluBucksCenter, '/mgt/activity/earnRuleList')).then(res => {
        if (res.msgCode == 0) {
          this.ruleDataList1 = res.data;
        }
      })
    },
    getSpendRule(){
      win.OkHttp.get($.api($.apiModule.bluBucksCenter, '/mgt/pointsSpendRule/ruleList')).then(res => {
        if (res.msgCode == 0) {
          this.ruleDataList2 = res.data;
        }
      })
    },
    handleClick() {
      // this.$refs.form.resetFields();
      this.searchForm.pageNo = 1
      this.searchForm.pageSize = 10
      this.search();
    },
    countryFormat(row) {
      if ( row.countryId === null || row.countryId === '') return row.countryId;
      let result = null;
      this.countryList.forEach(function (item) {
        if (item.geoNameId === row.countryId) {
          result = item.countryName;
        }
      })
      return result;
    },
    statusFormat1(row) {
      if ( row.earnRule === null || row.earnRule === '') return row.earnRule;
      // let data =  this.ruleDataList1.filter(item => item.ruleType === row.earnRule)[0];
      // if(data != null){
      //   return data.name;
      // }
      // return '';

      let result = null;
      this.ruleDataList1.forEach(function (item) {
        if (item.ruleType === row.earnRule) {
          result = item.name;
        }
      })
      return result;
    },
    statusFormat2(row) {
      if ( row.spendRule === null || row.spendRule === '') return row.spendRule;
      // let data =  this.ruleDataList2.filter(item => item.ruleType === row.spendRule)[0];
      // if(data != null){
      //     return data.name;
      // }
      // return '';

      let result = null;
      this.ruleDataList2.forEach(function (item) {
        if (item.ruleType === row.spendRule) {
          result = item.name;
        }
      })
      return result;

    },
    currentChangeHandle(val) {
      this.searchForm.pageNo = val
      this.getList()
    },
    // 每页数
    sizeChangeHandle(val) {
      this.searchForm.pageSize = val
      this.searchForm.pageNo = 1
      this.getList()
    },
    add() {
      this.$refs.add.visible(true,this.ruleDataList1,this.ruleDataList2,this.ruleDataList3,this.countryList);
    },
    search() {
      this.searchForm.pageNo = 1;
      this.getList();
    },
    resetForm() {
      this.$refs.form.resetFields();
      this.getList();

    },
    edit(row) {
      this.$refs.edit.visible(row, this.ruleDataList1,this.ruleDataList2,this.ruleDataList3,this.countryList);
    },
    rule(row){
      this.$refs.spendList.visible(row);
    },
    remove(row) {
      this.$confirm('是否确定删除该活动?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        win.OkHttp.get($.api($.apiModule.bluBucksCenter, '/mgt/activity/delete/') + row.id).then(res => {
          if (res.msgCode == 0) {
            this.$message.success("删除成功")
            this.searchForm.pageNo = 1
            this.getList();
          }
        })
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    showEarnRule(row){
        let obj = this.countryList.find((item) => {
          return item.geoNameId == row.countryId;
        });
        let countryName = "";
        if(obj){
          countryName = obj.countryName +"-";
        }
        if(row.earnRule == 1){
          //签到
          this.$refs.earnSign.visible(row,countryName);
        }else if(row.earnRule == 2){
          //购物
          this.$refs.earnOrder.visible(row,countryName);
        }else if(row.earnRule == 3){
          //光伏发电
          this.$refs.earnPvPower.visible(row,countryName);
        }else{
          this.$refs.earnComm.visible(row,countryName);
        }
    },
    // 积分发放-是否显示规则按钮
    isEligibleForPoints(row) {
      const codeList = ['HDE04', 'HDE08', 'HDE10', 'HDE19', 'HDE20', 'HDE21']
      return !codeList.includes(row.code)
    },
    // 积分消耗-是否显示规则按钮
    formatRuleButton(code) {
      const codeList = ['HDS04', 'HDS05', 'HDS06', 'HDS07', 'HDS08', 'HDS17']
      return codeList.includes(code);
    }
  }
}
</script>
