<template>
  <div>
    <el-card class="box-card" shadow="none">
      <el-form :model="searchForm" ref="form" :inline="true" size="mini" label-width="110px">
        <el-row :span="18">
          <el-col :span="18">
            <el-form-item label="参数" prop="requestParams">
              <el-input type="text" v-model="searchForm.requestParams" placeholder="参数" clearable></el-input>
            </el-form-item>
            <el-form-item label="错误信息" prop="errMsg">
              <el-input type="text" v-model="searchForm.errMsg" placeholder="错误信息" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex">
          <el-col :span="10">
            <el-form-item label=" ">
              <el-button type="primary" @click="search()">查询</el-button>
              <el-button type="primary" @click="resetForm()">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

    </el-card>
    <el-row>
      <el-col :span="24">
        <el-table :data="dataList" border stripe size="small">
          <el-table-column align="center" show-overflow-tooltip header-align="center" type="index"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="typeName" label="类型"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="requestParams" label="参数"
                           width="500"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="errMsg" label="错误信息"
                           width="500"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="creTime" width="100px" label="创建时间" ></el-table-column>
          <el-table-column align="center" label="操作" min-width="200px">
            <template slot-scope="scope">
<!--              <el-button size="mini" type="primary" v-has="" @click="doRequest(scope.row)">执行</el-button>-->
              <el-button size="mini" v-has="'18204ff9a1bf79115229d154a59'" type="danger" @click="remove(scope.row.id)">删除</el-button>
<!--              <el-button size="mini" type="danger" @click="remove(scope.row.id)">删除</el-button>-->
            </template>
          </el-table-column>

        </el-table>

        <el-pagination @size-change="sizeChangeHandle"
                       @current-change="currentChangeHandle"
                       :current-page="searchForm.pageNo"
                       :page-sizes="[10, 20, 50, 100]"
                       :page-size="searchForm.pageSize"
                       :total="searchForm.totalPage"
                       layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>

      </el-col>
    </el-row>

    <el-row>
      <el-col>
        <add ref="add" @success="getList()"/>
      </el-col>
    </el-row>
    <el-row>
      <el-col>
        <edit ref="edit" @success="getList()"/>
      </el-col>
    </el-row>
  </div>
</template>
<script>


module.exports = {
  components: {
    "add": httpVueLoader("/BluBucksC/faq/add.vue"),
    "edit": httpVueLoader("/BluBucksC/faq/edit.vue"),


  },
  data: function () {
    return {
      searchForm: {
        pageNo: 1,
        pageSize: 10,
        totalPage: 0,
        requestParams: '',
        errMsg: ''
      },
      dataList: [],
      countryList: [],
    }
  },

  methods: {
    search() {
      this.searchForm.pageNo = 1;
      this.getList();
    },
    getList() {
      win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/requestLog/pageList"), this.searchForm).then(res => {
        if (res.msgCode == 0) {
          this.dataList = res.data.content;
          this.searchForm.totalPage = Number(res.data.totalElements)
        }
      })
    },
    doRequest(row) {
      this.$confirm('是否确定执行?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        win.OkHttp.get($.api($.apiModule.bluBucksCenter, '/mgt/requestLog/request/' + row.id)).then(res => {
          if (res.msgCode == 0) {
          }
        })
      }).catch(() => {
        this.$message.info('已取消删除');
      });

    },
    remove(id) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        win.OkHttp.post($.api($.apiModule.bluBucksCenter, '/mgt/requestLog/delete/') + id).then(res => {
          if (res.msgCode == 0) {
            this.$message.success("删除成功")
            this.searchForm.pageNo = 1
            this.getList();
          }
        })
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    currentChangeHandle(val) {
      this.searchForm.pageNo = val
      this.getList()
    },
    // 每页数
    sizeChangeHandle(val) {
      this.searchForm.pageSize = val
      this.searchForm.pageNo = 1
      this.getList()
    },

    resetForm() {
      this.$refs.form.resetFields();
      this.getList();
    },

    getCountryList(){
      win.OkHttp.get($.api($.apiModule.midpMasterData, "~/iso/country/v1/localized/all-countries")).then(res => {
        if (res.msgCode == 0) {
          this.countryList = res.data.countries;
        }
      })
    },

  },
  mounted() {
    this.$nextTick(function () {
      this.getList();
      this.getCountryList();
    })
  }
}
</script>