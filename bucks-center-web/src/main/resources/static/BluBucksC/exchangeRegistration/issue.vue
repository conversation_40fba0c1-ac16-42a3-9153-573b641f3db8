<template>
  <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="40%"
      :close-on-click-modal="false"
      destroy-on-close
      :before-close="closeDialog">
    <el-form ref="form" :rules="rule" :model="form">
      <div>
        <el-row>
          <el-col>
            <span>发放用户:</span>
            <em>{{ form.name }}</em>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <span>发放用户邮箱:</span>
            <em>{{ form.accountEmail }}</em>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <span>发放用户手机:</span>
            <em>{{ form.phone }}</em>
          </el-col>
        </el-row>
      </div>

      <div>
        <el-row v-if="mode === 'issue'">
          <el-col>
            <el-form-item label="发放积分" prop="points">
              <el-input v-model.number="form.points" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="mode === 'issue'">
          <el-col>
            <el-form-item label="发放积分账户国家" prop="countryId">
              <el-select v-model="form.countryId" placeholder="请选择" filterable clearable>
                <el-option
                    v-for="item in countryList"
                    :key="item.countryId"
                    :label="item.countryName"
                    :value="item.countryId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="备注" prop="comment">
              <el-input type="textarea" v-model.trim="form.comment" placeholder="请输入"  rows='3' maxlength="1000" show-word-limit></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submit">确 定</el-button>
      <el-button plain @click="closeDialog">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
module.exports = {
  components: {},
  data() {
    return {
      dialogVisible: false,
      mode: '',
      title: "编辑活动",
      form: {
        name: '',
        accountEmail: '',
        phone: '',
        comment: '',
        countryId: '',
        points: '',
      },
      countryList: [],
      loading: null,
      rule: {
        points: [
          {
            required: true,
            message: '请选择需要发放的积分',
            trigger: 'blur',
            validator: (rule,value,callback) => {
              if(!value){
                callback(new Error('发放积分不能为空'))
              }else {
                callback()
              }
            }
          }
        ],
        countryId: [
          {required: true, message: '请选择需要发放的积分账户国家', trigger: ["blur", 'change']}
        ],
      },
    }
  },
  methods: {
    showDialog(mode, data) {
      this.mode = mode
      this.title = mode === 'issue' ? '发放' : '不发放'
      this.dialogVisible = true
      this.form = JSON.parse(JSON.stringify(data))
      this.getCountryList()
    },
    closeDialog() {
      Object.assign(this.form, {
        name: '',
        accountEmail: '',
        phone: '',
        comment: '',
        countryId: '',
        points: '',
      });
      this.$refs.form.clearValidate()
      this.dialogVisible = false
    },
    getLoading() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },
    async getCountryList() {
      const url = $.api($.apiModule.midpMasterData, "~/iso/country/v1/localized/all-countries")
      this.getLoading()
      try {
        const res = await win.OkHttp.get(url)
        this.loading.close()
        if (res.msgCode === 0 && res.data) {
          res.data.countries.forEach(item => {
            let obj = {
              countryName: item.countryName,
              countryCode: item.countryCode,
              countryId: item.geoNameId,
            }
            this.countryList.push(obj)
          })
        }
      }catch(e) {
          this.loading.close()
          this.$message.error("获取国家数据失败")
      }
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$confirm('是否确定进行修改?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let path = ''
            if(this.mode === 'issue') {
              path = '/mgt/pointsExchangeRegistration/issue'
            }else {
              path = '/mgt/pointsExchangeRegistration/refuse'
            }
            const url = $.api($.apiModule.bluBucksCenter, path)
            this.getLoading();
            win.OkHttp.put(url, this.form).then(res => {
              this.loading.close();
              if (+res.msgCode === 0) {
                this.$message.success(res.message);
                this.closeDialog()
                this.$emit("success");
              }
            }).catch(e => {
              this.loading.close();
            })
          }).catch(() => {
            this.$message.info('已取消修改');
          });
        } else {
          return false;
        }
      })
    }
  }
}
</script>

<style>
</style>
