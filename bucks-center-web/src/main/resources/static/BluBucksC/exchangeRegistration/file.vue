<template>
  <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="30%"
      :close-on-click-modal="false"
      destroy-on-close
      :before-close="closeDialog">
    <el-form ref="form" :rules="rule" :model="form">
      <div>
        <el-form-item label="导入文件" prop="file">
          <el-upload
              class="upload-demo"
              ref="upload"
              action=""
              multiple
              :limit=2
              accept=".xls, .xlsx"
              :auto-upload="false"
              :file-list="fileList"
              :on-change="handleChange"
              :show-file-list="true"
          >
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">请上传 .xls,.xlsx 标准格式文件</div>
          </el-upload>
        </el-form-item>
      </div>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submit">确 定</el-button>
      <el-button plain @click="closeDialog">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
module.exports = {
  components: {},
  data() {
    return {
      dialogVisible: false,
      mode: '',
      title: "批量发放",
      form: {
        file: null,
      },
      rule: {
        points: [
          {
            required: true,
            message: '请上传文件',
            trigger: 'change',
            validator: (rule,value,callback) => {
              if(!value){
                callback(new Error('上传文件不能为空'))
              }else {
                callback()
              }
            }
          }
        ],
      },
      countryList: [],
      loading: null,
      fileList: [],
    }
  },
  methods: {
    showDialog() {
      this.dialogVisible = true
    },
    closeDialog() {
      Object.assign(this.form, {
        file: null,
      });
      this.$refs.form.clearValidate()
      this.$refs.form.resetFields()
      this.$refs.upload.clearFiles()
      this.dialogVisible = false
    },
    getLoading() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },
    handleChange(_file, _fileList) {
      if(_fileList.length > 1) {
        this.fileList.splice(0, 1, _file)
      }
      this.form.file = _file
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$confirm('是否确定批量发放?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let formData = new FormData()
            formData.append('file', this.form.file.raw)

            let path = '/mgt/pointsExchangeRegistration/importExcel'
            const url = $.api($.apiModule.bluBucksCenter, path)
            console.log(formData)
            this.getLoading();
            win.OkHttp.post(url, formData).then(res => {
              this.loading.close();
              if (+res.msgCode === 0) {
                this.$message.success(res.message);
                this.closeDialog()
                this.$emit("success");
              }
            }).catch(e => {
              this.loading.close();
            })
          }).catch(() => {
            this.loading.close();
            this.$message.info('已取消批量发放');
          });
        } else {
          return false;
        }
      })
    }
  }
}
</script>

<style>
</style>
