<template>
  <!-- 包裹 -->
  <div class="page">
    <!-- 标题 -->
    <div class="page__title"></div>
    <!-- 主要内容 -->
    <div class="page__container">
      <!-- 查询条件 -->
      <div class="page__query-box">
        <el-card class="box-card" shadow="none">
          <el-form :model="form" ref="form" :inline="true" size="mini" label-width="110px">
            <el-row>
              <el-col :span="6">
                <el-form-item label="邮箱" prop="email">
                  <el-input type="text" v-model="form.email" placeholder="请输入" clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="购买平台" prop="platform" >
                  <el-select v-model="form.platform" placeholder="请选择" clearable filterable >
                    <el-option
                        v-for="item in platformList"
                        :key="item.label"
                        :label="item.label"
                        :value="item.label">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="购买型号" prop="model">
                  <el-input type="text" v-model="form.model" placeholder="请输入" clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="订单号" prop="orderNumber">
                  <el-input type="text" v-model="form.orderNumber" placeholder="请输入" clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="国家" prop="countryCode" >
                  <el-select v-model="form.countryCode" placeholder="请选择" clearable filterable >
                    <el-option
                        v-for="item in countryList"
                        :key="item.countryCode"
                        :label="item.countryName"
                        :value="item.countryCode">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="状态" prop="status" >
                  <el-select v-model="form.status" placeholder="请选择" clearable filterable >
                    <el-option
                        v-for="item in statusList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row type="flex">
              <el-col :span="10">
                <el-form-item label=" ">
                  <el-button type="primary" @click="searchForm"><i class="el-icon-search"> 查询 </i></el-button>
                  <el-button type="primary" @click="resetForm"><i class="el-icon-refresh"> 重置 </i></el-button>
                  <download-file ref="downloadFileComponent" :options="exportParams" @update-params="updateParams">
                    导出
                  </download-file>
                  <el-button type="primary" @click="batch">批量发放</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
      <!-- 表格 -->
      <div class="page__table-box">
        <!-- table -->
        <el-row>
          <el-col :span="24">
            <el-table :data="tableData" border stripe size="small">
              <el-table-column
                  align="center"
                  show-overflow-tooltip
                  header-align="center"
                  type="index">
              </el-table-column>
              <el-table-column
                  align="center"
                  show-overflow-tooltip
                  prop="email"
                  label="登记邮箱" >
              </el-table-column>
              <el-table-column
                  align="center"
                  show-overflow-tooltip
                  prop="platform"
                  label="购买平台" >
              </el-table-column>
              <el-table-column
                  align="center"
                  show-overflow-tooltip
                  prop="otherPlatform"
                  label="其他购买平台" >
                <template slot-scope="{row}">
                  {{ row.platform === 'Others' ? row.otherPlatform : '--' }}
                </template>
              </el-table-column>
              <el-table-column
                  align="center"
                  show-overflow-tooltip
                  prop="orderNumber"
                  label="订单号" >
              </el-table-column>
              <el-table-column
                  align="center"
                  show-overflow-tooltip
                  prop="model"
                  label="购买产品型号" >
              </el-table-column>
              <el-table-column
                  align="center"
                  show-overflow-tooltip
                  prop=""
                  label="订单金额" >
                <template slot-scope="{row}">
                  {{ row.currency }}{{ row.amount }}
                </template>
              </el-table-column>
              <el-table-column
                  align="center"
                  show-overflow-tooltip
                  prop="status"
                  label="状态"
                  width="100px"
                  header-align="center">
                <template slot-scope="{row}">
                  <el-tag type="primary" v-if="row.status === 1">{{ "待审核" }}</el-tag>
                  <el-tag type="success" v-if="row.status === 2">{{ "已发放" }}</el-tag>
                  <el-tag type="danger" v-if="row.status === 3">{{ "不发放" }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column
                  align="center"
                  show-overflow-tooltip
                  prop="comment"
                  label="备注" >
              </el-table-column>
              <el-table-column
                  align="center"
                  show-overflow-tooltip
                  prop="verify"
                  label="系统校验" >
              </el-table-column>
              <el-table-column
                  align="center"
                  show-overflow-tooltip
                  prop=""
                  label="国家" >
                <template slot-scope="{row}">
                  {{ getCountryInfo(row.countryCode).countryName }}
                </template>
              </el-table-column>
              <el-table-column
                  align="center"
                  label="操作"
                  min-width="150px">
                <template slot-scope="{row}">
                  <el-button size="mini" type="danger" @click="withdraw(row.id)" v-if="row.status === 2">撤回</el-button>
                  <el-button size="mini" type="primary" @click="issue(row)" v-if="row.status !== 2">发放</el-button>
                  <el-button size="mini" type="danger" @click="notIssue(row)" v-if="row.status === 1">不发放</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!--  pagination -->
            <el-pagination @size-change="sizeChangeHandle"
                           @current-change="currentChangeHandle"
                           :current-page="form.pageNo"
                           :page-sizes="form.pageSizes"
                           :page-size="form.pageSize"
                           :total="form.total"
                           layout="total, sizes, prev, pager, next, jumper">
            </el-pagination>
          </el-col>
        </el-row>
      </div>
      <issue ref="issue" @success="getTableData"></issue>
      <file ref="file" @success="getTableData"></file>
    </div>
  </div>
</template>
<script>


module.exports = {
  components: {
    "issue": httpVueLoader("/BluBucksC/exchangeRegistration/issue.vue"),
    'download-file': httpVueLoader('/webjars/plugins/bluetti-components/exportAndDownloadFile.vue'),
    'file': httpVueLoader('/BluBucksC/exchangeRegistration/file.vue')
  },
  data: function () {
    return {
      form: {
        email: '',
        platform: '',
        model: '',
        orderNumber: '',
        countryCode: '',
        status: '',
        pageNo: 1,
        pageSize: 10,
        total: 0,
        pageSizes: [10, 20, 50, 100],
      },
      platformList: [],
      countryList: [],
      exportParams: {
        method: 'post',
        apiUrl: $.api($.apiModule.bluBucksCenter, '/mgt/pointsExchangeRegistration/export'),
        params: this.form,
        fileName: 'exportData.xlsx',
        icon: 'el-icon-download',
        size: 'mini',
      },
      statusList: [
        {
          label: '待审核',
          value: '1',
        },
        {
          label: '已发放',
          value: '2',
        },
        {
          label: '不发放',
          value: '3',
        }
      ],
      tableData: [],
      loading: null,
    }
  },
  mounted() {
    this.$nextTick(function () {
      this.getTableData()
      this.getCountryList()
      this.getPlatformList()
    })
  },
  methods: {
    getTableData() {
      const params = Object.assign(this.form, {})
      const url = $.api($.apiModule.bluBucksCenter, "/mgt/pointsExchangeRegistration/page")
      this.getLoading()
      win.OkHttp.post(url, params).then(res => {
        this.loading.close()
        if(res.msgCode === 0 && res.data) {
          this.tableData = res.data.content
          this.form.total = res.data.totalElements * 1
        }
      }).catch(err => {
        this.loading.close()
        this.$message.error("获取表格数据失败")
      });
    },
    getCountryList() {
      const url = $.api($.apiModule.midpMasterData, "~/iso/country/v1/localized/all-countries")
      this.getLoading()
      win.OkHttp.get(url).then(res => {
        this.loading.close()
        if(res.msgCode === 0 && res.data) {
          res.data.countries.forEach(item => {
            let obj = {
              countryName: item.countryName,
              countryCode: item.countryCode,
              countryId: item.geoNameId,
            }
            this.countryList.push(obj)
          })
        }
      }).catch(err => {
        this.loading.close()
        this.$message.error("获取国家数据失败")
      });
    },
    getPlatformList() {
      const url = $.api($.apiModule.bluBucksCenter, "/bucksApp/pointsExchangeByOrder/v1/platformList")
      this.getLoading()
      win.OkHttp.get(url).then(res => {
        this.loading.close()
        if(res.msgCode === 0 && res.data) {
          res.data.forEach(item => {
            let obj = {
              label: item
            }
            this.platformList.push(obj)
          })
        }
      }).catch(err => {
        this.loading.close()
        this.$message.error("获取平台列表数据失败")
      });
    },
    getCountryInfo(countryCode) {
      let info = {}
      this.countryList.forEach(item => {
        if(item.countryCode === countryCode) {
          info = item
        }
      });
      return info
    },
    updateParams() {
      this.exportParams.params = Object.assign({}, this.form)
    },
    getLoading() {
      this.loading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      })
    },
    searchForm() {
      this.form.pageNo = 1
      this.getTableData()
    },
    resetForm() {
      this.$refs.form.resetFields()
      this.form.pageNo = 1
      this.getTableData()
    },
    batch() {
      this.$refs.file.showDialog()
    },
    withdraw(id) {
      this.$confirm('是否确定删除该活动?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const path = `/mgt/pointsExchangeRegistration/retract?id=${id}`
        const url = $.api($.apiModule.bluBucksCenter,path)
            win.OkHttp.put(url).then(res => {
          if (res.msgCode === 0) {
            this.$message.success("删除成功")
            this.getTableData();
          }
        })
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    issue(data) {
      let countryInfo = this.getCountryInfo(data.countryCode)
      Object.assign(data, countryInfo)
      this.$refs.issue.showDialog('issue', data)
    },
    notIssue(data) {
      this.$refs.issue.showDialog('notIssue', data)
    },
    currentChangeHandle(val) {
      this.form.pageNo = val
      this.getTableData()
    },
    // 每页数
    sizeChangeHandle(val) {
      this.form.pageSize = val
      this.form.pageNo = 1
      this.getTableData()
    },
  },
}
</script>

<style>
</style>
