<template>
  <div>

    <el-card class="box-card" shadow="none">
      <el-form :model="searchForm" ref="form" :inline="true" size="mini" label-width="110px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="日期" prop="dates">
              <el-date-picker
                  v-model="searchForm.dates"
                  type="daterange"
                  :picker-options="pickerOptions"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd"
                  align="right">
              </el-date-picker>
            </el-form-item>
          </el-col>


          <el-col :span="6">
            <el-form-item label="渠道" prop="channelCode" label-width="110px">
              <el-select v-model="searchForm.channelCode" placeholder="请选择" clearable filterable
                         @change="getActivity()">
                <el-option
                    v-for="item in channel"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>


          <el-col :span="6">
            <el-form-item label="活动" prop="activityId" label-width="110px">
              <el-select filterable clearable v-model="searchForm.activityId" placeholder="请选择"
                         @change="changeActivity()">
                <el-option
                    v-for="item in activity"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

        </el-row>


        <el-row type="flex">
          <el-col :span="10">
            <el-form-item label=" ">
              <el-button type="primary" @click="search()">查询</el-button>
              <el-button type="primary" @click="resetForm()">重置</el-button>
              <el-button type="warning" @click="doExport()">导出</el-button>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
    </el-card>


    <el-row>
      <el-col :span="24">
        <el-table :data="dataList" border stripe size="small">
          <el-table-column align="center" show-overflow-tooltip header-align="center" type="index"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="days" label="日期"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="channelName" label="渠道名称"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="activityName" label="活动名称"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="type1Points" label="发放积分"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="type1Num" label="发放人数"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="type2Points" label="消耗积分"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="type2Num" label="消耗人数"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="type3Points" label="回收积分"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="type3Num" label="回收人数"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="ratio" label="消耗比"></el-table-column>

        </el-table>

        <el-pagination @size-change="sizeChangeHandle"
                       @current-change="currentChangeHandle"
                       :current-page="searchForm.pageNo"
                       :page-sizes="[10, 20, 50, 100]"
                       :page-size="searchForm.pageSize"
                       :total="searchForm.totalPage"
                       layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>
      </el-col>
    </el-row>


  </div>
</template>
<script>


module.exports = {
  components: {},
  data: function () {
    return {
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      activity: [],
      channel: [],
      dataList: [],
      searchForm: {
        pageNo: 1,
        pageSize: 10,
        totalPage: 0,
        dates: [],
        activityId: '',
        channelCode: '',
      },
    }
  },
  methods: {


    getList() {
      win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/pointsJournalByDays/pageList"), this.searchForm).then(res => {
        if (res.msgCode == 0) {
          this.dataList = res.data.content;
          this.searchForm.totalPage = Number(res.data.totalElements)
        }
      })
    },
    search() {
      this.searchForm.pageNo = 1;
      this.getList();
    },
    currentChangeHandle(val) {
      this.searchForm.pageNo = val
      this.getList()
    },
    // 每页数
    sizeChangeHandle(val) {
      this.searchForm.pageSize = val
      this.searchForm.pageNo = 1
      this.getList()
    },
    resetForm() {
      this.$refs.form.resetFields();
      this.searchForm.activityId = '';
      this.searchForm.channelCode = ''
      this.searchForm.countryCode = ''
      this.getList();
      this.getChannel();
      this.getActivity();
    },
    getChannel() {
      win.OkHttp.get($.api($.apiModule.bluBucksCenter, "/mgt/channel/list")).then(res => {
        if (res.msgCode == 0) {
          this.channel = res.data;
        }
      })
    },

    getActivity() {
      this.searchForm.activityId = ''
      win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/activity/getList"), this.searchForm).then(res => {
        if (res.msgCode == 0) {
          this.activity = res.data;
        }
      })
    },
    doExport() {
      function createObjectURL(object) {
        return (window.URL) ? window.URL.createObjectURL(object) : window.webkitURL.createObjectURL(object);
      }

      let _that = this;
      let xhr = new XMLHttpRequest();
      let startTime = '';
      let endTime = '';
      if (this.searchForm.dates.length > 0) {
        startTime = this.searchForm.dates[0];
        endTime = this.searchForm.dates[1];
      }

      let params = "channelCode=" + this.searchForm.channelCode + "&activityId=" + this.searchForm.activityId + "&startTime=" + startTime + "&endTime=" + endTime;
      xhr.open('get', $.api($.apiModule.bluBucksCenter, "/mgt/pointsJournalByDays/export?" + params));
      xhr.responseType = 'blob';
      xhr.setRequestHeader("Authorization", win.SsoUtil.getAccessToken());
      xhr.setRequestHeader("x-app-key", $.apiClient);
      xhr.setRequestHeader("x-os", "web");
      xhr.onload = function (e) {
        if (this.status == 200) {
          var blob = this.response;
          if (blob.type == 'application/json') {
            _that.$message.warning("数据为空");
          } else {
            var filename = "daysStaticsPoints.xls";
            if (window.navigator.msSaveOrOpenBlob) {
              navigator.msSaveBlob(blob, filename);
            } else {
              var a = document.createElement('a');
              var url = createObjectURL(blob);
              a.href = url;
              a.download = filename;
              document.body.appendChild(a);
              a.click();
              window.URL.revokeObjectURL(url);
            }
          }
        }
      };
      xhr.send();
    },
    changeActivity() {
      this.activity.forEach(a => {
        if (a.id === this.searchForm.activityId) {
          this.searchForm.channelCode = a.channelCode;
          this.searchForm.countryCode = a.country;
        }
      })
    },
  },


  mounted() {
    this.$nextTick(function () {
      this.getList();
      this.getChannel();
      this.getActivity();
    })
  }
}
</script>
<style>


</style>