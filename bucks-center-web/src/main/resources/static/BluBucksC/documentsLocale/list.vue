<template>
  <div>
    <el-card class="box-card" shadow="none">
      <el-form :model="searchForm" ref="form" :inline="true" size="mini" label-width="110px">
        <el-row>

          <el-col :span="8">
            <el-form-item label="语种名称" prop="localeName">
              <el-input type="text" v-model="searchForm.localeName" placeholder="文案名称" clearable></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="语种代码" prop="localeCode">
              <el-input type="text" v-model="searchForm.localeCode" placeholder="文案代码" clearable></el-input>
            </el-form-item>
          </el-col>


        </el-row>


        <el-row type="flex">
          <el-col :span="10">
            <el-form-item label=" ">
              <el-button type="primary" @click="search()">查询</el-button>
              <el-button type="primary" @click="resetForm()">重置</el-button>
              <el-button type="primary" v-has="'188667e4359f791157564c2ec7f'" @click="add()">新增</el-button>
            </el-form-item>
          </el-col>
        </el-row>


      </el-form>

    </el-card>
    <el-row>
      <el-col :span="24">
        <el-table :data="dataList" border stripe  size="small">
          <el-table-column align="center" show-overflow-tooltip header-align="center" type="index"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="localeName" label="语种名称" width="500"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="localeCode" label="语种代码" width="500"></el-table-column>

          <el-table-column align="center" label="操作" min-width="200px">
            <template slot-scope="scope">
              <el-button size="mini" type="primary" v-has="'188668d6038f791157564c2ec84'" @click="edit( scope.row)">编辑</el-button>
              <el-button size="mini" type="danger"  v-has="'1886691974cf791157564c2ec85'" @click="remove( scope.row)">删除</el-button>
            </template>
          </el-table-column>

        </el-table>

        <el-pagination @size-change="sizeChangeHandle"
                       @current-change="currentChangeHandle"
                       :current-page="searchForm.pageNo"
                       :page-sizes="[10, 20, 50, 100]"
                       :page-size="searchForm.pageSize"
                       :total="searchForm.totalPage"
                       layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>


      </el-col>
    </el-row>

    <el-row>
      <el-col>
        <add ref="add" @success="getList()"/>
      </el-col>
    </el-row>
    <el-row>
      <el-col>
        <edit ref="edit" @success="getList()"/>
      </el-col>
    </el-row>

  </div>
</template>
<script>


module.exports = {
  components: {
    "add": httpVueLoader("/BluBucksC/documentsLocale/add.vue"),
    "edit": httpVueLoader("/BluBucksC/documentsLocale/edit.vue"),


  },
  data: function () {
    return {
      searchForm: {
        pageNo: 1,
        pageSize: 10,
        totalPage: 0,
        localeName: '',
        localeCode: ''
      },
      dataList: [],
    }
  },

  methods: {
    search() {
      this.searchForm.pageNo = 1;
      this.getList();
    },
    getList() {
      win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/DocumentsLocale/page"), this.searchForm).then(res => {
        if (res.msgCode == 0) {
          this.dataList = res.data.content;
          this.searchForm.totalPage = Number(res.data.totalElements)
        }
      })
    },

    remove(row) {
      this.$confirm('是否确定删除该语种?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        win.OkHttp.get($.api($.apiModule.bluBucksCenter, '/mgt/DocumentsLocale/delete/') + row.id).then(res => {
          if (res.msgCode == 0) {
            this.$message.success("删除成功")
            this.searchForm.pageNo = 1
            this.getList();
          }
        })
      }).catch(() => {
        this.$message.info('已取消删除');
      });


    },


    currentChangeHandle(val) {
      this.searchForm.pageNo = val
      this.getList()
    },
    // 每页数
    sizeChangeHandle(val) {
      this.searchForm.pageSize = val
      this.searchForm.pageNo = 1
      this.getList()
    },

    edit(row) {
      this.$refs.edit.visible(row);
    },

    add() {
      this.$refs.add.visible(true);
    },

    resetForm() {
      this.$refs.form.resetFields();
      this.getList();

    },


  },
  mounted() {
    this.$nextTick(function () {
      this.getList();
    })
  }
}
</script>