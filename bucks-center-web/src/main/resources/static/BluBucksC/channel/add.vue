<template>
  <div>
    <el-dialog :title="title" center :visible.sync="dialogVisible" width="40%" :close-on-click-modal="false"
               @close="resetForm('form')">
      <el-form ref="form" :rules="formRules" :model="form" :inline="true" size="mini">






        <el-row>

          <el-col :span="11">
            <el-form-item label="渠道类型" prop="channelType" label-width="110px">
              <el-select v-model="form.type" placeholder="请选择" @change="changeType()">
                <el-option
                    v-for="item in typeList"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          </el-row>
        <el-row>

          <el-col :span="11">
            <el-form-item label="来源渠道名称" prop="name" label-width="110px">
              <el-select v-model="form.name" placeholder="请选择" clearable filterable @change="changeName()">
                <el-option
                    v-for="item in list"
                    :key="item.appKey"
                    :label="item.appName"
                    :value="item.appName">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

        </el-row>
        <el-row>

          <el-col :span="11">
            <el-form-item label="来源渠道标识" prop="code" label-width="110px" >
              <el-input class="input1" v-model="form.code" clearable disabled width="120px"></el-input>
            </el-form-item>
          </el-col>

        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" content="">
        <el-button type="primary" @click="submit">新增</el-button>
        <el-button @click="resetForm('form')">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
const typeList=[{code:'1',name:'应用标识'}];

module.exports = {
  components: {},
  data: function () {
    return {
      list:[],
      dialogVisible: false,
      title: "新增来源渠道",
      loading: null,
      request:{
        name: "",
        pageNo: 1,
        pageSize: 10000,
        roleCode: "",
        appKey:''
      },
      form: {
        type:'1',
        name: '',
        code: ''

      },
      typeList:typeList,
      formRules: {
        name: [
          {required: true, message: '必须输入来源渠道名称', trigger: '[change,blur]'}
        ],
        code: [
          {required: true, message: '必须输入来源渠道标识', trigger: '[change,blur]'}
        ],
        type:[
          {required: true, message: '必须选择渠道类型', trigger: '[change,blur]'}
        ]
      }
    }
  },
  methods: {
    changeName(){
      if(this.form.name===''||this.form.name===null){
        this.form.code=''
      }


      this.list.forEach(a => {
        if(a.appName===this.form.name){
          this.form.code=a.appKey;
        }
      })
    },
    getApp(){
      win.OkHttp.post($.api($.apiModule.midpAuthCenter, '~/application/list'), this.request).then(res=>{
            if (res.msgCode == 0) {
              this.list=res.data.content;
            }
      })
    },

    changeType(){

      if(this.form.channelType==='1'){
        this.getApp();
        this.$refs['form'].resetFields();
        this.form.channelType='1'
      }
    },

    loadingFunc() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },
    visible() {
      this.getApp();
      this.dialogVisible = true;
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.dialogVisible = false;
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {

          this.$confirm('是否确定新增?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(()=>{
            this.loadingFunc();
            win.OkHttp.post($.api($.apiModule.bluBucksCenter, '/mgt/channel/add'), this.form).then(res => {
              this.loading.close();
              if (res.msgCode == 0) {
                this.$message.success("新增成功");
                this.resetForm("form");
                this.$emit("success");
              }
            }).catch(e => {
              this.loading.close();
            })
          }).catch(() => {
            this.$message.info('已取消新增');
          });


        } else {
          return false;
        }
      })
    }


  }
}
</script>
