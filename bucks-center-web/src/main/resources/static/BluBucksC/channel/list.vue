<template>
  <div>
    <el-card class="box-card" shadow="none">
      <el-form :model="searchForm" ref="form" :inline="true" size="mini" label-width="110px">
        <el-row>

          <el-col :span="8">
            <el-form-item label="来源渠道名称" prop="name">
              <el-input type="text" v-model="searchForm.name" placeholder="来源渠道名称" clearable></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="来源渠道标识" prop="code">
              <el-input type="text" v-model="searchForm.code" placeholder="来源渠道标识" clearable></el-input>
            </el-form-item>
          </el-col>


          <el-col :span="8">
            <el-form-item label="类型" prop="type" label-width="110px">
              <el-select v-model="searchForm.type" placeholder="请选择" clearable filterable>
                <el-option
                    v-for="item in type"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>


        </el-row>


        <el-row type="flex">
          <el-col :span="10">
            <el-form-item label=" ">
              <el-button type="primary" @click="search()">查询</el-button>
              <el-button type="primary" @click="resetForm()">重置</el-button>
              <el-button type="primary" @click="add()" v-has="'18205899764f79115229d154a64'">新增</el-button>
            </el-form-item>
          </el-col>
        </el-row>


      </el-form>

    </el-card>
    <el-row>
      <el-col :span="24">
        <el-table :data="dataList" border stripe size="small">
          <el-table-column align="center" show-overflow-tooltip header-align="center" type="index"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="name" label="来源渠道名称"
                           width="500"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="code" label="来源渠道标识"
                           width="500"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="type" label="类型"
                           :formatter="typeFormat"></el-table-column>

          <el-table-column align="center" label="操作" min-width="200px">
            <template slot-scope="scope">
              <el-button size="mini" type="primary" @click="edit( scope.row)" v-has="'182058aa01bf79115229d154a66'">编辑
              </el-button>
              <el-button size="mini" type="danger" @click="remove( scope.row)" v-has="'18239a07cebf79115229d154adf'" v-if="scope.row.type!==2">删除
              </el-button>
            </template>
          </el-table-column>

        </el-table>

        <el-pagination @size-change="sizeChangeHandle"
                       @current-change="currentChangeHandle"
                       :current-page="searchForm.pageNo"
                       :page-sizes="[10, 20, 50, 100]"
                       :page-size="searchForm.pageSize"
                       :total="searchForm.totalPage"
                       layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>


      </el-col>
    </el-row>

    <el-row>
      <el-col>
        <add ref="add" @success="getList()"/>
      </el-col>
    </el-row>
    <el-row>
      <el-col>
        <edit ref="edit" @success="getList()"/>
      </el-col>
    </el-row>

  </div>
</template>
<script>
const typeList = [{code: 1, name: '应用标识'}, {code: 2, name: '店铺'}]

module.exports = {
  components: {
    "add": httpVueLoader("/BluBucksC/channel/add.vue"),
    "edit": httpVueLoader("/BluBucksC/channel/edit.vue"),


  },
  data: function () {
    return {
      type: typeList,

      typeList: typeList,
      searchForm: {
        pageNo: 1,
        pageSize: 10,
        totalPage: 0,
        name: '',
        code: '',
        type: '',
      },
      dataList: [],
    }
  },

  methods: {

    typeFormat(row) {
      if (row.type === null || row.type === '') return row.type;
      let result = null;
      this.typeList.forEach(function (item) {
        if (item.code === row.type) {
          result = item.name;
        }
      })
      return result;
    },
    search() {
      this.searchForm.pageNo = 1;
      this.getList();
    },
    getList() {
      win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/channel/pageList"), this.searchForm).then(res => {
        if (res.msgCode == 0) {
          this.dataList = res.data.content;
          this.searchForm.totalPage = Number(res.data.totalElements)
        }
      })
    },

    remove(row) {
      this.$confirm('是否确定删除该来源渠道?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        win.OkHttp.get($.api($.apiModule.bluBucksCenter, '/mgt/channel/delete/') + row.id).then(res => {
          if (res.msgCode == 0) {
            this.$message.success("删除成功")
            this.searchForm.pageNo = 1
            this.getList();
          }
        })
      }).catch(() => {
        this.$message.info('已取消删除');
      });


    },


    currentChangeHandle(val) {
      this.searchForm.pageNo = val
      this.getList()
    },
    // 每页数
    sizeChangeHandle(val) {
      this.searchForm.pageSize = val
      this.searchForm.pageNo = 1
      this.getList()
    },

    edit(row) {
      this.$refs.edit.visible(row);
    },

    add() {
      this.$refs.add.visible(true);
    },

    resetForm() {
      this.$refs.form.resetFields();
      this.getList();

    },


  },
  mounted() {
    this.$nextTick(function () {
      this.getList();
    })
  }
}
</script>