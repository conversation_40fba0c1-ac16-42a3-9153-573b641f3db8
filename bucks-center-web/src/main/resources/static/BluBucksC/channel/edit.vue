<template>
  <div>
    <el-dialog :title="title" center :visible.sync="dialogVisible" width="40%" :close-on-click-modal="false"
               @close="resetForm('form')">
      <el-form ref="form" :rules="formRules" :model="form" :inline="true" size="mini" label-width="110px">
        <el-row>
          <el-col :span="11">
            <el-form-item label="来源渠道名称" prop="name" >
              <el-input class="input1" v-model="form.name" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="来源渠道标识" prop="code" >
              <el-input class="input1" v-model="form.code" clearable disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" content="">
        <el-button type="primary" @click="submit">修改</el-button>
        <el-button @click="resetForm('form')">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
module.exports = {
  components: {},
  data: function () {
    return {
      title: "编辑来源渠道",
      dialogVisible: false,

      loading: null,
      form: {
        name: '',
        code: ''
      },
      formRules: {
        name: [
          {required: true, message: '必须输入来源渠道名称', trigger: 'blur'}
        ],
        code: [
          {required: true, message: '必须输入来源渠道标识', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {

    loadingFunc() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },

    visible(row) {
      this.dialogVisible = true;
      win.OkHttp.get($.api($.apiModule.bluBucksCenter, "/mgt/channel/get/") + row.id).then(res => {
        if (res.msgCode == 0) {
          this.form = res.data
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.dialogVisible = false;
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {

          this.$confirm('是否确定进行修改?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(()=>{
            this.loadingFunc();
            win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/channel/update"), this.form).then(res => {
              this.loading.close();
              if (res.msgCode == 0) {
                this.$message.success("修改成功");
                this.resetForm("form");
                this.$emit("success");
              }else{
                this.$message.error(res.message);
              }

            }).catch(e => {
              this.loading.close();
            })
          }).catch(() => {
            this.$message.info('已取消修改');
          });






        } else {
          return false;
        }
      })
    }
  }
}
</script>
<style>

</style>