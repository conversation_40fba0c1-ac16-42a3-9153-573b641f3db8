<template>
  <div>
    <el-card class="box-card" shadow="none">
      <el-form :model="searchForm" ref="form" :inline="true" size="mini" label-width="110px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="国家/地区" prop="countryId" >
              <el-select v-model="searchForm.countryId" placeholder="请选择" clearable filterable >
                <el-option
                    v-for="item in countryList"
                    :key="item.geoNameId"
                    :label="item.countryName +'/'+ item.countryCode"
                    :value="item.geoNameId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="用户邮箱" prop="email">
              <el-input type="text" v-model="searchForm.email" placeholder="用户邮箱" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账户注册时间" prop="creTime">
              <el-date-picker
                v-model="creTimeRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                @change="handleCreTimeChange"
                clearable>
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="累计发放" prop="earn">
              <el-input v-model="searchForm.earnMin" placeholder="最小值" type="number" style="width: 100px;"></el-input>
              <span style="margin: 0 5px;">-</span>
              <el-input v-model="searchForm.earnMax" placeholder="最大值" type="number" style="width: 100px;"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="累计消耗" prop="spend">
              <el-input v-model="searchForm.spendMin" placeholder="最小值" type="number" style="width: 100px;"></el-input>
              <span style="margin: 0 5px;">-</span>
              <el-input v-model="searchForm.spendMax" placeholder="最大值" type="number" style="width: 100px;"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="剩余积分" prop="balance">
              <el-input v-model="searchForm.balanceMin" placeholder="最小值" type="number" style="width: 100px;"></el-input>
              <span style="margin: 0 5px;">-</span>
              <el-input v-model="searchForm.balanceMax" placeholder="最大值" type="number" style="width: 100px;"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex">
          <el-col :span="10">
            <el-form-item label=" ">
              <el-button type="primary" @click="search()">查询</el-button>
              <el-button type="primary" @click="resetForm()">重置</el-button>
              <el-button type="warning" @click="doExport()">导出</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-row>
      <el-col :span="24">
        <el-table :data="dataList" border stripe  size="small">
          <el-table-column align="center" show-overflow-tooltip header-align="center" type="index"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="username" label="用户昵称" ></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="email" label="用户邮箱" ></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="countryId" label="国家/地区" :formatter="countryFormat"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="earn" label="累积发放" ></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="spend" label="累积消耗" ></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="balance" label="剩余积分" ></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="amount" label="消费金额" ></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="currency" label="币种" ></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="isInBlackList" label="是否在黑名单" width="100px" header-align="center">
            <template slot-scope="scope">
              <el-tag type="danger" v-if="scope.row.isInBlackList === true">{{ "是" }}</el-tag>
              <el-tag type="success" v-if="scope.row.isInBlackList === false">{{ "否" }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" min-width="200px">
            <template slot-scope="scope">
              <el-button size="mini" type="primary" @click="findDetail( scope.row)" v-has="'182053178c0f79115229d154a61'">查看明细</el-button>
              <el-button size="mini" type="primary" @click="join( scope.row)" v-if="scope.row.isInBlackList===false" v-has="'182053297f7f79115229d154a62'">加入黑名单</el-button>
              <el-button size="mini" type="primary" @click="out( scope.row)" v-if="scope.row.isInBlackList===true" v-has="'1820589df51f79115229d154a65'">移出黑名单</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination @size-change="sizeChangeHandle"
                       @current-change="currentChangeHandle"
                       :current-page="searchForm.pageNo"
                       :page-sizes="[10, 20]"
                       :page-size="searchForm.pageSize"
                       :total="searchForm.totalPage"
                       layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>
      </el-col>
    </el-row>

    <add ref="add" @success="getList"></add>
    <edit ref="edit" @success="getList"></edit>
    <detail ref="detail" @success="getList"></detail>
    <join ref="joinPage" @success="getList"></join>
  </div>
</template>
<script>

module.exports = {
  name: 'pointsBalancePage',
  components: {
    "detail": httpVueLoader("/BluBucksC/balance/detail.vue"),
    "add": httpVueLoader("/BluBucksC/balance/add.vue"),
    "edit": httpVueLoader("/BluBucksC/balance/edit.vue"),
    "join":httpVueLoader("/BluBucksC/balance/joinPage.vue")
  },
  data: function () {
    return {
      searchForm: {
        uname:'',
        countryId: '',
        email:'',
        // 新增筛选条件
        creTimeStart: '',
        creTimeEnd: '',
        earnMin: '',
        earnMax: '',
        spendMin: '',
        spendMax: '',
        balanceMin: '',
        balanceMax: '',
        pageNo: 1,
        pageSize: 10,
        totalPage: 0,
      },
      dataList: [],
      countryList:[],
      outForm:{},
      creTimeRange: [], // 账户注册时间范围
    }
  },
  mounted() {
    this.$nextTick(_ => {
      this.getList();
    })
  },
  methods: {
    // 获取表格内容
    getList() {
      win.OkHttp.get($.api($.apiModule.midpMasterData, "~/iso/country/v1/localized/all-countries")).then(res => {
        if (+res.msgCode === 0) {
          this.countryList = res.data.countries || [];
        }
      })

      // 处理搜索参数，将空字符串的数字字段设为null
      const searchParams = Object.assign({}, this.searchForm);
      if (searchParams.earnMin === '') searchParams.earnMin = null;
      if (searchParams.earnMax === '') searchParams.earnMax = null;
      if (searchParams.spendMin === '') searchParams.spendMin = null;
      if (searchParams.spendMax === '') searchParams.spendMax = null;
      if (searchParams.balanceMin === '') searchParams.balanceMin = null;
      if (searchParams.balanceMax === '') searchParams.balanceMax = null;

      win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/pointsBalance/pageList"), searchParams).then(res => {
        if (+res.msgCode === 0) {
          this.dataList = res.data.content || [];
          this.searchForm.totalPage = Number(res.data.totalElements)
        }
      })
    },

    // 行操作
    out(row){
      this.$confirm('是否确认将此用户移出黑名单', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.outForm.uid=row.uid;
        this.outForm.countryId=row.countryId;
        win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/blacklist/out"),this.outForm).then(res => {
          if (+res.msgCode === 0) {
            this.$message.success("操作成功");
            this.getList();
          }else{
            this.$message.error(res.message);
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
      });
    },
    join(row){
      this.$refs.joinPage.visible(row);
    },
    countryFormat(row) {
      if ( row.countryId === null || row.countryId === '') return '';
      let result = null;
      this.countryList.forEach(function (item) {
        if (item.geoNameId === row.countryId) {
          result = item.countryName;
        }
      })
      return result;
    },
    findDetail(row){
      this.$refs.detail.visible(row);
    },
    doExport() {
      function createObjectURL(object) {
        return (window.URL) ? window.URL.createObjectURL(object) : window.webkitURL.createObjectURL(object);
      }
      let _that = this;
      let xhr = new XMLHttpRequest();
      let params = "uname=" + this.searchForm.uname + "&countryId=" + this.searchForm.countryId;
      xhr.open('get', $.api($.apiModule.bluBucksCenter, "/mgt/pointsBalance/export?" + params));
      xhr.responseType = 'blob';
      xhr.setRequestHeader("Authorization", win.SsoUtil.getAccessToken());
      xhr.setRequestHeader("x-app-key", $.apiClient);
      xhr.setRequestHeader("x-os", "web");
      xhr.onload = function (e) {
        if (this.status === 200) {
          let blob = this.response;
          if (blob.type === 'application/json') {
            _that.$message.warning("数据为空");
          } else {
            let filename = "balancePoints.xls";
            if (window.navigator.msSaveOrOpenBlob) {
              navigator.msSaveBlob(blob, filename);
            } else {
              let a = document.createElement('a');
              let url = createObjectURL(blob);
              a.href = url;
              a.download = filename;
              document.body.appendChild(a);
              a.click();
              window.URL.revokeObjectURL(url);
            }
          }
        }
      };
      xhr.send();
    },
    // remove(row) {
    //   this.$confirm('是否确定删除该来源渠道?', '提示', {
    //     confirmButtonText: '确定',
    //     cancelButtonText: '取消',
    //     type: 'warning'
    //   }).then(() => {
    //     win.OkHttp.get($.api($.apiModule.bluBucksCenter, '/mgt/pointsBalance/delete/') + row.id).then(res => {
    //       if (res.code == 0) {
    //         this.$message.success("删除成功")
    //         this.searchForm.pageNo = 1
    //         this.getList();
    //       }
    //     })
    //   }).catch(() => {
    //     this.$message.info('已取消删除');
    //   });
    //
    //
    // },

    // 处理账户注册时间范围变化
    handleCreTimeChange(value) {
      if (value && value.length === 2) {
        this.searchForm.creTimeStart = value[0];
        this.searchForm.creTimeEnd = value[1];
      } else {
        this.searchForm.creTimeStart = '';
        this.searchForm.creTimeEnd = '';
      }
    },

    // 表单操作
    search() {
      this.searchForm.pageNo = 1;
      this.getList();
    },
    edit(row) {
      this.$refs.edit.visible(row);
    },
    add() {
      this.$refs.add.visible(true);
    },
    resetForm() {
      this.$refs.form.resetFields();
      // 重置新增的筛选条件
      this.creTimeRange = [];
      this.searchForm.creTimeStart = '';
      this.searchForm.creTimeEnd = '';
      this.searchForm.earnMin = '';
      this.searchForm.earnMax = '';
      this.searchForm.spendMin = '';
      this.searchForm.spendMax = '';
      this.searchForm.balanceMin = '';
      this.searchForm.balanceMax = '';
      this.getList();
    },

    // 分页操作
    currentChangeHandle(val) {
      this.searchForm.pageNo = val
      this.getList()
    },
    sizeChangeHandle(val) {
      this.searchForm.pageSize = val
      this.searchForm.pageNo = 1
      this.getList()
    },
  },
}
</script>
<style>


</style>
