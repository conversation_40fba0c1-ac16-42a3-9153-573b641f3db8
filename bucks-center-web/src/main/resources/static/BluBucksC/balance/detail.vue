<template>
  <el-dialog :title="title" center :visible.sync="dialogVisible" width="70%">
    <el-form :model="form" ref="form" :inline="true" size="mini" label-width="110px">
      <el-row>
        <el-col :span="8">
          <el-form-item label="来源渠道" prop="channelCode">
            <el-select v-model="form.channelCode" placeholder="请选择" clearable filterable>
              <el-option
                  v-for="item in channelList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.code">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="渠道活动" prop="activityId">
            <el-select v-model="form.activityId" placeholder="请选择" clearable filterable>
              <el-option
                  v-for="item in activityList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label=" ">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button type="primary" @click="reset">重置</el-button>
            <export ref="exportFile" :options="exportParams" @update-params="updateParams">导出</export>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-row>
      <el-col :span="24">
        <el-table :data="tableData" border stripe size="small">
          <el-table-column align="center" show-overflow-tooltip header-align="center" type="index"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="channelName" label="来源渠道"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="activityName" label="渠道活动"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="countryId" label="国家/地区"
                           :formatter="countryFormat"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="points" label="积分数"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="amount" label="消费金额"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="currency" label="币种"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="genType" label="类型"
                           :formatter="statusFormat1"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="desc" label="描述"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="genTime" label="时间"
                           width="200px"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="orderId" label="业务单号"></el-table-column>
        </el-table>
        <el-pagination @size-change="sizeChangeHandle"
                       @current-change="currentChangeHandle"
                       :current-page="form.pageNo"
                       :page-sizes="[10, 20, 50, 100]"
                       :page-size="form.pageSize"
                       :total="form.totalPage"
                       layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>
      </el-col>
    </el-row>
  </el-dialog>
</template>

<script>
const ruleDataList1 = [{code: 1, name: "发放"}, {code: 2, name: "消耗"}, {code: 3, name: "回退"}, {code: 4, name: "清零"}];
module.exports = {
  name: 'pointsBalanceDetail',
  components: {
    'export': httpVueLoader('/webjars/plugins/bluetti-components/exportAndDownloadFile.vue'),
  },
  data() {
    return {
      title: '用户积分余额变动明细',
      dialogVisible: false,
      form: {
        channelCode: '',
        activityId: '',
        pageNo: 1,
        pageSize: 10,
        totalPage: 0,
      },
      tableData: [],
      countryList: [],
      channelList: [],
      activityList: [],
      exportParams: {
        method: 'post',
        apiUrl: $.api($.apiModule.bluBucksCenter, '/mgt/pointsBalance/exportDetail'),
        params: this.form,
        fileName: '用户积分详情导出.xlsx',
        icon: '',
        size: "mini",
      }
    }
  },
  methods: {
    // 初始化
    visible(row) {
      this.form.pageNo = 1
      this.form.uid = row.uid
      this.form.countryId = row.countryId;
      this.dialogVisible = true;
      this.getCountries()
      this.getChannels()
      this.getActivities()
      this.getList()
    },
    getCountries() {
      win.OkHttp.get($.api($.apiModule.midpMasterData, "~/iso/country/v1/localized/all-countries")).then(res => {
        if (+res.msgCode === 0) {
          this.countryList = res.data.countries || [];
        }
      })
    },
    getChannels() {
      win.OkHttp.get($.api($.apiModule.bluBucksCenter, "/mgt/channel/existsList")).then(res => {
        if (+res.msgCode === 0) {
          this.channelList = res.data || []
        }
      })
    },
    getActivities() {
      const params = {
        pageNo: 1,
        pageSize: 999
      }
      win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/activity/pageList"), params).then(res => {
        if (+res.msgCode === 0) {
          this.activityList = res.data.content || []
        }
      })
    },
    getList() {
      win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/pointsBalance/getDetailByUid"), this.form).then(res => {
        if (+res.msgCode === 0) {
          this.tableData = res.data.content || []
          this.form.totalPage = Number(res.data.totalElements)
        }
      })
    },

    // 行格式化
    statusFormat1(row) {
      if (row.genType === null || row.genType === '') return row.genType;
      let data = ruleDataList1.filter(item => item.code === row.genType)[0];
      if (data != null) {
        return data.name;
      }
      return "";
    },
    countryFormat(row) {
      if (row.countryId === null || row.countryId === '') return row.countryId;
      let data = this.countryList.filter(item => item.geoNameId === row.countryId)[0];
      if (data != null) {
        return data.countryName;
      }
      return "";
    },

    // 行操作
    search() {
      this.form.pageNo = 1
      this.getList()
    },
    reset() {
      Object.assign(this.form, {
        channelCode: '',
        activityId: '',
        pageNo: 1,
        pageSize: 10,
        totalPage: 0,
      },)
      this.getList()
    },
    updateParams() {
      this.exportParams.params = Object.assign({}, this.form);
    },

    // 分页
    currentChangeHandle(val) {
      this.form.pageNo = val
      this.getList()
    },
    sizeChangeHandle(val) {
      this.form.pageSize = val
      this.form.pageNo = 1
      this.getList()
    },
  }
}
</script>
<style>


</style>
