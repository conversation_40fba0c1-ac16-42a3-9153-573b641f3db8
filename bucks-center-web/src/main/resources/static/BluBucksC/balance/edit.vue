<template>
  <div>
    <el-dialog :title="title" center :visible.sync="dialogVisible" width="40%" :close-on-click-modal="false"
               @close="resetForm('form')">
      <el-form ref="form" :rules="formRules" :model="form" :inline="true" size="mini" label-width="125px">
        <el-row>
          <el-col>
            <el-form-item label="用户id" prop="uid">
              <el-input class="input1" v-model="form.uid" clearable></el-input>
            </el-form-item>
          </el-col>


          <el-col>
            <el-form-item label="国家/地区" prop="countryId">
              <el-select v-model="form.countryId" placeholder="请选择" clearable filterable>
                <el-option
                    v-for="item in countryList"
                    :key="item.geoNameId"
                    :label="item.countryName"
                    :value="item.geoNameId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>


        </el-row>


        <el-row>
          <el-col>
            <el-form-item label="累积发放" prop="earn">
              <el-input-number class="input1" v-model="form.earn"></el-input-number>
            </el-form-item>
          </el-col>

          <el-col>
            <el-form-item label="累积消耗" prop="spend">
              <el-input-number class="input1" v-model="form.spend"></el-input-number>
            </el-form-item>
          </el-col>


          <el-col>
            <el-form-item label="余额" prop="balance">
              <el-input-number class="input1" v-model="form.balance"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
      <div slot="footer" class="dialog-footer" content="">
        <el-button type="primary" @click="submit">修改</el-button>
        <el-button @click="resetForm('form')">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>


module.exports = {
  components: {},
  data: function () {
    return {
      form: {},
      loading: null,
      dialogVisible: false,
      title: "编辑用户",
      countryList: [],
      formRules: {
        uid: [
          {required: true, message: '必须输入用户id', trigger: 'blur'}
        ],
        countryId: [
          {required: true, message: '必须输入国家编码', trigger: ["blur", 'change']}
        ],
        earn: [
          {required: true, message: '必须输入累积发放', trigger: 'blur'},

        ],
        spend: [
          {required: true, message: '必须输入累积消耗', trigger: 'blur'},

        ],
        balance: [
          {required: true, message: '必须输入余额', trigger: 'blur'},

        ],

      },


    }
  },
  methods: {
    loadingFunc() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },

    visible(row) {

      win.OkHttp.get($.api($.apiModule.midpMasterData, "~/iso/country/v1/localized/all-countries")).then(res => {
        if (res.msgCode == 0) {
          this.countryList = res.data.countries;
        }
      })



      this.dialogVisible = true;
      win.OkHttp.get($.api($.apiModule.bluBucksCenter, "/mgt/pointsBalance/get/") + row.id).then(res => {
        if (res.msgCode == 0) {
          this.form = res.data
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.dialogVisible = false;
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$confirm('是否确定进行修改?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.loadingFunc();
            win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/pointsBalance/update"), this.form).then(res => {
              this.loading.close();
              if (res.msgCode == 0) {
                this.$message.success("修改成功");
                this.resetForm("form");
                this.$emit("success");
              } else {
                this.$message.error(res.message);
              }

            }).catch(e => {
              this.loading.close();
            })
          }).catch(() => {
            this.$message.info('已取消修改');
          });


        } else {
          return false;
        }
      })
    }


  }
}
</script>
<style>


</style>