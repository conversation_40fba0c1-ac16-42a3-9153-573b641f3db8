<template>
  <div>
    <el-dialog :title="title" center :visible.sync="dialogVisible" width="40%" :close-on-click-modal="false"
               @close="close2">
      <el-form ref="form" :rules="formRules" :model="form" :inline="true" size="mini" label-width="125px">
        <el-row>


          <el-col>
            <el-form-item label="用户">
              <el-input class="input1" v-model="uname" disabled></el-input>
            </el-form-item>
            <el-button type="primary" @click="select" size="small">选择</el-button>
          </el-col>

          <el-col>
            <el-form-item prop="uid" style="display: none">
              <el-input class="input1" v-model="form.uid"></el-input>
            </el-form-item>
          </el-col>


          <el-col>
            <el-form-item label="国家/地区" prop="countryId">
              <el-select v-model="form.countryId" placeholder="请选择" clearable filterable>
                <el-option
                    v-for="item in countryList"
                    :key="item.geoNameId"
                    :label="item.countryName"
                    :value="item.geoNameId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>


        </el-row>


        <el-row>
          <el-col>
            <el-form-item label="累积发放" prop="earn">
              <el-input-number class="input1" v-model="form.earn"
              ></el-input-number>
            </el-form-item>
          </el-col>

          <el-col>
            <el-form-item label="累积消耗" prop="spend">
              <el-input-number class="input1" v-model="form.spend"
              ></el-input-number>
            </el-form-item>
          </el-col>


          <el-col>
            <el-form-item label="余额" prop="balance">
              <el-input-number class="input1" v-model="form.balance"
              ></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
      <div slot="footer" class="dialog-footer" content="">
        <el-button type="primary" @click="submit">新 增</el-button>
        <el-button @click="resetForm('form')">取 消</el-button>
      </div>
    </el-dialog>


    <el-dialog :title="title" center :visible.sync="userVisible" width="60%" :close-on-click-modal="false"
               @close="close1()">

      <el-card class="box-card" shadow="none">
        <el-form :model="searchForm.member" ref="form1" :inline="true" size="mini" label-width="110px">
          <el-row>
            <el-col :span="8">
              <el-form-item label="用户昵称" prop="nickName">
                <el-input type="text" v-model="searchForm.member.nickName" placeholder="用户昵称" clearable></el-input>
              </el-form-item>
            </el-col>


            <el-col :span="8">
              <el-form-item label="用户手机" prop="phone">
                <el-input type="text" v-model="searchForm.member.phone" placeholder="用户手机" clearable></el-input>
              </el-form-item>
            </el-col>


            <el-col :span="8">
              <el-form-item label="用户邮箱" prop="email">
                <el-input type="text" v-model="searchForm.member.email" placeholder="用户邮箱" clearable></el-input>
              </el-form-item>
            </el-col>


          </el-row>


          <el-row type="flex">
            <el-col :span="10">
              <el-form-item label=" ">
                <el-button type="primary" @click="search()">查询</el-button>
                <el-button type="primary" @click="reset1('form1')">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <div style="overflow: auto;height: 480px">
        <el-row>
          <el-col :span="24">
            <el-table :data="users" border stripe size="small" @row-click="rowClick">
              <el-table-column align="center" show-overflow-tooltip header-align="center"
                               type="index"></el-table-column>
              <!--            <el-table-column align="center" show-overflow-tooltip prop="username" label="用户账号" ></el-table-column>-->
              <el-table-column align="center" show-overflow-tooltip prop="nickname" label="用户昵称"></el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="phone" label="用户手机"></el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="email" label="用户邮箱"></el-table-column>
              <!--            <el-table-column align="center" show-overflow-tooltip prop="country" label="国家" ></el-table-column>-->
            </el-table>

            <el-pagination @size-change="sizeChangeHandle"
                           @current-change="currentChangeHandle"
                           :current-page="searchForm.pageNo"
                           :page-sizes="[10, 20, 50, 100]"
                           :page-size="searchForm.pageSize"
                           :total="searchForm.totalPage"
                           layout="total, sizes, prev, pager, next, jumper">
            </el-pagination>


          </el-col>
        </el-row>
      </div>

      <div slot="footer" class="dialog-footer" content="">

        <el-button @click="close1()">取 消</el-button>
      </div>
    </el-dialog>


  </div>
</template>
<script>


module.exports = {
  components: {},
  data: function () {
    return {
      countryList: [],
      searchForm: {
        pageNo: 1,
        pageSize: 10,
        totalPage: 0,
        member: {
          nickName: '',
          phone: '',
          email: ''
        }
      },
      users: [],
      loading: null,
      form: {
        earn: 0,
        spend: 0,
        balance: 0
      },
      lastClick : undefined,
      uname: '',
      dialogVisible: false,
      title: "新增用户",
      userVisible: false,
      formRules: {
        uid: [
          {required: true, message: '必须输入用户id', trigger: 'blur'}
        ],
        countryId: [
          {required: true, message: '必须选择国家/地区', trigger: ["blur", 'change']}
        ],
        earn: [
          {required: true, message: '必须输入累积发放', trigger: 'blur'},

        ],
        spend: [
          {required: true, message: '必须输入累积消耗', trigger: 'blur'},

        ],
        balance: [
          {required: true, message: '必须输入余额', trigger: ["blur", 'change']},

        ],

      },
    }
  },
  methods: {
    close1() {
      this.searchForm={
        pageNo: 1,
        pageSize: 10,
        totalPage: 0,
        member: {
          nickName:'',
          phone:'',
          email:''
        }
      }
      this.getUsers()
      this.userVisible = false
    },
    select() {
      this.userVisible = true
    },

    lockClick(){
      var nowClick = new Date();
      if (this.lastClick === undefined) {
        this.lastClick = nowClick;
        return true;
      } else {
        if (Math.round((nowClick.getTime() - this.lastClick.getTime())) > 500) {
          this.lastClick = nowClick;
          return true;
        }
        else {
          this.lastClick = nowClick;
          return false
        }
      }
    },



    rowClick(row) {
      if (this.lockClick()) {
        this.uname = row.nickname;
        this.form.uid = row.uid
        this.userVisible = false;
      }
    },
    getUsers() {
      win.OkHttp.post($.api($.apiModule.bluUserCenter, "~/uc/basic/list"), this.searchForm).then(res => {
        if (res.msgCode == 0) {
          this.users = res.data.content;
          this.searchForm.totalPage = Number(res.data.totalElements)
        }
      })
      // this.users=[{"uid":"181eb1556006f8a3c663710bff6","username":null,"nickname":"145","avatar":null,"phone":null,"email":"<EMAIL>","company":null,"country":null,"additions":{},"nation":null,"city":null,"address":null,"extraInfoMap":{},"sex":null,"realName":null,"birth":null,"age":null,"status":1,"id":"181eb1556006f8a3c663710bff6","testerFlag":0},{"uid":"181eab4785b6f8a3c663710bff3","username":null,"nickname":"1162266425","avatar":null,"phone":null,"email":"<EMAIL>","company":null,"country":null,"additions":{},"nation":null,"city":null,"address":null,"extraInfoMap":{},"sex":null,"realName":null,"birth":null,"age":null,"status":1,"id":"181eab4785b6f8a3c663710bff3","testerFlag":0},{"uid":"181e24a5acf6f8a3c663710bff1","username":null,"nickname":"423","avatar":null,"phone":null,"email":"<EMAIL>","company":null,"country":null,"additions":{},"nation":null,"city":null,"address":null,"extraInfoMap":{},"sex":null,"realName":null,"birth":null,"age":null,"status":1,"id":"181e24a5acf6f8a3c663710bff1","testerFlag":0},{"uid":"181e1a7ad356f8a3c663710bfef","username":null,"nickname":"213","avatar":null,"phone":null,"email":"<EMAIL>","company":null,"country":null,"additions":{},"nation":null,"city":null,"address":null,"extraInfoMap":{},"sex":null,"realName":null,"birth":null,"age":null,"status":1,"id":"181e1a7ad356f8a3c663710bfef","testerFlag":0},{"uid":"181dcfac8e56f8a3c663710bfe8","username":null,"nickname":"254","avatar":null,"phone":null,"email":"<EMAIL>","company":null,"country":null,"additions":{},"nation":null,"city":null,"address":null,"extraInfoMap":{},"sex":null,"realName":null,"birth":null,"age":null,"status":1,"id":"181dcfac8e56f8a3c663710bfe8","testerFlag":0},{"uid":"181dbc2576d6f8a3c663710bfe4","username":null,"nickname":"guojia02","avatar":null,"phone":null,"email":"<EMAIL>","company":null,"country":null,"additions":{},"nation":null,"city":null,"address":null,"extraInfoMap":{},"sex":null,"realName":null,"birth":null,"age":null,"status":1,"id":"181dbc2576d6f8a3c663710bfe4","testerFlag":0},{"uid":"181dbc18b926f8a3c663710bfe2","username":null,"nickname":"guojia01","avatar":null,"phone":null,"email":"<EMAIL>","company":null,"country":null,"additions":{},"nation":null,"city":null,"address":null,"extraInfoMap":{},"sex":null,"realName":null,"birth":null,"age":null,"status":1,"id":"181dbc18b926f8a3c663710bfe2","testerFlag":0},{"uid":"181db6a41ed6f8a3c663710bfdf","username":null,"nickname":"guojia","avatar":null,"phone":null,"email":"<EMAIL>","company":null,"country":null,"additions":{},"nation":null,"city":null,"address":null,"extraInfoMap":{},"sex":null,"realName":null,"birth":null,"age":null,"status":1,"id":"181db6a41ed6f8a3c663710bfdf","testerFlag":0},{"uid":"181d84a8f186f8a3c663710bfdb","username":null,"nickname":"qwgfqp","avatar":null,"phone":null,"email":"<EMAIL>","company":null,"country":null,"additions":{},"nation":null,"city":null,"address":null,"extraInfoMap":{},"sex":null,"realName":null,"birth":null,"age":null,"status":1,"id":"181d84a8f186f8a3c663710bfdb","testerFlag":0},{"uid":"181d7f2907d6f8a3c663710bfd9","username":null,"nickname":"723","avatar":null,"phone":null,"email":"<EMAIL>","company":null,"country":null,"additions":{},"nation":null,"city":null,"address":null,"extraInfoMap":{},"sex":null,"realName":null,"birth":null,"age":null,"status":1,"id":"181d7f2907d6f8a3c663710bfd9","testerFlag":0}]
    },
    search() {
      this.searchForm.pageNo = 1;
      this.getUsers()
    },
    currentChangeHandle(val) {
      this.searchForm.pageNo = val
      this.getUsers()
    },
    sizeChangeHandle(val) {
      this.searchForm.pageSize = val
      this.searchForm.pageNo = 1
      this.getUsers()
    },
    loadingFunc() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },

    visible() {


      win.OkHttp.get($.api($.apiModule.midpMasterData, "~/iso/country/v1/localized/all-countries")).then(res => {
        if (res.msgCode == 0) {
          this.countryList = res.data.countries;
        }
      })

      this.getUsers();
      this.dialogVisible = true;
    },

    close2() {
      this.uname = ''
      this.form.uid = ''
      this.resetForm('form')
    },
    reset1(formName) {
      this.$refs[formName].resetFields();
      this.visible();
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.dialogVisible = false;
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$confirm('是否确定新增?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.loadingFunc();
            win.OkHttp.post($.api($.apiModule.bluBucksCenter, '/mgt/pointsBalance/add'), this.form).then(res => {
              this.loading.close();
              if (res.msgCode == 0) {
                this.$message.success("新增成功");
                this.resetForm("form");
                this.$emit("success");
              }
            }).catch(e => {
              this.loading.close();
            })
          }).catch(() => {
            this.$message.info('已取消新增');
          });


        } else {
          return false;
        }
      })
    }
  }
}
</script>
<style>


</style>