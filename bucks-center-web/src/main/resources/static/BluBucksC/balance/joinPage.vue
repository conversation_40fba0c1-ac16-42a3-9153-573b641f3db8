<template>
  <div>
    <el-dialog :title="title" center :visible.sync="dialogVisible" width="40%" :close-on-click-modal="false"
               @close="resetForm('form')">
      <el-form ref="form"  :model="form" :inline="true" size="mini" label-width="125px">
        <el-row>
          <el-col >
            <el-form-item label="加入黑名单原因" prop="reason" clearable>
              <el-input class="input1" v-model="form.reason"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" content="">
        <el-button type="primary" @click="submit">确 认</el-button>
        <el-button @click="resetForm('form')">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>


module.exports = {
  components: {},
  data: function () {
    return {
      dialogVisible:false,
      title: "是否确认加入黑名单",
      form:{

      },

    }
  },
  methods: {
    visible(row){
      this.dialogVisible=true
      this.form.uid=row.uid
      this.form.countryId=row.countryId
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.dialogVisible = false;
    },
    submit(){

      this.$confirm('是否确认将此用户添加至黑名单', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {



        win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/blacklist/join"), this.form).then(res => {
          if (res.msgCode == 0) {
            this.$message.success("加入成功");
            this.resetForm("form");
            this.$emit("success");
          }else{
            this.$message.error(res.message);
          }
        })


      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
      });

    },

    submitJoin(){
      console.log(this.form)
    }
  }
}
</script>
<style>


</style>