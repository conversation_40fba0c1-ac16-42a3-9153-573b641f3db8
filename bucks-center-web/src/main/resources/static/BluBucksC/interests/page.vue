<template>
  <div>
    <el-card class="box-card" shadow="none">
      <el-form :model="form" ref="form" :inline="true" size="mini" label-width="110px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="国家/地区" prop="countryId" >
              <el-select v-model="form.countryId" placeholder="请选择" clearable filterable >
                <el-option
                    v-for="item in countryList"
                    :key="item.geoNameId"
                    :label="item.countryName +'/'+ item.countryCode"
                    :value="item.geoNameId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex">
          <el-col :span="8">
            <el-form-item label=" ">
              <el-button type="primary" @click="search">查询</el-button>
              <el-button type="primary" @click="reset">重置</el-button>
              <el-button type="primary" @click="add">新增</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-row>
      <el-table :data="tableData" border stripe size="small" v-loading="tableLoading">
        <el-table-column align="center" show-overflow-tooltip header-align="center" type="index"></el-table-column>
        <el-table-column align="center" show-overflow-tooltip prop="countryName" label="国家/地区"></el-table-column>
        <el-table-column align="center" show-overflow-tooltip prop="name" label="权益名称"></el-table-column>
        <el-table-column align="center" show-overflow-tooltip prop="" label="权益图标">
          <template slot-scope="{row}">
            <img :src="row.iconUrl ? row.iconUrl : '/BluBucksC/assets/images/default.png'" alt="加载失败" class="dialog_table-img">
          </template>
        </el-table-column>
        <el-table-column align="center" show-overflow-tooltip prop="instructions" label="权益介绍"></el-table-column>
        <el-table-column align="center" show-overflow-tooltip prop="" label="有效期" width="400px">
          <template slot-scope="{row}">
            <span>[{{row.effectiveStart}}, {{row.effectiveEnd}}]</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作">
          <template slot-scope="{row}">
            <el-button size="mini" type="primary" @click="edit(row)">编辑</el-button>
            <el-button size="mini" type="primary" @click="del(row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination @size-change="sizeChangeHandle"
                     @current-change="currentChangeHandle"
                     :current-page="page.pageNo"
                     :page-sizes="[10, 20, 50, 100]"
                     :page-size="page.pageSize"
                     :total="page.totalPage"
                     layout="total, sizes, prev, pager, next, jumper">
      </el-pagination>
    </el-row>
    <add ref="handleAdd" @success="addCallback" :country-list="countryList"></add>
  </div>
</template>

<script>
module.exports = {
  name: "pointsInterestsPage",
  components: {
    "add": httpVueLoader('/BluBucksC/interests/add.vue')
  },
  data() {
    return {
      form: {
        countryId: '',
      },
      tableData: [],
      tableLoading: false,
      page: {
        pageNo: 1,
        pageSize: 10,
        totalPage: 0,
      },
      countryList: [],
    }
  },
  mounted() {
    this.getCountries()
    this.getTable()
  },
  methods: {
    // 初始化
    getCountries() {
      win.OkHttp.get($.api($.apiModule.midpMasterData, "~/iso/country/v1/localized/all-countries")).then(res => {
        if (+res.msgCode === 0) {
          this.countryList = res.data.countries || [];
        }
      })
    },
    // 获取表格内容
    getTable() {
      const params = Object.assign({}, this.form, {
        pageNo: this.page.pageNo,
        pageSize: this.page.pageSize
      })
      win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/pointsInterests/page"), params).then(res => {
        if (+res.msgCode === 0) {
          this.tableData = res.data.content || [];
          this.page.totalPage = Number(res.data.totalElements)
          if((!res.data.content || !res.data.content.length) && this.page.pageNo > 1) {
            this.page.pageNo --
            this.page.pageNo <= 0 && (this.page.pageNo = 1)
            this.getTable()
          }
        }
      })
    },

    // 表单操作
    search() {
      this.page.pageNo = 1
      this.getTable()
    },
    reset() {
      Object.assign(this.form, {
        countryId: '',
      })
      this.page.pageNo = 1
      this.getTable()
    },
    add() {
      this.$refs.handleAdd.open(0)
    },
    addCallback() {
      this.getTable()
    },

    // 行操作
    edit(row) {
      this.$refs.handleAdd.open(1, row)
    },
    del(id) {
      this.$confirm('此操作将删除该行数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tableLoading = true
        win.OkHttp.delete($.api($.apiModule.bluBucksCenter, `/mgt/pointsInterests/delete?id=${id}`))
            .then(res => {
              this.tableLoading = false
              if(+res.msgCode === 0) {
                this.getTable()
                this.$message({
                  type: 'success',
                  message: '删除成功!'
                });
              }
            })
            .catch(e => {this.tableLoading = false})
      }).catch(() => {});
    },

    // 分页
    sizeChangeHandle(_pageSize) {
      this.page.pageSize = _pageSize
      this.page.pageNo = 1
      this.getTable()
    },
    currentChangeHandle(_pageNo) {
      this.page.pageNo = _pageNo
      this.getTable()
    }
  }
}
</script>

<style scoped>
.dialog_table-img {
  width: 50px;
  height: 50px;
  border-radius: 12px;
}
</style>
