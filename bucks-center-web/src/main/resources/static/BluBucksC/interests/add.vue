<template>
  <el-dialog :title="curMode.title" center :visible.sync="dialogVisible" width="40%" :close-on-click-modal="false"
             @close="close">
    <el-form :model="form" ref="form" :rules="rules" size="mini" label-position="left" label-width="110px">
      <el-form-item label="国家地区" prop="countryId">
        <el-select v-model="form.countryId" placeholder="请选择" clearable filterable>
          <el-option
              v-for="item in countryList"
              :key="item.geoNameId"
              :label="item.countryName +'/'+ item.countryCode"
              :value="item.geoNameId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="权益名称" prop="name">
        <el-input type="text" v-model="form.name" placeholder="请输入" clearable></el-input>
      </el-form-item>
      <el-form-item label="有效期" prop="time">
        <el-date-picker
            v-model="time"
            type="datetimerange"
            :default-time="['00:00:00', '23:59:59']"
            range-separator="-"
            value-format="yyyy-MM-dd HH:mm:ss"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="updateTime">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="附件上传" prop="iconUrl">
        <file-upload
            v-if="dialogVisible"
            ref="handleFile"
            :limit="1"
            class="file-upload"
            :file-list="fileList"
            @change="updateFileList">
        </file-upload>
      </el-form-item>
      <el-form-item label="权益介绍" prop="instructions">
        <el-input v-model="form.instructions" placeholder="请输入" type="textarea" maxlength="500"
                  show-word-limit></el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer" content="">
      <el-button type="primary" @click="submit">保 存</el-button>
      <el-button @click="close">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
module.exports = {
  name: 'pointsInterestsAdd',
  components: {
    "file-upload": httpVueLoader("/BluBucksC/components/file-upload.vue"),
  },
  props: {
    countryList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: false,
      mode: [
        {title: '新增权益', path: '/mgt/pointsInterests/add', method: 'post'},
        {title: '编辑权益', path: '/mgt/pointsInterests/edit', method: 'put'},
      ],
      curMode: {},
      curModeIndex: 0,
      form: {
        countryId: '',
        effectiveStart: '',
        effectiveEnd: '',
        iconUrl: '',
        instructions: '',
        name: '',
      },
      fileList: [],
      time: [],
      rules: {
        countryId: [
          {required: true, message: '请选择国家/地区', trigger: 'blur'}
        ],
        name: [
          {
            required: true, message: '请输入权益名称', trigger: 'blur',
            validator: (rule, val, callback) => {
              if (!val.trim()) {
                callback(new Error('请输入权益名称'))
              }
              else {
                callback()
              }
            }
          }
        ],
        time: [
          {
            required: true, message: '请输入有效期起止日期', trigger: 'blur',
            validator: (rule, val, callback) => {
              if (this.form.effectiveStart && this.form.effectiveEnd) {
                callback()
              } else {
                callback(new Error('请输入有效期起止日期'))
              }
            }
          }
        ],
        iconUrl: [
          {
            required: true, message: '请上传附件', trigger: 'blur',
            validator: (rule, val, callback) => {
              if(!this.fileList[0] || !this.fileList[0].fileUrl) {
                callback(new Error('请上传附件'))
              }
              else {
                callback()
              }
            }
          }
        ],
        instructions: [
          {
            required: true, message: '请输入权益介绍', trigger: 'blur',
            validator: (rule, val, callback) => {
              if (!val.trim()) {
                callback(new Error('请输入权益介绍'))
              }
              else {
                callback()
              }
            }
          }
        ]
      }
    }
  },
  methods: {
    // 初始化弹窗
    open(curModeIndex = 0, data = {}) {
      this.curModeIndex = +curModeIndex
      this.curMode = this.mode[this.curModeIndex]
      if (this.curModeIndex === 1) {
        this.form = JSON.parse(JSON.stringify(data))
        if(this.form.iconUrl) {
          this.fileList = [{fileUrl: this.form.iconUrl}]
        }
        this.time = [this.form.effectiveStart, this.form.effectiveEnd]
      }
      this.dialogVisible = true
    },

    // 操作
    updateFileList(_list) {
      this.fileList = _list
    },
    updateTime() {
      this.form.effectiveStart = this.time[0] || ''
      this.form.effectiveEnd = this.time[1] || ''
    },

    // 关闭弹窗
    close() {
      Object.assign(this.form, {
        countryId: '',
        effectiveStart: '',
        effectiveEnd: '',
        iconUrl: '',
        instructions: '',
        name: '',
      })
      this.fileList = []
      this.time = []
      this.$refs.form.clearValidate()
      this.dialogVisible = false
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.form.iconUrl = this.fileList[0] ? this.fileList[0].fileUrl : ''
          const params = Object.assign({}, this.form)
          win.OkHttp[this.curMode.method]($.api($.apiModule.bluBucksCenter, this.curMode.path), params)
              .then(res => {
                this.$message.success(res.message)
                this.$emit('success')
                this.close()
              })
              .catch(err => {
              })
        }
      })
    },
  }
}
</script>
<style scoped>
.el-input__inner {
  width: 100%;
}

</style>
