<template>
  <div>
    <el-card class="box-card" shadow="none">
    <el-form :model="searchForm" ref="form" :inline="true" size="mini" label-width="110px">
      <el-row>

        <el-col :span="8">
          <el-form-item label="用户名称" prop="uname">
            <el-input type="text" v-model="searchForm.uname" placeholder="用户名称" clearable></el-input>
          </el-form-item>
        </el-col>


        <el-col :span="8">
          <el-form-item label="用户邮箱" prop="uemail">
            <el-input type="text" v-model="searchForm.uemail" placeholder="用户邮箱" clearable></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="国家/地区" prop="countryId">
            <el-select v-model="searchForm.countryId" placeholder="请选择" clearable filterable>
              <el-option
                  v-for="item in countryList"
                  :key="item.geoNameId"
                  :label="item.countryName +'/'+ item.countryCode"
                  :value="item.geoNameId">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

      </el-row>


      <el-row type="flex">
        <el-col :span="10">
          <el-form-item label=" ">
            <el-button type="primary" @click="search()">查询</el-button>
            <el-button type="primary" @click="resetForm()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>


    </el-form>
    </el-card>

    <el-row>
      <el-col :span="24">
        <el-table :data="dataList" border stripe size="small">
          <el-table-column align="center" show-overflow-tooltip header-align="center" type="index"></el-table-column>
<!--          <el-table-column align="center" show-overflow-tooltip prop="account" label="用户账户"></el-table-column>-->
          <el-table-column align="center" show-overflow-tooltip prop="uname" label="用户名称"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="uemail" label="用户邮箱"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="uphone" label="用户手机"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="countryId" label="国家/地区"
                           :formatter="countryFormat"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="reason" label="加入原因"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="joinTime" label="加入黑名单时间"></el-table-column>
          <el-table-column align="center" label="操作" min-width="100px">
            <template slot-scope="scope">
              <el-button size="mini" type="danger" @click="remove( scope.row)" v-has="'1820589df51f79115229d154a65'">移出黑名单</el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination @size-change="sizeChangeHandle"
                       @current-change="currentChangeHandle"
                       :current-page="searchForm.pageNo"
                       :page-sizes="[10, 20, 50, 100]"
                       :page-size="searchForm.pageSize"
                       :total="searchForm.totalPage"
                       layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>


      </el-col>
    </el-row>


  </div>
</template>
<script>


module.exports = {
  components: {},
  data: function () {
    return {
      searchForm: {
        pageNo: 1,
        pageSize: 10,
        totalPage: 0,
        uname: '',
        uemail: '',
        countryId:''
      },
      dataList: [],
      loading: null,
      countryList: [],
      request: {}


    }
  },
  methods: {

    countryFormat(row) {

      if (row.countryId === null || row.countryId === '') return '';
      return this.countryList.filter(item => item.geoNameId === row.countryId)[0].countryName;
    },

    loadingFunc() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },

    remove(row) {
      this.$confirm('是否确认将此用户移出黑名单', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.request.uid = row.uid
        this.request.countryId = row.countryId
        this.loadingFunc();
        win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/blacklist/out"), this.request).then(res => {
          this.loading.close();
          if (res.msgCode == 0) {
            this.$message.success("操作成功");
            this.getList();
          } else {
            this.loading.close();
            this.$message.error(res.message);
          }
        })

      }).catch(() => {

        this.$message({
          type: 'info',
          message: '已取消'
        });
      });


    },

    currentChangeHandle(val) {
      this.searchForm.pageNo = val
      this.getList()
    },
    // 每页数
    sizeChangeHandle(val) {
      this.searchForm.pageSize = val
      this.searchForm.pageNo = 1
      this.getList()
    },

    resetForm() {
      this.$refs.form.resetFields();
      this.getList();
    },

    search() {
      this.searchForm.pageNo = 1;
      this.getList();
    },
    getList() {

      win.OkHttp.get($.api($.apiModule.midpMasterData, "~/iso/country/v1/localized/all-countries")).then(res => {
        if (res.msgCode == 0) {
          this.countryList = res.data.countries;
        }
      })



      win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/blacklist/pageList"), this.searchForm).then(res => {
        if (res.msgCode == 0) {
          this.dataList = res.data.content;
          this.searchForm.totalPage = Number(res.data.totalElements)
        }
      })
    },


  },
  mounted() {
    this.$nextTick(function () {
      this.getList();
    })
  }
}
</script>
<style>


</style>