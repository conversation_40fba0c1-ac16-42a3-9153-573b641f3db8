<template>
  <div>
    <el-dialog :title="title" center :visible.sync="dialogVisible" width="650px" :close-on-click-modal="false"
               @close="resetForm('form')">
      <el-form ref="form" :rules="formRules" :model="form" :inline="true" size="mini" label-width="150px">
        <el-row>
          <el-form-item label="活动" prop="activityId">
            <el-select v-model="form.activityId" filterable placeholder="国家/地区-活动-渠道" clearable>
              <el-option
                  v-for="item in activityList"
                  :key="item.activityId"
                  :label="`${item.countryName}-${item.activityName}-${item.channelName}`"
                  :value="item.activityId">
              </el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>

          <el-form-item label=" " prop="earnLimit">
            <div>
              <el-input class="edit-input" value="累计天数" :disabled="true" size="small" style="width:100px"></el-input>
              <el-input class="edit-input" value="积分" :disabled="true" size="small" style="width:100px"></el-input>
              <el-input class="edit-input" value="额外奖励" :disabled="true" size="small" style="width:100px"></el-input>
            </div>
            <div>
              <el-input class="edit-input" :disabled="true" size="small" style="width:100px"></el-input>
              <el-input class="edit-input" placeholder="同步积分" onkeyup="this.value=this.value.replace(/[^\d]/g,'')" v-model="batchPoints" @input="updatePoints()"
                        size="small" style="width:100px"></el-input>
              <el-input class="edit-input" :disabled="true" size="small" style="width:100px"></el-input>
            </div>
            <div v-for="item in dayList">
              <input class="edit-input" :id="item.eid" name="earnLimit" :disabled="true"
                     onkeyup="this.value=this.value.replace(/[^\d]/g,'')" :value="item.show"
                     style="width:98px;height:32px;background-color: #F5F7FA;color: #C0C4CC;cursor: not-allowed;border-radius: 4px;border: 1px solid #DCDFE6;text-align: center">
              <input class="edit-input" :id="item.pid" name="points"  :value="item.points"
                     onkeyup="this.value=this.value.replace(/[^\d]/g,'')"
                     style="width:68px;height: 32px;;border-radius: 4px;border: 1px solid #DCDFE6;padding: 0 15px">
              <input class="edit-input" :id="item.extId" name="extraReward"
                     onkeyup="this.value=this.value.replace(/[^\d]/g,'')"
                     style="width:68px;height: 32px;;border-radius: 4px;border: 1px solid #DCDFE6;padding: 0 15px"
                     :value="item.extraReward">
            </div>
          </el-form-item>
        </el-row>
        <!--        <el-row>-->
        <!--          <el-form-item label="发放类型" prop="earnType">-->
        <!--            <el-select v-model="form.earnType" placeholder="请选择" @change="changeEarnType" clearable>-->
        <!--              <el-option-->
        <!--                  v-for="item in earnTypeList"-->
        <!--                  :key="item.id"-->
        <!--                  :label="item.name"-->
        <!--                  :value="item.id">-->
        <!--              </el-option>-->
        <!--            </el-select>-->
        <!--          </el-form-item>-->
        <!--        </el-row>-->
        <!--        <el-row>-->
        <!--          <el-form-item label="单用户每日次数" prop="dailyNum">-->
        <!--            <el-input class="input2" max="11" :disabled="dailyNumDisable"-->
        <!--                      onkeyup="this.value=this.value.replace(/[^\d]/g,'')"-->
        <!--                      v-model="form.dailyNum"></el-input>-->
        <!--          </el-form-item>-->
        <!--        </el-row>-->

        <!--        <el-row>-->
        <!--          <el-form-item label="状态" prop="enable">-->
        <!--            <el-select v-model="form.enable" placeholder="请选择" style="width:178px">-->
        <!--              <el-option label="启用" :value="1"></el-option>-->
        <!--              <el-option label="禁用" :value="0"></el-option>-->
        <!--            </el-select>-->
        <!--          </el-form-item>-->
        <!--        </el-row>-->
      </el-form>
      <div slot="footer" class="dialog-footer" content="">
        <el-button type="primary" @click="submit()">提 交</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
module.exports = {
  data: function () {
    return {
      dialogVisible: false,
      title: "编辑",
      form: {
        id: '',
        earnType: 2,
        extraReward: 0,
        earnLimit: '',
        activityId: '',
        dailyNum: '',
        enable: 1,
        ruleType: 1
      },
      dailyNumDisable: false,
      loading: null,
      earnTypeList: [{id: 1, name: '一次性'}, {id: 2, name: '无限次'}],
      activityList: [],
      dayList: [],
      batchPoints:'',
      formRules:
          {
            activityId: [
              {required: true, message: '活动不能为空', trigger: 'blur'}
            ],
            earnLimit: [
              {required: true, message: '签到天数不能为空', trigger: 'blur'}
            ],
            points: [
              {required: true, message: '积分数不能为空', trigger: 'blur'}
            ],
            earnType: [
              {required: true, message: '发放类型不能为空', trigger: 'blur'}
            ],
            dailyNum: [
              {required: true, message: '单用户每日次数不能为空', trigger: 'blur'}
            ],
            enable: [
              {required: true, message: '状态不能为空', trigger: 'blur'}
            ],
          }
    }
  },
  props: {},
  methods: {
    visible(row, activityList) {
      this.activityList = activityList;
      this.form = {...row};
      this.dialogVisible = true;
      this.changeEarnType();
      this.getList();
    },
    updatePoints() {
      let points = this.batchPoints.replace(/[^\d]/g,'');
      $("input[name='points']").each(function () {
        $(this).val(points);
      })
    },
    initDays(dataList) {
      //this.dayList = [];
      // dataList.forEach(function (item,index){
      //   let i = index + 1;
      //   this.dayList.push({
      //     id: item.id,
      //     show: "第" + i + "天",
      //     eid: item.id,//"earnLimit_" + i,
      //     pid: "points_" + i,
      //     extId: "extraReward_" + i,
      //     earnLimit: item.earnLimit,
      //     points: item.points,
      //     extraReward: item.extraReward,
      //   });
      // })
      this.dayList = [];
      for (let i = 1; i < 32; i++) {
        let item = dataList[i-1];
        this.dayList.push({
          id: item.id,
          show:  i + "天",
          eid: item.id,//"earnLimit_" + i,
          pid: "points_" + i,
          extId: "extraReward_" + i,
          earnLimit: item.earnLimit,
          points: item.points,
          extraReward: item.extraReward,
        });
      }
    },
    getList() {
      this.dataListLoading = true
      win.OkHttp.post($.api($.apiModule.bluBucksCenter, '/mgt/pointsEarnRule/getSignList/' + this.form.id)).then(res => {
        if (res.msgCode == 0) {
          //this.dayList = res.data;
          this.initDays(res.data)
        }
      })
      this.dataListLoading = false;
    },
    changeEarnType() {
      if (this.form.earnType == '1') {
        this.dailyNumDisable = true;
        this.form.dailyNum = 1;
      } else {
        this.dailyNumDisable = false;
      }
    },
    loadingFunc() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loadingFunc();
          if (!this.form.extraReward) {
            this.form.extraReward = 0;
          }
          let data = [];
          let actId = this.form.activityId;
          this.dayList.forEach(function (item,index) {
            let days = ++index;
            let points = $("#" + item.pid).val();
            if (!points) {
              points = 0;
            }
            let extraReward = $("#" + item.extId).val();
            if (!extraReward) {
              extraReward = 0;
            }
            let id = item.id;
            data.push({
              id: id,
              activityId: actId,
              points: points,
              earnLimit: days,
              extraReward: extraReward,
              dailyNum: 1,
              enable: 1,
              ruleType: 1,
              earnType: 2
            });
          });
          win.OkHttp.post($.api($.apiModule.bluBucksCenter, '/mgt/pointsEarnRule/update'), {"items":data}).then(res => {
            this.loading.close();
            if (res.msgCode == 0) {
              this.$message.success("保存成功")
              this.resetForm("form");
              this.$emit("success");
            }
          }).catch(e => {
            this.loading.close();
          })
        } else {
          return false;
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.dialogVisible = false;
    }
  }
}
</script>