<template>
  <div>

    <el-card class="box-card" shadow="none">
      <el-form ref="form" :model="form" :inline="true" size="mini" label-width="90px">
        <el-row :span="18">
          <el-col :span="18">
            <el-form-item prop="channelCode" label="来源渠道">
              <el-select v-model="form.channelCode" placeholder="请选择" clearable>
                <el-option
                    v-for="item in channelList"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item prop="country" label="国家/地区">
              <el-select v-model="form.countryId" placeholder="请选择" clearable>
                <el-option
                    v-for="item in countryList"
                    :key="item.geoNameId"
                    :label="item.countryName +'/'+ item.countryCode"
                    :value="item.geoNameId">
                </el-option>
              </el-select>
            </el-form-item>


            <el-form-item prop="activityId" label="活动" v-if="activityShow">
              <el-select v-model="form.activityId" placeholder="请选择" clearable>
                <el-option
                    v-for="item in activityList"
                    :key="item.activityId"
                    :label="item.activityName"
                    :value="item.activityId">
                </el-option>
              </el-select>
            </el-form-item>

          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label=" ">
              <el-button type="primary" @click="pageList()">查询</el-button>
              <el-button type="primary" @click="resetForm()">重置</el-button>
              <el-button type="primary" v-has="'18204fbe641f79115229d154a56'" @click="add()">新增</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-link type="warning" @click="tipsDialogVisible = true">帮助提示?</el-link>
        </el-row>
      </el-form>
    </el-card>
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="签到" name="1">
        <el-row>
          <el-col :span="24">
            <el-table :data="dataList" border stripe v-loading="dataListLoading" size="small">
              <el-table-column align="center" show-overflow-tooltip header-align="center"
                               type="index"></el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="channelName" label="来源渠道名称"></el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="channelCode" label="渠道编码"></el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="activityName" label="活动"></el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="country" label="国家/地区" :formatter="countryFormat"></el-table-column>
              <el-table-column align="center" label="操作">
                <template slot-scope="scope">
                  <el-button size="small" type="primary" v-has="'182050468dbf79115229d154a5d'" @click="edit(scope.row)">编辑</el-button>
                  <el-button size="small" type="danger" v-has="'18204fc1e89f79115229d154a57'" @click="remove(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="货币兑换" name="2">
        <el-row>
          <el-col :span="24">
            <el-table :data="dataList" border stripe v-loading="dataListLoading" size="small">
              <el-table-column align="center" show-overflow-tooltip header-align="center"
                               type="index"></el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="channelName" label="来源渠道名称"></el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="channelCode" label="渠道编码"></el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="activityName" label="活动"></el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="country" label="国家/地区" :formatter="countryFormat"></el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="earnLimit" label="累计消费额"></el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="points" label="兑换比例"></el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="extraReward" label="额外奖励"></el-table-column>
              <el-table-column align="center" label="操作">
                <template slot-scope="scope">
                  <el-button size="small" type="primary" v-has="'182050468dbf79115229d154a5d'" @click="edit(scope.row)">编辑</el-button>
                  <el-button size="small" type="danger" v-has="'18204fc1e89f79115229d154a57'" @click="remove(scope.row)">删除</el-button>>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="光伏发电" name="3">
        <el-row>
          <el-col :span="24">
            <el-table :data="dataList" border stripe v-loading="dataListLoading" size="small">
              <el-table-column align="center" show-overflow-tooltip header-align="center"
                               type="index"></el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="channelName" label="来源渠道名称"></el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="channelCode" label="渠道编码"></el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="activityName" label="活动"></el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="country" label="国家/地区" :formatter="countryFormat"></el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="earnLimit" label="兑换门槛"></el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="points" label="兑换比例"></el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="extraReward" label="额外奖励"></el-table-column>
              <el-table-column align="center" label="操作">
                <template slot-scope="scope">
                  <el-button size="small" type="primary" v-has="'182050468dbf79115229d154a5d'" @click="edit(scope.row)">编辑</el-button>
                  <el-button size="small" type="danger" v-has="'18204fc1e89f79115229d154a57'" @click="remove(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="通用" name="9">
        <el-row>
          <el-col :span="24">
            <el-table :data="dataList" border stripe v-loading="dataListLoading" size="small">
              <el-table-column align="center" show-overflow-tooltip header-align="center"
                               type="index"></el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="channelName" label="来源渠道名称"></el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="channelCode" label="渠道编码"></el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="activityName" label="活动"></el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="country" label="国家/地区" :formatter="countryFormat"></el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="points" label="发放个数"></el-table-column>
              <el-table-column align="center" label="操作">
                <template slot-scope="scope">
                  <el-button size="small" type="primary" v-has="'182050468dbf79115229d154a5d'" @click="edit(scope.row)">编辑</el-button>
                  <el-button size="small" type="danger" v-has="'18204fc1e89f79115229d154a57'" @click="remove(scope.row)">删除</el-button>>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>

    <el-pagination @size-change="sizeChangeHandle"
                   @current-change="currentChangeHandle"
                   :current-page="form.pageNo"
                   :page-sizes="[10, 20, 50, 100]"
                   :page-size="form.pageSize"
                   :total="form.totalPage"
                   layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>

    <el-row>
      <el-col>
        <sign-edit ref="signEdit" @success="getList()"/>
      </el-col>
    </el-row>
    <el-row>
      <el-col>
        <sign-add ref="signAdd" @success="getList()"/>
      </el-col>
    </el-row>
    <el-row>
      <el-col>
        <currency-edit ref="currencyEdit" @success="getList()"/>
      </el-col>
    </el-row>
    <el-row>
      <el-col>
        <currency-add ref="currencyAdd" @success="getList()"/>
      </el-col>
    </el-row>
    <el-row>
      <el-col>
        <comm-edit ref="commEdit" @success="getList()"/>
      </el-col>
    </el-row>
    <el-row>
      <el-col>
        <comm-add ref="commAdd" @success="getList()"/>
      </el-col>
    </el-row>
    <el-row>
      <el-col>
        <pv-edit ref="pvEdit" @success="getList()"/>
      </el-col>
    </el-row>
    <el-row>
      <el-col>
        <pv-add ref="pvAdd" @success="getList()"/>
      </el-col>
    </el-row>
    <el-row>
      <el-col>
        <el-dialog
            title="帮助提示"
            :visible.sync="tipsDialogVisible"
            width="30%">
          <div>
            1、等级和等级名称可为空。<br/>
            2、相同活动相同发放门槛规则只能存在一条。<br/>
            3、额外奖励会在用户第一次达到相应发放门槛时发放且只发放一次。<br/>
            4、一次性发放类型，同一个用户只会发放一次（永久）。<br/>
            5、新增和编辑时，点击”新增“和”删除“和按钮不会进行数据保存，点击提交后才会进行保存。
          </div>
          <span slot="footer" class="dialog-footer">
<!--            <el-button @click="tipsDialogVisible = false">取 消</el-button>-->
            <el-button type="primary" @click="tipsDialogVisible = false">确 定</el-button>
          </span>
        </el-dialog>
      </el-col>
    </el-row>
  </div>

</template>
<script>
module.exports = {
  components: {
    "sign-add": httpVueLoader("/BluBucksC/earnRule/signAdd.vue"),
    "sign-edit": httpVueLoader("/BluBucksC/earnRule/signEdit.vue"),
    "currency-add": httpVueLoader("/BluBucksC/earnRule/currencyAdd.vue"),
    "currency-edit": httpVueLoader("/BluBucksC/earnRule/currencyEdit.vue"),
    "comm-add": httpVueLoader("/BluBucksC/earnRule/commAdd.vue"),
    "comm-edit": httpVueLoader("/BluBucksC/earnRule/commEdit.vue"),
    "pv-add": httpVueLoader("/BluBucksC/earnRule/pvAdd.vue"),
    "pv-edit": httpVueLoader("/BluBucksC/earnRule/pvEdit.vue"),
  },
  data: function () {
    return {
      form: {
        pageNo: 1,
        pageSize: 10,
        totalPage: 0,
        ruleType: 1,
        activityId: "",
        channelCode: "",
        countryId: ""
      },
      tipsDialogVisible:false,
      activityShow: false,
      dataListLoading: false,
      dataList: [],
      activeName: '1',
      ruleTypeList: [],
      activityList: [],
      channelList: [],
      countryList: []
    }
  },
  methods: {
    countryFormat(row) {
      if ( row.countryId === null || row.countryId === '') return '';
      let data = this.countryList.filter(item => item.geoNameId === row.countryId)[0];
      if(data!= null){
        return  data.countryName+"("+data.countryCode+")";
      }
      return '';
    },
    getList() {
      this.dataListLoading = true
      win.OkHttp.post($.api($.apiModule.bluBucksCenter, '/mgt/pointsEarnRule/page'), this.form).then(res => {
        if (res.msgCode == 0) {
          this.dataList = res.data.content;
          this.form.totalPage = Number(res.data.totalElements);
        }
      })
      this.dataListLoading = false;
    },
    getChannelList() {
      win.OkHttp.get($.api($.apiModule.bluBucksCenter, '/mgt/channel/list')).then(res => {
        if (res.msgCode == 0) {
          this.channelList = res.data;
        }
      })
    },
    getCountryList() {
      win.OkHttp.get($.api($.apiModule.midpMasterData, "~/iso/country/v1/localized/all-countries")).then(res => {
        if (res.msgCode == 0) {
          this.countryList = res.data.countries;
        }
      })
    },
    getActivityList() {
        win.OkHttp.get($.api($.apiModule.bluBucksCenter, '/mgt/activity/commonList')).then(res => {
          if (res.msgCode == 0) {
            this.activityList = [];
            let data = res.data;
            if (data) {
              for (let i = 0; i < data.length; i++) {
                if(data[i].earnRule == this.activeName){
                  this.activityList.push(data[i]);
                }
              }
            }
            // for(let i=0; i<this.activityList.length; i++){
            //   for(let j=0;j<this.countryList.length;j++){
            //     if(this.activityList[i].countryCode===this.countryList[j].countryCode){
            //       this.activityList[i].countryName=this.countryList[j].countryCode
            //     }
            //   }
            // }

          }
        })
    },
    handleClick() {
      if (this.activeName == '1') {
        this.activityShow = false;
        this.form.activityId = '';
      } else {
        this.activityShow = true;
      }
      this.pageList();
      this.getActivityList();
    },
    pageList() {
      this.form.pageNo = 1;
      this.form.pageSize = 10;
      this.form.ruleType = this.activeName;
      this.getList();
    },
    // 当前页
    currentChangeHandle(val) {
      this.form.pageNo = val
      this.getList()
    },
    // 每页数
    sizeChangeHandle(val) {
      this.form.pageSize = val
      this.form.pageNo = 1
      this.getList()
    },
    edit(row) {
      if (this.activeName == '1') {
        this.$refs.signEdit.visible(row, this.activityList);
      } else if (this.activeName == '2') {
        this.$refs.currencyEdit.visible(row, this.activityList);
      } else if (this.activeName == '3') {
        this.$refs.pvEdit.visible(row, this.activityList);
      }else if (this.activeName == '9') {
        this.$refs.commEdit.visible(row, this.activityList);
      }
    },
    add() {
      if (this.activeName == '1') {
        this.$refs.signAdd.visible(this.activityList);
      } else if (this.activeName == '2') {
        this.$refs.currencyAdd.visible(this.activityList);
      }  else if (this.activeName == '3') {
        console.log(this.activityList);
        this.$refs.pvAdd.visible(this.activityList);
      }else if (this.activeName == '9') {
        this.$refs.commAdd.visible(this.activityList);
      }
    },
    remove(row) {
      this.$confirm('确认删除? ', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/pointsEarnRule/delete/" + row.id)).then(res => {
          if (res.msgCode == 0) {
            this.$message.success("删除成功")
            this.getList();
          }
        })
      }).catch(() => {
      });
    },

    enableFormatter(row, column) {
      const enable = row.enable
      if (enable == 0) {
        return '禁用'
      } else {
        return '启用'
      }
    },
    resetForm() {
      this.form.name = null;
      this.getList();
    },
  },
  mounted() {
    this.$nextTick(function () {
      this.pageList();
      this.getChannelList();
      this.getActivityList();
      this.getCountryList();
    })
  }
}
</script>