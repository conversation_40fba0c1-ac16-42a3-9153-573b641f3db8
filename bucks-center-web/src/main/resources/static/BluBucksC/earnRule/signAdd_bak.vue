<template>
  <div>
    <el-dialog :title="title" center :visible.sync="dialogVisible" width="750px" :close-on-click-modal="false"
               @close="resetForm('form')">
      <el-card class="box-card" shadow="none">
        <el-form ref="form" :rules="formRules" :model="form" :inline="true" size="mini" label-width="250px">
          <el-row>
            <el-form-item label="活动" prop="activityId">
              <el-select v-model="form.activityId" filterable placeholder="国家/地区-活动-渠道" clearable>
                <el-option
                    v-for="item in activityList"
                    :key="item.activityId"
                    :label="`${item.countryName}-${item.activityName}-${item.channelName}`"
                    :value="item.activityId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row>

            <el-form-item label="签到天数|积分|额外奖励：" prop="earnLimit">
              <div>
                <el-input class="edit-input" value="签到天数" :disabled="true" size="small" style="width:100px"></el-input>
                <el-input class="edit-input" value="积分" :disabled="true" size="small" style="width:100px"></el-input>
                <el-input class="edit-input" value="额外奖励" :disabled="true" size="small" style="width:100px"></el-input>
              </div>
              <div>
                <el-input class="edit-input" :disabled="true" size="small" style="width:100px"></el-input>
                <el-input class="edit-input" placeholder="同步积分" v-model="batchPoints" @input="updatePoints()"
                          size="small" style="width:100px"></el-input>
                <el-input class="edit-input" :disabled="true" size="small" style="width:100px"></el-input>
              </div>
              <div v-for="item in dayList">
                <input class="edit-input" :id="item.eid" name="earnLimit" :disabled="true"
                       onkeyup="this.value=this.value.replace(/[^\d]/g,'')" :value="item.show"
                       style="width:98px;height:32px;background-color: #F5F7FA;color: #C0C4CC;cursor: not-allowed;border-radius: 4px;border: 1px solid #DCDFE6;text-align: center">
                <input class="edit-input" :id="item.pid" name="points"
                       onkeyup="this.value=this.value.replace(/[^\d]/g,'')"
                       style="width:68px;height: 32px;;border-radius: 4px;border: 1px solid #DCDFE6;padding: 0 15px">
                <input class="edit-input" :id="item.extId" name="extraReward"
                       onkeyup="this.value=this.value.replace(/[^\d]/g,'')"
                       style="width:68px;height: 32px;;border-radius: 4px;border: 1px solid #DCDFE6;padding: 0 15px"
                       value="0">
              </div>
            </el-form-item>
          </el-row>
        </el-form>
      </el-card>
      <div slot="footer" class="dialog-footer" content="">
        <el-button type="primary" @click="submit()">提 交</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
module.exports = {
  data: function () {
    return {
      dialogVisible: false,
      title: "新增",
      form: {
        extraReward: 0,
        earnLimit: '',
        activityId: '',
        earnType: 2,
        dailyNum: 1,
        enable: 1,
        ruleType: 1
      },
      batchPoints: '',
      earnLimitPointsExtraReward: '',
      dailyNumDisable: false,
      loading: null,
      earnTypeList: [{id: '1', name: '一次性'}, {id: '2', name: '无限次'}],
      activityList: [],
      dayList: [],
      formRules:
          {
            activityId: [
              {required: true, message: '活动不能为空', trigger: 'blur'}
            ]
          }
    }
  },
  props: {},
  methods: {
    visible(activityList) {
      this.activityList = activityList;
      this.dialogVisible = true;
      this.initDays();
    },
    updatePoints() {
      let points = this.batchPoints.replace(/[^\d]/g, '');
      $("input[name='points']").each(function () {
        $(this).val(points);
      })
    },
    initDays() {
      this.dayList = [];
      for (let i = 1; i < 32; i++) {
        this.dayList.push({
          id: i,
          show: "第" + i + "天",
          eid: "earnLimit_" + i,
          pid: "points_" + i,
          extId: "extraReward_" + i
        });
      }
    },
    changeEarnType() {
      if (this.form.earnType == '1') {
        this.dailyNumDisable = true;
        this.form.dailyNum = 1;
      } else {
        this.dailyNumDisable = false;
      }
    },
    loadingFunc() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loadingFunc();
          if (!this.form.extraReward) {
            this.form.extraReward = 0;
          }
          let data = [];
          let actId = this.form.activityId;
          this.dayList.forEach(function (item) {
            let points = $("#" + item.pid).val();
            if (!points) {
              points = 0;
            }
            let extraReward = $("#" + item.extId).val();
            if (!extraReward) {
              extraReward = 0;
            }
            let earnLimit = item.id;
            data.push({
              activityId: actId,
              points: points,
              earnLimit: earnLimit,
              extraReward: extraReward,
              dailyNum: 1,
              enable: 1,
              ruleType: 1,
              earnType: 2
            });
          });

          win.OkHttp.post($.api($.apiModule.bluBucksCenter, '/mgt/pointsEarnRule/save'), {"items":data}).then(res => {
            this.loading.close();
            if (res.msgCode == 0) {
              this.$message.success("保存成功")
              this.resetForm("form");
              this.$emit("success");
            }
          }).catch(e => {
            this.loading.close();
          })
        } else {
          return false;
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.dialogVisible = false;
    }
  }
}
</script>