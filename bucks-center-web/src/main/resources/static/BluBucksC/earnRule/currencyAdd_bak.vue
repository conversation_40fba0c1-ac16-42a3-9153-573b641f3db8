<template>
  <div>
    <el-dialog :title="title" center :visible.sync="dialogVisible" width="650px" :close-on-click-modal="false"
               @close="resetForm('form')">
      <el-form ref="form" :rules="formRules" :model="form" :inline="true" size="mini" label-width="150px">
        <el-row>
          <el-form-item label="活动" prop="activityId">
            <el-select v-model="form.activityId" filterable placeholder="国家/地区-活动-渠道" clearable>
              <el-option
                  v-for="item in activityList"
                  :key="item.activityId"
                  :label="`${item.countryName}-${item.activityName}-${item.channelName}`"
                  :value="item.activityId">
              </el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="累计消费额" prop="earnLimit">
            <el-input class="input2" max="11" onkeyup="this.value=this.value.replace(/[^\d]/g,'')" v-model="form.earnLimit"></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="兑换比例" prop="points">
            <el-input class="input2" max="11" onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" v-model="form.points"></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="额外奖励" prop="extraReward">
            <el-input class="input2" max="11" onkeyup="this.value=this.value.replace(/[^\d]/g,'')" v-model="form.extraReward"></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="发放类型" prop="earnType">
            <el-select v-model="form.earnType" placeholder="请选择" @change="changeEarnType" clearable>
              <el-option
                  v-for="item in earnTypeList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row v-if="!dailyNumDisable">
          <el-form-item label="单用户每日次数" prop="dailyNum">
            <el-input class="input2" max="11" :disabled="dailyNumDisable"
                      onkeyup="this.value=this.value.replace(/[^\d]/g,'')"
                      v-model="form.dailyNum"></el-input>
          </el-form-item>
        </el-row>

<!--        <el-row>-->
<!--          <el-form-item label="状态" prop="enable">-->
<!--            <el-select v-model="form.enable" placeholder="请选择" style="width:178px">-->
<!--              <el-option label="启用" :value="1"></el-option>-->
<!--              <el-option label="禁用" :value="0"></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--        </el-row>-->
      </el-form>
      <div slot="footer" class="dialog-footer" content="">
        <el-button type="primary" @click="submit()">提 交</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
module.exports = {
  data: function () {
    return {
      dialogVisible: false,
      title: "新增",
      form: {
        earnType: '',
        extraReward: 0,
        earnLimit: 0,
        activityId: '',
        dailyNum: 0,
        enable: 1,
        ruleType: 2,
        points:''
      },
      dailyNumDisable: false,
      loading: null,
      earnTypeList: [{id: 1, name: '一次性'}, {id: 2, name: '无限次'}],
      activityList: [],
      formRules:
          {
            activityId: [
              {required: true, message: '活动不能为空', trigger: 'blur'}
            ],
            earnLimit: [
              {required: true, message: '累计消费额不能为空', trigger: 'blur'}
            ],
            points: [
              {required: true, message: '兑换比例不能为空', trigger: 'blur'}
            ],
            extraReward: [
              {required: true, message: '额外奖励不能为空', trigger: 'blur'}
            ],
            earnType: [
              {required: true, message: '发放类型不能为空', trigger: 'blur'}
            ],
            dailyNum: [
              {required: true, message: '单用户每日次数不能为空', trigger: 'blur'}
            ],
            enable: [
              {required: true, message: '状态不能为空', trigger: 'blur'}
            ],
          }
    }
  },
  props: {},
  methods: {
    visible(activityList) {
      this.activityList = activityList;
      this.dialogVisible = true;
    },
    changeEarnType() {
      if (this.form.earnType == '1') {
        this.dailyNumDisable = true;
        this.form.dailyNum = 99999;
      } else {
        this.dailyNumDisable = false;
      }
    },
    loadingFunc() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.form.points = (Math.floor(parseFloat(this.form.points) * 100)/100).toFixed(2);
          if(this.form.points <= 0){
            this.$message.error("兑换比例必须大于0");
            return false;
          }
          this.loadingFunc();
          win.OkHttp.post($.api($.apiModule.bluBucksCenter, '/mgt/pointsEarnRule/add'), this.form).then(res => {
            this.loading.close();
            if (res.msgCode == 0) {
              this.$message.success("保存成功")
              this.resetForm("form");
              this.$emit("success");
            }
          }).catch(e => {
            this.loading.close();
          })
        } else {
          return false;
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.dialogVisible = false;
    }
  }
}
</script>