<template>
  <div>
    <el-dialog :title="title" center :visible.sync="dialogVisible" width="1280px" :close-on-click-modal="false"
               @close="resetForm('form')">
      <el-form ref="form" :rules="formRules" :model="form" :inline="true" label-width="150px">
        <el-row>
          <el-table :data="dataList" border style="width: 100%">
            <el-table-column prop="name" label="国家/地区-活动-渠道">
              <template slot-scope="scope">
                <el-select v-model="scope.row.activityId" @change="selectAll(scope.row.activityId)" filterable
                           placeholder="国家/地区-活动-渠道" clearable>
                  <el-option
                      v-for="item in activityList"
                      :key="item.activityId"
                      :label="`${item.countryName}-${item.activityName}-${item.channelName}`"
                      :value="item.activityId">
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="memberLevel" label="等级">
              <template slot-scope="scope">
                <el-input class="input2" onkeyup="this.value=this.value.replace(/[^\d]/g,'')"
                          v-model="scope.row.memberLevel"></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="title" label="等级名称">
              <template slot-scope="scope">
                <el-input class="input2" v-model="scope.row.title"></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="earnLimit" label="累计消费额">
              <template slot-scope="scope">
                <el-input class="input2" max="11" onkeyup="this.value=this.value.replace(/[^\d]/g,'')"
                          v-model="scope.row.earnLimit"></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="points" label="兑换比例">
              <template slot-scope="scope">
                <el-input class="input2" max="11" onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
                          v-model="scope.row.points"></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="extraReward" label="额外奖励">
              <template slot-scope="scope">
                <el-input class="input2" max="11" onkeyup="this.value=this.value.replace(/[^\d]/g,'')"
                          v-model="scope.row.extraReward"></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="earnType" label="发放类型">
              <template slot-scope="scope">
                <el-select v-model="scope.row.earnType" placeholder="请选择" @change="changeEarnType(scope.row)" clearable>
                  <el-option
                      v-for="item in earnTypeList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id">
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="address" label="单用户每日次数">
              <template slot-scope="scope">
                <el-input class="input2" max="11" :disabled="scope.row.dailyNumDisable"
                          onkeyup="this.value=this.value.replace(/[^\d]/g,'')"
                          v-model="scope.row.dailyNum"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="180">
              <template slot-scope="scope">
                <el-button type="danger" @click="addRow(scope.row)">新增</el-button>
                <el-button type="warning" @click="deleteRow(scope.row)">删除</el-button>
              </template>
            </el-table-column>

          </el-table>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" content="">
        <el-button type="primary" @click="submit()">提 交</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
module.exports = {
  data: function () {
    return {
      dialogVisible: false,
      title: "编辑",
      form: {
        id: '',
        earnType: 0,
        extraReward: 0,
        earnLimit: 0,
        activityId: '',
        dailyNum: 0,
        enable: 1,
        ruleType: 2,
        points: ''
      },
      dailyNumDisable: false,
      loading: null,
      earnTypeList: [{id: 1, name: '一次性'}, {id: 2, name: '无限次'}],
      activityList: [],
      dataList: [],
      formRules:
          {
            activityId: [
              {required: true, message: '活动不能为空', trigger: 'blur'}
            ],
            earnLimit: [
              {required: true, message: '累计消费额不能为空', trigger: 'blur'}
            ],
            points: [
              {required: true, message: '兑换比例不能为空', trigger: 'blur'}
            ],
            extraReward: [
              {required: true, message: '额外奖励不能为空', trigger: 'blur'}
            ],
            earnType: [
              {required: true, message: '发放类型不能为空', trigger: 'blur'}
            ],
            dailyNum: [
              {required: true, message: '单用户每日次数不能为空', trigger: 'blur'}
            ],
            enable: [
              {required: true, message: '状态不能为空', trigger: 'blur'}
            ],
          }
    }
  },
  props: {},
  methods: {
    visible(row, activityList) {
      this.activityList = activityList;
      this.form = {...row};
      this.dialogVisible = true;
      this.getList();
    },
    selectAll(activityId) {
      this.dataList.forEach(function (item) {
        item.activityId = activityId;
      })
    },
    getList() {
      this.dataListLoading = true
      win.OkHttp.post($.api($.apiModule.bluBucksCenter, '/mgt/pointsEarnRule/getSignList/' + this.form.id)).then(res => {
        if (res.msgCode == 0) {
          this.dataList = res.data;
          this.dataList.forEach(function (item) {
            item.rowId = item.id;
            item.dailyNumDisable = item.earnType == '1';
          })
        }
      })
      this.dataListLoading = false;
    },
    addRow(row) {
      let initData = {
        rowId: row.rowId + '1',
        memberLevel: row.memberLevel + 1,
        earnType: '',
        extraReward: 0,
        earnLimit: 0,
        activityId: row.activityId,
        dailyNum: 0,
        enable: 1,
        ruleType: 2,
        points: '',
        title: '',
        ruleCode: row.ruleCode
      }
      this.dataList.push(initData);
    },
    deleteRow(row) {
      this.$confirm('确认删除? ', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let newTableData = [];
        this.dataList.forEach(function (item) {
          if (item.rowId != row.rowId) {
            newTableData.push(item);
          }
        })
        this.dataList = newTableData;
      }).catch(() => {
      });
    },
    changeEarnType(row) {
      if (row.earnType == '1') {
        row.dailyNumDisable = true;
        row.dailyNum = 0;
      } else {
        row.dailyNumDisable = false;
        row.dailyNum = 99999;
      }
    },
    loadingFunc() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          //let points = parseFloat(this.form.points);
          // this.form.points = (Math.floor(parseFloat(this.form.points) * 100) / 100).toFixed(2);
          // if (this.form.points <= 0) {
          //   this.$message.error("兑换比例必须大于0");
          //   return false;
          // }
          this.loadingFunc();
          if (this.dataList.length === 0) {
            this.$message.warning("请填写至少一条规则")
          }
          win.OkHttp.post($.api($.apiModule.bluBucksCenter, '/mgt/pointsEarnRule/update'), {"items": this.dataList}).then(res => {
            this.loading.close();
            if (res.msgCode == 0) {
              this.$message.success("保存成功")
              this.resetForm("form");
              this.$emit("success");
            }
          }).catch(e => {
            this.loading.close();
          })
        } else {
          return false;
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.dialogVisible = false;
    }
  }
}
</script>