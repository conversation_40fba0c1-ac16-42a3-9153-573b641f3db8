<template>
  <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="30%"
      :close-on-click-modal="false"
      destroy-on-close
      :before-close="closeDialog">
    <el-form ref="form" :rules="rule" :model="form">
      <el-row>
        <el-col>
          <el-form-item label="类型" prop="genType">
            <el-select v-model="form.genType" placeholder="请选择类型" clearable filterable @change="findActivity()">
              <el-option
                  v-for="item in ruleDataList1"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="活动" prop="activityVal">
            <el-select filterable clearable v-model="form.activityVal" @change="selectActivity"  placeholder="国家/地区-活动-渠道">
              <el-option
                  v-for="item in activity"
                  :key="item.activityId"
                  :label="`${item.countryName}-${item.activityName}-${item.channelName}`"
                  :value="`${item.countryId}|${item.activityCode}|${item.channelCode}`">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-form-item label="原因" prop="reason" label-position="left">
            <el-input v-model="form.reason" type="textarea" maxlength="2000" show-word-limit placeholder="请输入原因"></el-input>
        </el-form-item>
      </el-row>
      <div>
        <el-form-item label="导入文件">
          <el-upload
              class="upload-demo"
              ref="upload"
              action=""
              :limit=1
              accept=".xls, .xlsx"
              :auto-upload="false"
              :file-list="fileList"
              :on-change="handleChange"
              :on-remove="handleRemove"
              :show-file-list="true"
          >
            <el-button size="small" type="primary" :disabled="!!form.file">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">
              请上传 .xls,.xlsx 标准格式文件
              <span
                  style="color: #409EFF;cursor: pointer"
                  @click="downloadTemplate"
              >下载模板
              </span>
            </div>
          </el-upload>
        </el-form-item>
      </div>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submit">确 定</el-button>
      <el-button plain @click="closeDialog">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
module.exports = {
  components: {},
  data() {
    return {
      earnList:[],
      spendList:[],
      temList:[],
      activity:[],
      dialogVisible: false,
      mode: '',
      title: "导入",
      form: {
        file: null,
        code:'',
        genType:'',
        reason:'',
        countryId:'',
        channelCode:'',
        activityVal:''
      },
      ruleDataList1:[
        {code: 1, name: "发放"},
        {code: 2, name: "消耗"}
      ],
      rule: {
        activityVal: [
          {required: true, message: '必须选择活动', trigger: ["blur", 'change']}
        ],
        genType: [
          {required: true, message: '必须选择类型', trigger: ["blur", 'change']},

        ],
        reason: [
          {required: true, message: '必须输入原因', trigger: ["blur", 'change']},
        ],
      },
      countryList: [],
      loading: null,
      fileList: [],
    }
  },
  methods: {
    showDialog() {
      this.dialogVisible = true
      win.OkHttp.get($.api($.apiModule.midpMasterData, "~/iso/country/v1/localized/all-countries")).then(res => {
        if (res.msgCode == 0) {
          this.countryList = res.data.countries;

          win.OkHttp.get($.api($.apiModule.bluBucksCenter, "/mgt/activity/commonList")).then(res => {
            if (res.msgCode == 0) {
              this.activity = res.data;

              for(let i=0; i<this.activity.length; i++){
                for(let j=0;j<this.countryList.length;j++){
                  if(this.activity[i].countryId===this.countryList[j].countryId){
                    this.activity[i].countryName=this.countryList[j].countryName
                  }
                }
                if(this.activity[i].earnRule!==""&&this.activity[i].earnRule!=null){
                  this.earnList.push(this.activity[i]);
                }else if(this.activity[i].spendRule!==""&&this.activity[i].spendRule!=null){
                  this.spendList.push(this.activity[i]);
                }

              }
              this.temList=this.activity
            }
          })

        }
      })
    },
    closeDialog() {
      this.fileList = []
      this.form = {
        file: null,
        code:'',
        genType:'',
        reason:'',
        countryId:'',
        channelCode:'',
        activityVal:''
      }
      this.earnList = [],
      this.spendList = [],
      this.temList = [],
      this.$refs.form.clearValidate()
      this.$refs.form.resetFields()
      this.$refs.upload.clearFiles()
      this.dialogVisible = false
    },
    findActivity(){
      this.form.activityVal=''
      if(this.form.genType===1){
        this.activity=this.earnList;
      }else if(this.form.genType===2){
        this.activity=this.spendList;
      }else{
        this.activity=this.temList;
      }

    },
    selectActivity() {
      this.form.genType=''
      let val = this.form.activityVal;
      let arr = val.split("|");
      this.form.code = arr[1];
      this.form.channelCode = arr[2];
      this.form.countryId = arr[0];
      this.activity.forEach(a=>{
        if(a.activityCode===this.form.code){
          if(a.earnRule!=null&&a.earnRule!==""){
            this.form.genType=1
          }else if(a.spendRule!=null&&a.spendRule!==""){
            this.form.genType=2
          }
        }
      })
    },
    downloadTemplate(){
      let timestamp = Date.now()
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      win.OkHttp.get($.api($.apiModule.bluBucksCenter, '/mgt/pointsIntervene/downloadImportTemplate'),
          {responseType: 'blob'}).then(res => {
        const a = document.createElement('a');
        const url = window.URL.createObjectURL(res);
        a.href = url;
        a.download = `points_import.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }).finally(_=>{
        loading.close()
      })
    },
    getLoading() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },
    handleChange(_file, _fileList) {
      if(_fileList.length > 1) {
        this.fileList.splice(0, 1, _file)
      }
      this.form.file = _file
    },
    handleRemove(){
      this.form.file=null
      this.$refs.upload.clearFiles()
      this.fileList = []
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if(!this.form.file) return this.$message.warning('请上传文件')
          const formData = new FormData();
          formData.append("file", this.form.file.raw);
          const params = `?code=${this.form.code}&genType=${this.form.genType}&reason=${encodeURIComponent(this.form.reason)}&countryId=${this.form.countryId}&channelCode=${this.form.channelCode}`
          win.OkHttp.post($.api($.apiModule.bluBucksCenter, '/mgt/pointsIntervene/importPoints'+params),
              formData,{
                responseType: 'blob',
              }).then(res => {
            const url = window.URL.createObjectURL(new Blob([res]));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', 'import_points_result.xlsx'); // 设置下载文件名
            document.body.appendChild(link);
            link.click();
            window.URL.revokeObjectURL(url); // 释放 URL 对象
            this.$message.success('导入成功')
            this.$emit('success')
            this.closeDialog()
          })
        } else {
          return false;
        }
      })
    }
  }
}
</script>

<style scoped>
.el-textarea{
  width: calc(100% - 80px);
}
</style>
