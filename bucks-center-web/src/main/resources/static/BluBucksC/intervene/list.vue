<template>
  <div>
    <el-card class="box-card" shadow="none">
      <el-form :model="searchForm" ref="form" :inline="true" size="mini" label-width="110px">
        <el-row>
          <el-col :span="8">

            <el-form-item label="来源渠道" prop="channelCode">
              <el-select v-model="searchForm.channelCode" placeholder="请选择" filterable clearable>
                <el-option
                    v-for="item in channel"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>


          <el-col :span="8">
            <el-form-item label="活动" prop="activityId">
              <el-select v-model="searchForm.activityId" placeholder="请选择" clearable filterable>
                <el-option
                    v-for="item in activity"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="国家/地区" prop="countryId">
              <el-select v-model="searchForm.countryId" placeholder="请选择" clearable filterable>
                <el-option
                    v-for="item in countryList"
                    :key="item.geoNameId"
                    :label="item.countryName +'/'+ item.countryCode"
                    :value="item.geoNameId">
                </el-option>
              </el-select>
            </el-form-item>


          </el-col>
        </el-row>
        <el-row type="flex">
          <el-col :span="10">
            <el-form-item label=" ">
              <el-button type="primary" @click="search()">查询</el-button>
              <el-button type="primary" @click="resetForm()">重置</el-button>
              <el-button type="primary" @click="add()" v-has="'182058bd73ff79115229d154a67'">新增</el-button>
              <el-button type="primary" @click="bulkImport()">批量导入</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>


    <el-row>
      <el-col :span="24">
        <el-table :data="dataList" border stripe size="small">
          <el-table-column align="center" show-overflow-tooltip header-align="center" type="index"></el-table-column>

          <el-table-column align="center" show-overflow-tooltip prop="journalUname" label="用户名称"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="time" label="录入时间"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="uname" label="录入人"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="countryId" label="国家/地区"
                           :formatter="countryFormat"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="activityId" label="活动"
                           :formatter="activityFormat"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="channelCode" label="来源渠道"
                           :formatter="channelFormat"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="genType" label="类型"
                           :formatter="statusFormat1"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="points" label="积分"></el-table-column>


        </el-table>

        <el-pagination @size-change="sizeChangeHandle"
                       @current-change="currentChangeHandle"
                       :current-page="searchForm.pageNo"
                       :page-sizes="[10, 20, 50, 100]"
                       :page-size="searchForm.pageSize"
                       :total="searchForm.totalPage"
                       layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>


      </el-col>
    </el-row>

    <el-row>
      <el-col>
        <add ref="add" @success="getList()"/>
      </el-col>
    </el-row>

    <file ref="file" @success="getList()"></file>
  </div>
</template>
<script>
const ruleDataList1 = [{code: 1, name: "增加"}, {code: 2, name: "消耗"}];

module.exports = {
  components: {
    "add": httpVueLoader("/BluBucksC/intervene/add.vue"),
    'file': httpVueLoader('/BluBucksC/intervene/file.vue')
  },
  data: function () {
    return {
      searchForm: {
        pageNo: 1,
        pageSize: 10,
        totalPage: 0,
        channelCode: '',
        activityId: '',
        countryId: '',

      },
      dataList: [],
      channel: [],
      countryList: [],
      activity: []
    }
  },

  methods: {

    bulkImport(){
      this.$refs.file.showDialog()
    },
    statusFormat1(row) {
      if (row.genType === null || row.genType === '') return row.genType;


      let result = null;
      ruleDataList1.forEach(function (item) {
        if (item.code === row.genType) {
          result = item.name;
        }
      })
      return result;
    },

    channelFormat(row) {
      if (row.channelCode === null || row.channelCode === '') return row.channelCode;
      let result = null;
      this.channel.forEach(function (item) {
        if (item.code === row.channelCode) {
          result = item.name;
        }
      })
      return result;

    },


    activityFormat(row) {
      if (row.activityId === null || row.activityId === '') return row.activityId;
      let result = null;
      this.activity.forEach(function (item) {
        if (item.id === row.activityId) {
          result = item.name;
        }
      })
      return result;
    },
    countryFormat(row) {
      if (row.countryId === null || row.countryId === '') return row.countryId;


      let result = null;
      this.countryList.forEach(function (item) {
        if (item.geoNameId === row.countryId) {
          result = item.countryName;
        }
      })
      return result;


    },

    search() {
      this.searchForm.pageNo = 1;
      this.getList();
    },

    getList() {
      win.OkHttp.get($.api($.apiModule.midpMasterData, "~/iso/country/v1/localized/all-countries")).then(res => {
        if (res.msgCode == 0) {
          this.countryList = res.data.countries;
        }
      })
      win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/pointsIntervene/page"), this.searchForm).then(res => {
        if (res.msgCode == 0) {
          this.dataList = res.data.content;
          this.searchForm.totalPage = Number(res.data.totalElements)
        }
      })

      win.OkHttp.get($.api($.apiModule.bluBucksCenter, "/mgt/channel/existsList")).then(res => {
        if (res.msgCode == 0) {
          this.channel = res.data;
        }
      })

      win.OkHttp.get($.api($.apiModule.bluBucksCenter, "/mgt/activity/existsList")).then(res => {
        if (res.msgCode == 0) {
          this.activity = res.data;
        }
      })

    },


    currentChangeHandle(val) {
      this.searchForm.pageNo = val
      this.getList()
    },
    // 每页数
    sizeChangeHandle(val) {
      this.searchForm.pageSize = val
      this.searchForm.pageNo = 1
      this.getList()
    },


    add() {
      this.$refs.add.visible(true);
    },

    resetForm() {
      this.$refs.form.resetFields();
      this.getList();

    },


  },
  mounted() {
    this.$nextTick(function () {
      this.getList();
    })
  }
}
</script>
