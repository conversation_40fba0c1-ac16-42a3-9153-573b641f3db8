<template>
  <div>

    <el-dialog :title="title1" center :visible.sync="userVisible" width="60%" :close-on-click-modal="false"
               @close="close1()">

      <el-card class="box-card" shadow="none">
        <el-form :model="searchForm.member" ref="form1" :inline="true" size="mini" label-width="110px">
          <el-row>
            <el-col :span="8">
              <el-form-item label="用户昵称" prop="nickName">
                <el-input type="text" v-model="searchForm.member.nickName" placeholder="用户昵称" clearable></el-input>
              </el-form-item>
            </el-col>


            <el-col :span="8">
              <el-form-item label="用户手机" prop="phone">
                <el-input type="text" v-model="searchForm.member.phone" placeholder="用户手机" clearable></el-input>
              </el-form-item>
            </el-col>


            <el-col :span="8">
              <el-form-item label="用户邮箱" prop="email">
                <el-input type="text" v-model="searchForm.member.email" placeholder="用户邮箱" clearable></el-input>
              </el-form-item>
            </el-col>


          </el-row>


          <el-row type="flex">
            <el-col :span="10">
              <el-form-item label=" ">
                <el-button type="primary" @click="search()">查询</el-button>
                <el-button type="primary" @click="resetForm('form1')">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <div style="overflow: auto;height: 480px">
      <el-row>
        <el-col :span="24">
          <el-table :data="users" border stripe size="small" @row-click="rowClick">
            <el-table-column align="center" show-overflow-tooltip header-align="center" type="index"></el-table-column>
            <!--            <el-table-column align="center" show-overflow-tooltip prop="username" label="用户账号" ></el-table-column>-->
            <el-table-column align="center" show-overflow-tooltip prop="nickname" label="用户昵称"></el-table-column>
            <el-table-column align="center" show-overflow-tooltip prop="phone" label="用户手机"></el-table-column>
            <el-table-column align="center" show-overflow-tooltip prop="email" label="用户邮箱"></el-table-column>
            <!--            <el-table-column align="center" show-overflow-tooltip prop="country" label="国家" ></el-table-column>-->
          </el-table>

          <el-pagination @size-change="sizeChangeHandle"
                         @current-change="currentChangeHandle"
                         :current-page="searchForm.pageNo"
                         :page-sizes="[10, 20, 50, 100]"
                         :page-size="searchForm.pageSize"
                         :total="searchForm.totalPage"
                         layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>


        </el-col>
      </el-row>
      </div>

      <div slot="footer" class="dialog-footer" content="">

        <el-button @click="close1()">取 消</el-button>
      </div>
    </el-dialog>


    <el-dialog :title="title" center :visible.sync="dialogVisible" width="40%" :close-on-click-modal="false"
               @close="cancel()">
      <el-form ref="form" :rules="formRules" :model="form" :inline="true" size="mini">



        <el-row>
          <el-col>
            <el-form-item label="类型" prop="genType" label-width="110px">
              <el-select v-model="form.genType" placeholder="请选择类型" clearable filterable @change="findActivity()">
                <el-option
                    v-for="item in ruleDataList1"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>



        <el-row>
          <el-col>
            <el-form-item label="活动" prop="activityVal"  label-width="110px">
              <el-select filterable clearable v-model="form.activityVal" @change="selectActivity"  placeholder="国家/地区-活动-渠道" >
                <el-option
                    v-for="item in activity"
                    :key="item.activityId"
                    :label="`${item.countryName}-${item.activityName}-${item.channelName}`"
                    :value="`${item.countryId}|${item.activityCode}|${item.channelCode}`">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>





        <el-row>
          <el-col>
            <el-form-item label="积分值" prop="points" label-width="110px">
              <el-input-number class="input1" v-model="form.points" :min="0" :precision="0"
                        ></el-input-number>
            </el-form-item>


          </el-col>


        </el-row>


        <el-row>
          <el-col>
            <el-form-item label="用户" label-width="110px">
              <el-input class="input1" v-model="uname" disabled></el-input>
            </el-form-item>
            <el-button type="primary" @click="select" size="small">选择</el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item prop="uid" label-width="110px" style="display: none">
              <el-input class="input1" v-model="form.uid"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
        <el-col >
          <el-form-item label="录入原因" prop="reason" label-width="110px">
            <el-input type="text" v-model="form.reason" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" content="">
        <el-button type="primary" @click="submit">新增</el-button>
        <el-button @click="cancel()">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
const ruleDataList1 = [{code: 1, name: "发放"}, {code: 2, name: "消耗"}];

module.exports = {
  components: {},
  data: function () {
    const pointsNum = (rule, value, callback) => {

      if(Number(value)<=0){
        callback(new Error('积分不能小于等于零'));
      }

      const age1 = /^[0-9]+\.[0-9]{2}$/
      const age2 = /^[0-9]+\.[0-9]{1}$/
      const age3 = /^[0-9]*$/
      if(!(age1.test(value) || age2.test(value) || age3.test(value))){
        callback(new Error('最多输入两位小数'))
      }else {
        callback()
      }
    }

    return {
      earnList:[],
      spendList:[],
      temList:[],
      loading: null,
      uname: '',
      dialogVisible: false,
      title: "新增人工干预",
      title1:'选择用户',
      lastClick :undefined,
      form: {
        genType:'',
        reason:'',
        points: 0,
        code: '',
        channelCode: '',
        countryId: '',
        activityVal:''
      },
      channel: [],
      countryList: [],
      activityVal: '',
      activity: [],
      ruleDataList1: ruleDataList1,
      formRules: {
        activityVal: [
          {required: true, message: '必须选择活动', trigger: ["blur", 'change']}
        ],
        genType: [
          {required: true, message: '必须选择类型', trigger: ["blur", 'change']},

        ],
        points: [
          {required: true, validator: pointsNum, trigger: ["blur", 'change']}
        ],
        reason: [
          {required: true, message: '必须输入录入原因', trigger: ["blur", 'change']},
        ],

      },
      userVisible: false,
      users: [],


      searchForm: {
        pageNo: 1,
        pageSize: 10,
        totalPage: 0,
        member: {
          nickName:'',
          phone:'',
          email:''
        }
      }
    }
  },
  methods: {
    findActivity(){
      this.form.activityVal=''
      if(this.form.genType===1){
        this.activity=this.earnList;
      }else if(this.form.genType===2){
        this.activity=this.spendList;
      }else{
        this.activity=this.temList;
      }

    },
    selectActivity() {
      this.form.genType=''
      let val = this.form.activityVal;
      let arr = val.split("|");
      this.form.code = arr[1];
      this.form.channelCode = arr[2];
      this.form.countryId = arr[0];
      this.activity.forEach(a=>{
        if(a.activityCode===this.form.code){
          if(a.earnRule!=null&&a.earnRule!==""){
            this.form.genType=1
          }else if(a.spendRule!=null&&a.spendRule!==""){
            this.form.genType=2
          }
        }
      })


    },

    loadingFunc() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },
    search() {
      this.searchForm.pageNo = 1;
      this.getUsers()
    },
    currentChangeHandle(val) {
      this.searchForm.pageNo = val
      this.getUsers()
    },
    // 每页数
    sizeChangeHandle(val) {
      this.searchForm.pageSize = val
      this.searchForm.pageNo = 1
      this.getUsers()
    },

    lockClick(){
      var nowClick = new Date();
      if (this.lastClick === undefined) {
        this.lastClick = nowClick;
        return true;
      } else {
        if (Math.round((nowClick.getTime() - this.lastClick.getTime())) > 500) {
          this.lastClick = nowClick;
          return true;
        }
        else {
          this.lastClick = nowClick;
          return false
        }
      }
    },

    rowClick(row) {
      if (this.lockClick()) {
        this.uname = row.nickname;
        this.form.uid = row.uid
        this.userVisible = false;
      }
    },
    getUsers() {
      win.OkHttp.post($.api($.apiModule.bluUserCenter, "~/uc/basic/list"), this.searchForm).then(res => {
        if (res.msgCode == 0) {
          this.users = res.data.content;
          this.searchForm.totalPage = Number(res.data.totalElements)
        }
      })
    },

    close1() {
      this.searchForm={
        pageNo: 1,
        pageSize: 10,
        totalPage: 0,
        member: {
          nickName:'',
          phone:'',
          email:''
        }
      }
      this.userVisible=false
      this.getUsers()
    },
    select() {

      this.userVisible = true




    },
    visible() {
      this.dialogVisible = true;

      win.OkHttp.get($.api($.apiModule.midpMasterData, "~/iso/country/v1/localized/all-countries")).then(res => {
        if (res.msgCode == 0) {
          this.countryList = res.data.countries;

          win.OkHttp.get($.api($.apiModule.bluBucksCenter, "/mgt/activity/commonList")).then(res => {
            if (res.msgCode == 0) {
              this.activity = res.data;

              for(let i=0; i<this.activity.length; i++){
                for(let j=0;j<this.countryList.length;j++){
                  if(this.activity[i].countryId===this.countryList[j].countryId){
                    this.activity[i].countryName=this.countryList[j].countryName
                  }
                }
                if(this.activity[i].earnRule!==""&&this.activity[i].earnRule!=null){
                  this.earnList.push(this.activity[i]);
                }else if(this.activity[i].spendRule!==""&&this.activity[i].spendRule!=null){
                  this.spendList.push(this.activity[i]);
                }

              }
              this.temList=this.activity
            }
          })

        }
      })



      this.getUsers();


    },
    cancel() {
      this.earnList=[]
      this.spendList=[]
      this.temList=[]
      this.uname = ''
      this.form.uid = ''
      this.form.activityVal=''
      this.dialogVisible = false;
      this.$refs['form'].resetFields();

    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.visible();

    },
    submit() {

      this.$refs.form.validate((valid) => {
        if (valid) {


          if(this.form.uid==null||this.form.uid===''){
            this.$message.error("用户不能为空");
            return false;
          }
          else{
            this.$confirm('是否确定新增?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(()=>{
              this.loadingFunc();
              win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/pointsIntervene/add"), this.form).then(res => {
                this.loading.close();
                if (res.msgCode == 0) {
                  this.earnList=[]
                  this.spendList=[]
                  this.temList=[]
                  this.$message.success("新增成功");
                  this.resetForm("form");
                  this.dialogVisible = false;
                  this.$emit("success");


                }
              }).catch(e => {
                this.loading.close();
              })
            }).catch(() => {
              this.$message.info('已取消新增');
            });
          }




        } else {
          return false;
        }

      })
    }
  }
}
</script>