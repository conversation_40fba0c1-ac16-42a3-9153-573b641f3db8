<template>
  <div>
    <el-card class="box-card" shadow="none">
      <el-form :model="searchForm" ref="form" :inline="true" size="mini" label-width="110px">
        <el-row :span="18">
          <el-col :span="18">
            <el-form-item label="标题" prop="title">
              <el-input type="text" v-model="searchForm.title" placeholder="标题" clearable></el-input>
            </el-form-item>
            <el-form-item prop="countryId" label="国家/地区">
              <el-select v-model="searchForm.countryId" placeholder="请选择" filterable clearable>
                <el-option
                    v-for="item in countryList"
                    :key="item.geoNameId"
                    :label="item.countryName +'/'+ item.countryCode"
                    :value="item.geoNameId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex">
          <el-col :span="10">
            <el-form-item label=" ">
              <el-button type="primary" @click="search()">查询</el-button>
              <el-button type="primary" @click="resetForm()">重置</el-button>
              <el-button type="primary" v-has="'1820501c41bf79115229d154a5a'" @click="add()">新增</el-button>
<!--              <el-button type="primary" @click="add()">新增</el-button>-->
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

    </el-card>
    <el-row>
      <el-col :span="24">
        <el-table :data="dataList" border stripe size="small">
          <el-table-column align="center" show-overflow-tooltip header-align="center" type="index"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="country" label="国家/地区" :formatter="countryFormat"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="title" label="标题" width="500"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="content" label="内容" width="500"></el-table-column>
          <el-table-column align="center" label="操作" min-width="200px">
            <template slot-scope="scope">
              <el-button size="mini" type="primary" v-has="'18205029badf79115229d154a5c'"  @click="edit(scope.row)">编辑</el-button>
              <el-button size="mini" type="danger" v-has="'1820501d730f79115229d154a5b'"  @click="remove(scope.row.id)">删除</el-button>

<!--              <el-button size="mini" type="primary"  @click="edit(scope.row)">编辑</el-button>-->
<!--              <el-button size="mini" type="danger" @click="remove(scope.row.id)">删除</el-button>-->
            </template>
          </el-table-column>

        </el-table>

        <el-pagination @size-change="sizeChangeHandle"
                       @current-change="currentChangeHandle"
                       :current-page="searchForm.pageNo"
                       :page-sizes="[10, 20, 50, 100]"
                       :page-size="searchForm.pageSize"
                       :total="searchForm.totalPage"
                       layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>

      </el-col>
    </el-row>

    <el-row>
      <el-col>
        <add ref="add" @success="getList()"/>
      </el-col>
    </el-row>
    <el-row>
      <el-col>
        <edit ref="edit" @success="getList()"/>
      </el-col>
    </el-row>
  </div>
</template>
<script>


module.exports = {
  components: {
    "add": httpVueLoader("/BluBucksC/faq/add.vue"),
    "edit": httpVueLoader("/BluBucksC/faq/edit.vue"),


  },
  data: function () {
    return {
      searchForm: {
        pageNo: 1,
        pageSize: 10,
        totalPage: 0,
        title: '',
        countryId:''
      },
      dataList: [],
      countryList: [],
    }
  },

  methods: {
    search() {
      this.searchForm.pageNo = 1;
      this.getList();
    },
    countryFormat(row) {
      if ( row.countryId === null || row.countryId === '') return row.countryId;
      return this.countryList.filter(item => item.geoNameId === row.countryId)[0].countryName;
    },
    getList() {
      win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/faq/page"), this.searchForm).then(res => {
        if (res.msgCode == 0) {
          this.dataList = res.data.content;
          this.searchForm.totalPage = Number(res.data.totalElements)
        }
      })
    },
    getCountryList() {
      win.OkHttp.get($.api($.apiModule.midpMasterData, "~/iso/country/v1/localized/all-countries")).then(res => {
        if (res.msgCode == 0) {
          this.countryList = res.data.countries;
        }
      })
    },
    remove(id) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        win.OkHttp.post($.api($.apiModule.bluBucksCenter, '/mgt/faq/delete/') + id).then(res => {
          if (res.msgCode == 0) {
            this.$message.success("删除成功")
            this.searchForm.pageNo = 1
            this.getList();
          }
        })
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },

    currentChangeHandle(val) {
      this.searchForm.pageNo = val
      this.getList()
    },
    // 每页数
    sizeChangeHandle(val) {
      this.searchForm.pageSize = val
      this.searchForm.pageNo = 1
      this.getList()
    },

    edit(row) {
      this.$refs.edit.visible(row,this.countryList);
    },

    add() {
      this.$refs.add.visible(this.countryList);
    },

    resetForm() {
      this.$refs.form.resetFields();
      this.getList();
    },
  },
  mounted() {
    this.$nextTick(function () {
      this.getList();
      this.getCountryList();
    })
  }
}
</script>