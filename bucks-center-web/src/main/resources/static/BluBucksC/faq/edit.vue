<template>
  <div>
    <el-dialog :title="title" center :visible.sync="dialogVisible" width="40%" :close-on-click-modal="false"
               @close="resetForm('form')">
      <el-form ref="form" :rules="formRules" :model="form" :inline="true" size="mini">
        <el-row >
          <el-col>
            <el-form-item label="国家/地区" prop="countryId" label-width="110px">
              <el-select v-model="form.countryId" placeholder="请选择" clearable filterable>
                <el-option
                    v-for="item in countryList"
                    :key="item.geoNameId"
                    :label="item.countryName"
                    :value="item.geoNameId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="标题" prop="title" label-width="110px">
              <el-input class="input1" v-model="form.title" style="width:400px" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="内容" prop="content" label-width="110px">
              <el-input type="textarea" class="input1" :rows="5" :autosize="{ minRows: 2,maxRows:3}" style="width:100%;overflow: auto;word-break: break-all" v-model="form.content"
                        clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" content="">
        <el-button type="primary" @click="submit">保存</el-button>
        <el-button @click="resetForm('form')">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>


module.exports = {
  components: {},
  data: function () {
    return {
      dialogVisible: false,
      title: "编辑FAQ",
      loading: null,
      form: {
        id: '',
        title: '',
        content: '',
        countryId: ''
      },
      countryList: [],
      formRules: {
        title: [
          {required: true, message: '必须输入标题', trigger: 'blur'}
        ],
        content: [
          {required: true, message: '必须输入内容', trigger: 'blur'}
        ],
        countryId: [
          {required: true, message: '请选择国家/地区', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    loadingFunc() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },
    visible(row, countryList) {
      this.dialogVisible = true;
      this.form = {...row};
      this.countryList = countryList;
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.dialogVisible = false;
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {

          this.$confirm('是否确定修改?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.loadingFunc();
            win.OkHttp.post($.api($.apiModule.bluBucksCenter, '/mgt/faq/update'), this.form).then(res => {
              this.loading.close();
              if (res.msgCode == 0) {
                this.$message.success("修改成功");
                this.resetForm("form");
                this.$emit("success");
              }
            }).catch(e => {
              this.loading.close();
            })
          }).catch(() => {
            this.$message.info('已取消新增');
          });
        } else {
          return false;
        }
      })
    }


  }
}
</script>