<!--
引用方式：
<file-upload
    v-if="dialogVisible"
    ref="handleFile"
    :limit="1"
    class="file-upload"
    :file-list="fileList"
    @change="updateFileList">
</file-upload>

components:
"file-upload": httpVueLoader("/BluGoHire/components/file-upload.vue"),

methods:
updateFileList(_list) {
  this.fileList = _list
},

接受文件列表中，参数要求如下：
@param {string} fileId 文件 id (必要参数)
@param {string} name 文件标题 (非必要参数)
@param {string} fileUrl 文件 fileUrl (非必要参数)

传出的文件列表中，参数如下：
@param {string} fileId 文件 id
@param {string} name 文件标题
@param {string} fileUrl 文件 fileUrl
@param {string} size 文件 size

上传之前的格式化：
this.fileList = (list ?? []).map(item => ({
    ...item,
  })
)

-->

<template>
  <div>
    <div class="upload-wrapper">
      <div class="upload__box upload__item" v-if="viewOnly === false">
        <input
            ref="upload"
            type="file"
            id="file_upload"
            name="file_upload"
            accept="*"
            @change="selectedFile"/>
        <i class="el-icon-plus upload__item-upload"></i>
      </div>
      <template v-if="curFileList.length">
        <div class="upload__item" v-for="(file, index) in curFileList" v-if="file.fileUrl">
          <img :src="file.fileUrl" @click="showFile(file.fileUrl)" alt="file"/>
          <i class="el-icon-remove upload__item-close" @click="remove(file, index)" v-show="viewOnly === false"></i>
        </div>
      </template>
      <div class="upload__tips" slot="tip" v-show="tipShow">
        *最多只能上传{{ `${limit}` }}个文件，每个文件不能超过{{ `${fileSize}` }} M，支持上传的文件格式有: {{ this.accept.join('/') }}
      </div>
    </div>
  </div>
</template>

<script>
const acceptTypeMap = new Map([
  ['jpeg', 'image/jpeg'],
  ['jpg', 'image/jpeg'],
  ['gif', 'image/gif'],
  ['png', 'image/png'],
  ['webp', 'image/webp'],
  ['doc', 'application/msword'],
  ['docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  ['xls', 'application/vnd.ms-excel'],
  ['xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
  ['pdf', 'application/pdf'],
  ['rar', 'application/x-compressed'],
  ['zip', 'application/x-zip-compressed'],
])
module.exports = {
  name: "file-upload",
  props: {
    // 文件个数限制
    limit: {
      type: Number,
      default: 3
    },
    // 文件大小限制，单位默认为 MB
    fileSize: {
      type: Number,
      default: 2
    },
    // 文件类型限制
    accept: {
      type: Array,
      default: () => {
        return ['jpeg', 'jpg', 'png']
      }
    },
    // 编辑带入
    fileList: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 查看模式
    viewOnly: {
      type: Boolean,
      default: false
    },
    // 提示显示与否
    tipShow: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    fileList: {
      deep: true,
      immediate: true,
      handler(newArr) {
        // 处理编辑带入文件
        if (newArr && newArr.length) {
          newArr.forEach(item => {
            if(item.fileId) {
              let url = win.FileUtil.formatterImageUrl(item.fileId) || ''
              item.fileUrl = item.name ? this.getFileUrl(url, item.name) : url
            }
          })
          this.curFileList = [...newArr]
        }
      },
    },
    accept: {
      deep: true,
      immediate: true,
      handler(newArr) {
        if (newArr && newArr.length) {
          newArr.forEach(item => {
            if(acceptTypeMap.has(item)) {
              let val = acceptTypeMap.get(item)
              if(this.acceptType.indexOf(val) === -1) {
                this.acceptType.push(val)
              }
            }
            else {
              this.$message.warning(`当前控件暂不支持${item}类型文件`)
            }
          })
        }
      },
    }
  },
  data() {
    return {
      /**
       * 维护当前文件列表
       * @param {string} id 数据 id
       * @param {string} fileId 文件 id
       * @param {string} name 文件标题
       * @param {string} fileUrl 文件 fileUrl
       * */
      curFileList: [],
      acceptType: [],
    }
  },
  methods: {
    /**
     * 主流程
     * */
    // 选中文件后
    async selectedFile(event) {
      const appendFile = event.target.files[0]
      // 校验成功
      if (this.beforeUpload(appendFile)) {
        const { data } = await this.uploadFile(appendFile)
        if(this.afterUpload(data, appendFile)) {
          this.$emit('change', this.curFileList)
        }
      }
      this.removeFileFromUpload()
    },
    // 校验
    beforeUpload(appendFileList) {
      // 1.文件个数
      if (1 + this.curFileList.length > this.limit) {
        this.$message(`当前附件个数已达到最大限制：${this.limit}`)
        return false
      }
      // 2.文件格式
      const isLtForm = (this.acceptType.indexOf(appendFileList.type) !== -1)
      if (!isLtForm) {
        this.$message(`当前附件格式不正确，请重新上传以下格式文件：${this.accept}`)
        return false
      }
      // 3.文件大小
      const isLtSize = Object.keys(appendFileList).every(item => {
        return (appendFileList[item].size / 1024 / 1024) < this.fileSize
      })
      if (!isLtSize) {
        this.$message(`当前附件大小超过限制：${this.fileSize}M`)
        return false
      }
      // 校验通过
      return true
    },
    // 文件上传
    uploadFile(file) {
      const params = {
        uid: win.StorageUtil.sessionStorageGet('userInfo'),
        appId: 'blu-bucks-center',
        file: file
      }
      return win.FileUtil.uploadSingleFile(params)
    },
    // 追加
    afterUpload(returnData, appendFile) {
      if(!returnData) {
        this.$message.warning('文件上传有误')
        return false
      }
      const fileId = returnData.fileUploadDetails[0].id
      // 判断是否存在重复上传
      if(this.curFileList.length) {
        const obj = this.curFileList.find(item => item.fileId === fileId)
        if (obj) {
          this.$message.warning('请勿重复上传')
          return false
        }
      }

      const fileUrl = returnData.fileUploadDetails[0].fileServer + returnData.fileUploadDetails[0].filePath
      this.curFileList.push({
        fileId: fileId,
        name: appendFile.name,
        fileUrl: this.getFileUrl(fileUrl, appendFile.name),
        size: appendFile.size
      })
      return true
    },

    /**
     * 占位图
     * */
    getFileUrl(fileUrl, fileName) {
      const bitImg = this.getBitMap(fileName)
      return bitImg ? bitImg : fileUrl
    },
    getBitMap(fileName) {
      const typeName = fileName.slice(fileName.lastIndexOf('.')+1)
      if(typeName === 'doc' || typeName === 'docx') {
        return '/BluGoHire/assets/images/docx.png'
      }
      else if(typeName === 'xls' || typeName === 'xlsx') {
        return '/BluGoHire/assets/images/excel.png'
      }
      else if(typeName === 'pdf') {
        return '/BluGoHire/assets/images/pdf.jpg'
      }
      else if(typeName === 'rar') {
        return '/BluGoHire/assets/images/rar.png'
      }
      else if(typeName === 'zip') {
        return '/BluGoHire/assets/images/zip.png'
      }
      else return ''
    },
    // 预览文件
    showFile(url) {
      if (this.viewOnly) {
        window.open(url)
      }
    },

    /**
     * 删除
     * */
    remove(item, index) {
      // 校验删除文件是否存在，记录下标
      let delIndex = -1
      this.curFileList.find((it, _idx) => {
        delIndex = _idx
        return it.fileId === item.fileId
      })

      // 删除文件
      if (delIndex !== -1) {
        this.removeFile(delIndex)
        this.removeFileFromUpload(item, index)
      } else {
        this.$message('删除失败')
      }
    },
    // 从当前维护文件列表 curFileList 中删除
    removeFile(delIndex) {
      this.curFileList.splice(delIndex, 1)
      this.$emit('change', this.curFileList)
    },
    // 从控件中删除
    removeFileFromUpload() {
      this.$refs.upload.value = null
    },
    // 清空
    clearFileUpload() {
      this.curFileList = []
      // this.removeFileFromUpload()
    }
  },
  destroyed() {
    this.clearFileUpload()
  },
}
</script>

<style scoped>
.upload-wrapper {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 12px;
  margin: 8px 0;
}

.upload-wrapper .upload-tips {

}

.upload-wrapper .upload__box {
  border: 1px dashed #2F81FF;
}

.upload-wrapper .upload__item-upload {
  font-size: 28px;
  color: #2F81FF;
  width: 80px;
  height: 80px;
  line-height: 80px;
  text-align: center;
}

.upload-wrapper .upload__box input {
  position: absolute;
  left: 0;
  top: 0;
  display: inline-block;
  width: 80px;
  height: 80px;
  border: none;
  opacity: 0;
  cursor: pointer;
}

.upload-wrapper .upload__item {
  position: relative;
  display: flex;
  width: 80px;
  height: 80px;
  border-radius: 12px;
  margin-right: 6px;
}

.upload__item-close {
  height: 14px;
  width: 14px;
  position: absolute;
  right: -5px;
  top: -5px;
  font-size: 16px;
  color: red;
  cursor: pointer;
}

.upload-wrapper .upload__item img {
  max-width: 80px;
  max-height: 80px;
  border-radius: 12px;
  margin-right: 6px;
  object-fit: cover;
  cursor: pointer;
}

.upload__tips {
  font-size: 12px;
  color: #C0C4CC;
}
</style>
