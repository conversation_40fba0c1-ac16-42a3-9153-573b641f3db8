<template>
  <div>

    <el-form ref="form" :rules="formRules" :model="form" :inline="true" size="mini" label-width="110px">
      <el-row>
        <el-col :span="11" >
          <el-form-item label="清零周期月数" prop="zeroingCycle" >
            <el-input-number class="input1" v-model="form.zeroingCycle" :min="0" :precision="0">
              <i slot="suffix" style="margin-right: 10px">月</i>
            </el-input-number>
          </el-form-item>
        </el-col>


      </el-row>
    </el-form>


    <div slot="footer" class="dialog-footer" content="" style="margin-left: 110px;margin-top: 30px">
      <el-button type="primary" size="small" @click="submit" v-has="'18242863ff0f79115229d154af4'">确定</el-button>
      <el-button type="primary" size="small" @click="reset">重置</el-button>
    </div>


  </div>
</template>
<script>


module.exports = {
  components: {},
  data: function () {

    const cycle = (rule, value, callback) => {

      if(Number(value)<=0){
        callback(new Error('清零周期月数不能小于等于零'));
      }
      callback();


    }

    return {
      loading: null,
      form: {
        zeroingCycle:0,
      },
      formRules:{
        zeroingCycle: [
          {required: true, validator: cycle, trigger: 'change'}
        ],
      }






    }
  },
  methods: {
    loadingFunc() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
            this.$confirm('是否确定提交?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(()=>{
              this.loadingFunc();
              win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/pointsConfiguration/update"), this.form).then(res => {
                this.loading.close();
                if (res.msgCode == 0) {
                  this.$message.success("操作成功");
                  this.getList();
                }
              }).catch(e => {
                this.loading.close();
              })
            }).catch(() => {
              this.$message.info('已取消操作');
            });
        } else {
          return false;
        }

      })
    },


    reset() {
      this.$refs['form'].resetFields();
      this.getList()
    },


    getList() {
      win.OkHttp.get($.api($.apiModule.bluBucksCenter, "/mgt/pointsConfiguration/get")).then(res => {
        if (res.msgCode == 0) {
          if(res.data!=null){
            this.form=res.data
          }else{
            this.form={
              zeroingCycle:0,
            }
          }


        }
      })
    },

  },
  mounted() {
    this.$nextTick(function () {
      this.getList();
    })
  }
}
</script>
<style>


</style>