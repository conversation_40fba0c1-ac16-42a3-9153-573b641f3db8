<template>
  <div>
    <el-card class="box-card" shadow="none">
      <el-form :model="searchForm" label-position="left" ref="form" :inline="true" size="mini" label-width="75px">
        <el-row>
          <el-col :span="5">
            <el-form-item label="用户姓名">
              <el-input v-model="searchForm.userName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-button type="primary" @click="search()">查询</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- table -->
    <el-row>
      <el-col :span="24">
        <el-table :data="dataList" border stripe size="small">
          <el-table-column align="center" show-overflow-tooltip header-align="center" type="index" label="序号" width="80px"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="name" label="用户姓名"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="name" label="国家/地区"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="name" label="累计获取积分"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="name" label="累计已消耗积分"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="name" label="剩余积分"></el-table-column>
          <el-table-column align="center" label="操作">
            <template slot-scope="scope">
              <el-button size="small" type="primary" @click="jumpDetail(scope.row.id)" icon="el-icon-view">查看详情</el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
          :current-page="searchForm.pageNo"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="searchForm.pageSize"
          :total="searchForm.totalPage"
          layout="total, sizes, prev, pager, next, jumper"
        >
        </el-pagination>
      </el-col>
    </el-row>
  </div>
</template>
<script>
  module.exports = {
    name: 'userIntegralList',
    data() {
      return {
        searchForm: {
          pageNo: 1,
          pageSize: 10,
          totalPage: 0,
          userName: '',
        },
        dataList: [],
      }
    },
    methods: {
      search() {
        this.searchForm.pageNo = 1;
        this.getList();
      },
      getList() {
        win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/channel/pageList"), this.searchForm).then(res => {
          if (res.msgCode == 0) {
            this.dataList = res.data.content;
            this.searchForm.totalPage = Number(res.data.totalElements)
          }
        })
      },
      jumpDetail(id) {
        this.$router.push()
      },
      currentChangeHandle(val) {
        this.searchForm.pageNo = val
        this.getList()
      },
      // 每页数
      sizeChangeHandle(val) {
        this.searchForm.pageSize = val
        this.searchForm.pageNo = 1
        this.getList()
      },
      resetForm() {
        this.$refs.form.resetFields();
        this.getList();
      },
    },
    mounted() {
      this.getList();
    }
  }
</script>
