<template>
  <div>
    <!-- table -->
    <el-row>
      <el-col :span="24">
        <el-table :data="dataList" border stripe size="small">
          <el-table-column align="center" show-overflow-tooltip header-align="center" type="index" label="序号" width="80px"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="name" label="日期"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="name" label="渠道"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="name" label="活动"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="code" label="发放人数"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="name" label="发送数量"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="code" label="消耗人数"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="name" label="消耗数量"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="name" label="消耗比"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="code" label="回收人数"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="code" label="回收数量"></el-table-column>
        </el-table>

        <el-pagination
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
          :current-page="searchForm.pageNo"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="searchForm.pageSize"
          :total="searchForm.totalPage"
          layout="total, sizes, prev, pager, next, jumper"
        >
        </el-pagination>
      </el-col>
    </el-row>
  </div>
</template>
<script>
  module.exports = {
    name: 'intergralOverview',
    data() {
      return {
        searchForm: {
          pageNo: 1,
          pageSize: 10,
          totalPage: 0,
        },
        dataList: [],
      }
    },
    mounted() {
      this.getList();
    },
    methods: {
      search() {
        this.searchForm.pageNo = 1;
        this.getList();
      },
      getList() {
        win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/channel/pageList"), this.searchForm).then(res => {
          if (res.msgCode == 0) {
            this.dataList = res.data.content;
            this.searchForm.totalPage = Number(res.data.totalElements)
          }
        })
      },
      currentChangeHandle(val) {
        this.searchForm.pageNo = val
        this.getList()
      },
      // 每页数
      sizeChangeHandle(val) {
        this.searchForm.pageSize = val
        this.searchForm.pageNo = 1
        this.getList()
      },
    }
  }
</script>
