<template>
  <el-dialog :title="curMode.title" center :visible.sync="dialogVisible" width="60%" :close-on-click-modal="false"
             @close="close">
    <el-form :model="form" ref="form" :rules="rules" :inline="true" size="mini" label-width="110px">
      <el-row>
        <el-col :span="8">
          <el-form-item label="国家地区" prop="countryId">
            <el-select v-model="form.countryId" placeholder="请选择" clearable filterable :disabled="viewOnly" @change="getCurrency">
              <el-option
                  v-for="item in countryList"
                  :key="item.geoNameId"
                  :label="item.countryName +'/'+ item.countryCode"
                  :value="item.geoNameId">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="消费币种" prop="currency">
            <el-input type="text" v-model="form.currency" clearable disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="货币单位" prop="currencyUnit">
            <el-input type="text" v-model="form.currencyUnit" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-form-item label="等级说明" prop="content" class="dialog_special-item">
          <mavon-editor ref="mavonEditor" v-model="form.instructionsHtml" @save="saveMarkDown"
                        @change="changeMarkDown"/>
        </el-form-item>
      </el-row>

      <el-row class="dialog_row">
        <el-col :span="24">
          <el-form-item label="等级设置" prop="ruleList">
            <el-button v-if="!viewOnly" size="mini" type="primary" @click="add">添加等级</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-table :data="form.ruleList" border stripe size="small" v-loading="tableLoading">
            <el-table-column align="center" show-overflow-tooltip prop="" label="等级">
              <template slot-scope="{row}">
                lv{{ row.index }}
              </template>
            </el-table-column>
            <el-table-column align="center" show-overflow-tooltip prop="logoUrl" label="等级LOGO">
              <template slot-scope="{row}">
                <img :src="row.logoUrl ? row.logoUrl : '/BluBucksC/assets/images/default.png'" alt="加载失败"
                     class="dialog_table-img">
              </template>
            </el-table-column>
            <el-table-column align="center" show-overflow-tooltip prop="name" label="等级名称">
            </el-table-column>
            <el-table-column align="center" show-overflow-tooltip prop="consumptionThreshold" label="消费门槛">
            </el-table-column>
            <el-table-column align="center" show-overflow-tooltip prop="userNum" label="客户数量"></el-table-column>
            <el-table-column align="center" show-overflow-tooltip prop="giftPoints" label="赠送积分数">
            </el-table-column>
            <el-table-column align="center" show-overflow-tooltip prop="" label="等级权益">
              <template slot-scope="{row}">
                <el-button size="mini" type="primary" @click="config(row)">配置</el-button>
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作" min-width="200px">
              <template slot-scope="{row}">
                <el-button v-if="!viewOnly" size="mini" type="primary" @click="edit(row)">编辑</el-button>
                <el-button v-if="!viewOnly" size="mini" type="primary" @click="del(row.index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination @size-change="sizeChangeHandle"
                         @current-change="currentChangeHandle"
                         :current-page="page.pageNo"
                         :page-sizes="[10, 20, 50, 100]"
                         :page-size="page.pageSize"
                         :total="page.totalPage"
                         layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer" class="dialog-footer" content="">
      <el-button v-if="!curMode.disabled" type="primary" @click="submit">保 存</el-button>
      <el-button @click="close">取 消</el-button>
    </div>
    <add-level ref="handleAddLevel" @success="addLevelCallback"></add-level>
    <config-level ref="handleConfigLevel" @success="configLevelCallback"></config-level>
  </el-dialog>
</template>

<script>
module.exports = {
  name: 'pointsLevelAdd',
  components: {
    'add-level': httpVueLoader('/BluBucksC/level/components/addLevel.vue'),
    'config-level': httpVueLoader('/BluBucksC/level/components/configLevel.vue'),
    "file-upload": httpVueLoader("/BluBucksC/components/file-upload.vue"),
  },
  props: {
    countryList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: false,
      mode: [
        {title: '新增积分等级', disabled: false, path: '/mgt/pointsLevel/add', method: 'post'},
        {title: '编辑积分等级', disabled: false, path: '/mgt/pointsLevel/edit', method: 'put'},
        {title: '查看积分等级', disabled: true, path: '', method: ''},
      ],
      curMode: {},
      curModeIndex: 0,
      viewOnly: false,
      form: {
        countryId: '',
        currency: '',
        currencyUnit: '',
        instructions: '',
        instructionsHtml: '',
        ruleList: [],
      },
      tableLoading: false,
      page: {
        pageNo: 1,
        pageSize: 10,
        totalPage: 0,
      },
      rules: {
        countryId: [
          {required: true, message: '请选择国家/地区', trigger: 'blur',}
        ],
        ruleList: [
          {
            required: true, message: '请至少添加一个积分等级', triggers: ['blur','change'],
            validator: (rule, value, callback) => {
              if(!this.form.ruleList.length) {
                callback(new Error('请至少添加一个积分等级'))
              } else {
                callback();
              }
            }
          }
        ],
      }
    }
  },
  methods: {
    // 初始化弹窗
    open(curModeIndex = 0, _id = {}) {
      this.dialogVisible = true
      this.curModeIndex = +curModeIndex
      this.curMode = this.mode[this.curModeIndex]
      this.viewOnly = this.curMode.disabled
      if (+curModeIndex !== 0) {
        this.getData(_id)
      }
    },
    getData(_id) {
      win.OkHttp.get($.api($.apiModule.bluBucksCenter, `/mgt/pointsLevel/detail?id=${_id}`))
          .then(res => {
            if (+res.msgCode === 0) {
              this.form = res.data || {}
            }
          })
          .catch(err => {
          })
    },

    // 带入币种操作
    getCurrency(_countryId) {
      const obj = this.countryList.find(item => item.geoNameId === _countryId)
      if(obj) {
        this.form.currency = obj.currency
      }
    },
    // 富文本操作
    saveMarkDown(markdown, html) {
      this.form.instructionsHtml = html;
      this.form.instructions = markdown;
    },
    changeMarkDown(markdown, html) {
      this.form.instructionsHtml = html;
      this.form.instructions = markdown;
    },
    // 附件操作
    updateFileList(_list) {
      this.fileList = _list
    },

    // 行操作
    add() {
      this.$refs.handleAddLevel.open(this.form.ruleList, 0)
    },
    edit(row) {
      this.$refs.handleAddLevel.open(this.form.ruleList, 1, row)
    },
    addLevelCallback(_list) {
      this.form.ruleList = _list
    },
    del(index) {
      this.$confirm('此操作将删除该行数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.form.ruleList.splice(index - 1, 1)
        this.form.ruleList = this.form.ruleList.map((item, idx) => {
          if (idx >= index - 1) {
            return {
              ...item,
              index: item.index - 1
            }
          } else return item
        })
      })
    },
    config(row) {
      if(this.form.countryId) {
        const mode = (this.curModeIndex === 2) ? 1 : 0
        this.$refs.handleConfigLevel.open(+mode, row, this.form.countryId)
      } else {
        this.$message.warning('请先选择国家地区，再对积分等级进行配置')
      }
    },
    configLevelCallback(row) {
      this.form.ruleList.splice(row.index - 1, 1, row)
    },

    // 关闭弹窗
    close() {
      Object.assign(this.form, {
        countryId: '',
        currency: '',
        instructions: '',
        instructionsHtml: '',
        ruleList: [],
      })
      this.curMode = {}
      this.curModeIndex = 0
      this.$refs.form.clearValidate()
      this.dialogVisible = false
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const params = Object.assign({}, this.form)
          win.OkHttp[this.curMode.method]($.api($.apiModule.bluBucksCenter, this.curMode.path), params)
              .then(res => {
                if (+res.msgCode === 0) {
                  this.$message.success(res.message)
                  this.$emit('success')
                  this.close()
                }
              })
              .catch(err => {
              })
        }
      })
    },


    // 分页
    sizeChangeHandle(_pageSize) {
      this.page.pageSize = _pageSize
      this.page.pageNo = 1
      this.getTable()
    },
    currentChangeHandle(_pageNo) {
      this.page.pageNo = _pageNo
      this.getTable()
    }
  }
}
</script>
<style scoped>
.dialog_special-item {
  display: flex;
}

.dialog_row {
  margin-bottom: 8px;
}

.dialog_table-img {
  width: 50px;
  height: 50px;
  border-radius: 12px;
}

</style>
