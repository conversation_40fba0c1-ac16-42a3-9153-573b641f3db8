<template>
  <el-dialog title="配置等级权益" center :visible.sync="dialogVisible"
             width="50%" :close-on-click-modal="false" @close="close" :append-to-body="true">
    <el-table :data="tableData" border stripe size="small" v-loading="tableLoading"
              @selection-change="handleSelectionChange" ref="multipleTable" :row-key="row => row.id">
      <el-table-column align="center" show-overflow-tooltip header-align="center"
                       type="selection" :reserve-selection="true" :selectable="handleTableMode"></el-table-column>
      <el-table-column align="center" show-overflow-tooltip prop="countryName" label="国家/地区"></el-table-column>
      <el-table-column align="center" show-overflow-tooltip prop="name" label="权益名称"></el-table-column>
      <el-table-column align="center" show-overflow-tooltip prop="" label="权益图标">
        <template slot-scope="{row}">
          <img :src="row.iconUrl ? row.iconUrl : '/BluBucksC/assets/images/default.png'" alt="加载失败" class="dialog_table-img">
        </template>
      </el-table-column>
      <el-table-column align="center" show-overflow-tooltip prop="instructions" label="权益介绍"></el-table-column>
      <el-table-column align="center" show-overflow-tooltip prop="" label="有效期" width="350px">
        <template slot-scope="{row}">
          <span>[{{row.effectiveStart}}, {{row.effectiveEnd}}]</span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle"
                   @current-change="currentChangeHandle"
                   :current-page="page.pageNo"
                   :page-sizes="[10, 20, 50, 100]"
                   :page-size="page.pageSize"
                   :total="page.totalPage"
                   layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <div slot="footer" class="dialog-footer" content="">
      <el-button class="button-radius" type="primary" @click="close" plain>取消</el-button>
      <el-button class="button-radius" type="primary" @click="confirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
module.exports = {
  name: "pointsLevelConfigLevel.vue",
  data() {
    return {
      dialogVisible: false,
      tableData: [],
      tableLoading: false,
      mode: [
        {title: '新增配置', disabled: true},
        {title: '查看配置', disabled: false}
      ],
      curMode: {},
      curModeIndex: '',
      curCountryId: '',
      page: {
        pageNo: 1,
        pageSize: 10,
        totalPage: 0,
      },
      data: {},
      checkedList: [],
    }
  },
  methods: {
    // 初始化弹窗
    open(curModeIndex = 0, data = {}, countryId = '') {
      this.curModeIndex = +curModeIndex
      this.curMode = this.mode[this.curModeIndex]
      this.data = data
      this.curCountryId = countryId
      this.getTable()
      this.dialogVisible = true
    },
    getTable() {
      const params = Object.assign({}, {
        countryId: this.curCountryId,
        pageNo: this.page.pageNo,
        pageSize: this.page.pageSize
      })
      win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/pointsInterests/page"), params).then(res => {
        if (+res.msgCode === 0) {
          this.tableData = res.data.content || [];
          this.page.totalPage = Number(res.data.totalElements)
          // 勾选已选中选项
          this.checkedSelections()
        }
      })
    },

    // 控制多选表格是否可以编辑
    handleTableMode(row, index) {
      return this.curMode.disabled
    },
    // 勾选已选中选项
    checkedSelections() {
      const checkedList = this.data.interestsIdList
      if(checkedList && checkedList.length) {
        checkedList.forEach(item1 => {
          this.tableData.forEach(item2 => {
            if(String(item1) === String(item2.id)) {
              this.$refs.multipleTable.toggleRowSelection(item2)
            }
          })
        })
      }
    },
    // 多选表格结果存储
    handleSelectionChange(_list) {
      this.checkedList = _list
    },

    // 关闭弹窗
    close() {
      this.data = {}
      this.tableData = []
      this.$refs.multipleTable.clearSelection();
      this.dialogVisible = false
    },
    confirm() {
      this.data.interestsIdList = []
      this.checkedList.forEach(item => {
        this.data.interestsIdList.push(item.id)
      })
      this.$emit("success", this.data)
      this.close()
    },

    // 分页
    sizeChangeHandle(_pageSize) {
      this.page.pageSize = _pageSize
      this.page.pageNo = 1
      this.getTable()
    },
    currentChangeHandle(_pageNo) {
      this.page.pageNo = _pageNo
      this.getTable()
    }
  }
}
</script>

<style scoped>
.dialog_table-img {
  width: 50px;
  height: 50px;
  border-radius: 12px;
}
</style>
