<template>
  <el-dialog
      :title="curMode.title" center :visible.sync="dialogVisible" width="30%"
      :close-on-click-modal="false" :append-to-body="true" @close="close">
    <el-form ref="form" :model="form" label-position="left" :rules="rules" label-width="100px">
      <el-form-item label="等级名称" prop="name">
        <el-input type="text" v-model="form.name" placeholder="请输入" clearable></el-input>
      </el-form-item>
      <el-form-item label="消费门槛" prop="consumptionThreshold">
        <el-input type="text" v-model="form.consumptionThreshold" placeholder="请输入" clearable maxlength="20"></el-input>
      </el-form-item>
      <el-form-item label="赠送积分数" prop="giftPoints">
        <el-input type="text" v-model="form.giftPoints" placeholder="请输入" clearable maxlength="9"></el-input>
      </el-form-item>
      <el-form-item label="等级LOGO" prop="logoUrl">
        <file-upload
            v-if="dialogVisible"
            ref="handleFile"
            class="file-upload"
            :limit="1"
            :file-list="fileList"
            @change="updateFileList">
        </file-upload>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer" content="">
      <el-button type="primary" @click="submit">保 存</el-button>
      <el-button @click="close">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
module.exports = {
  name: "pointsLevelAddLevel.vue",
  components: {
    "file-upload": httpVueLoader("/BluBucksC/components/file-upload.vue"),
  },
  data() {
    return {
      dialogVisible: false,
      mode: [
        {title: '新增等级', path: '', method: ''},
        {title: '编辑等级', path: '', method: ''}
      ],
      curMode: {},
      curModeIndex: '',
      form: {
        name: '',
        consumptionThreshold: '',
        giftPoints: '',
        logoUrl: '',
        interestsIdList: [],
      },
      fileList: [],
      ruleList: [],
      ruleListIndex: null,
      minThreshold: -1,
      maxThreshold: Infinity,
      rules: {
        name: [
          {
            required: true, message: '请输入等级名称', trigger: 'blur',
            validator: (rule, value, callback) => {
              if (!value || value.trim() === '') {
                callback(new Error('请输入等级名称'))
              }
              else {
                callback();
              }
            },
          }
        ],
        consumptionThreshold: [
          {
            required: true, trigger: 'blur',
            validator: (rule, value, callback) => {
              const digitReg = /^\d{1,}$/
              const num = new BigNumber(value)
              if (!value && value !== 0) {
                callback(new Error('请输入消费门槛'))
              }
              else if(!String(value).match(digitReg)) {
                callback(new Error('只能输入数字'))
              }
              else if(num.comparedTo(this.minThreshold) === -1 || num.comparedTo(this.minThreshold) === 0) {
                callback(new Error(`当前等级 消费门槛最小值: ${this.minThreshold.plus(1)}`))
              }
              else if(num.comparedTo(this.maxThreshold) === 1 || num.comparedTo(this.maxThreshold) === 0) {
                callback(new Error(`当前等级 消费门槛最大值: ${this.maxThreshold.minus(1)}`))
              }
              else {
                callback()
              }
            },
          }
        ],
        giftPoints: [
          {
            required: true, message: '请输入赠送积分数', trigger: 'blur',
            validator: (rule, value, callback) => {
              const digitReg = /^\d{1,}$/
              if (!value && value !== 0) {
                callback(new Error('请输入赠送积分数'))
              }
              else if(!String(value).match(digitReg)) {
                callback(new Error('只能输入数字'))
              }
              else {
                callback();
              }
            },
          }
        ],
        logoUrl: [
          {
            required: true, message: '请上传等级LOGO', trigger: 'blur',
            validator: (rule, value, callback) => {
              if(!this.fileList[0] || !this.fileList[0].fileUrl) {
                callback(new Error('请上传附件'))
              }
              else {
                callback()
              }
            }
          }
        ]
      },
    }
  },
  methods: {
    // 初始化弹窗
    open(list = [], curModeIndex = 0, data = {}) {
      this.curModeIndex = +curModeIndex
      this.curMode = this.mode[this.curModeIndex]
      this.ruleList = list
      if(this.curModeIndex === 1) {
        this.form = JSON.parse(JSON.stringify(data))
        this.form.logoUrl && (this.fileList = [{fileUrl:this.form.logoUrl}])
        this.ruleListIndex = this.form.index
      }
      else {
        this.ruleListIndex = this.ruleList.length + 1
      }
      this.getThreshold()
      this.dialogVisible = true
    },

    // 获取当前消费门槛范围
    getThreshold() {
      const len = this.ruleList.length
      if(this.ruleListIndex - 2 >= 0) {
        this.minThreshold = new BigNumber(this.ruleList[this.ruleListIndex-2]?.consumptionThreshold ?? -1)
      }
      if(this.ruleListIndex >= 0 && this.ruleListIndex < len) {
        this.maxThreshold = new BigNumber(this.ruleList[this.ruleListIndex]?.consumptionThreshold ?? 'Infinity')
      }
    },
    // 附件操作
    updateFileList(_list) {
      this.fileList = _list
    },

    // 关闭弹窗
    close() {
      Object.assign(this.form, {
        name: '',
        consumptionThreshold: '',
        giftPoints: '',
        logoUrl: '',
        interestsIdList: []
      })
      this.fileList = []
      this.ruleList = []
      this.ruleListIndex = null
      this.minThreshold = -1
      this.maxThreshold = Infinity
      this.$refs.form.clearValidate()
      this.dialogVisible = false
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if(valid) {
          this.form.logoUrl = this.fileList[0] ? this.fileList[0].fileUrl : ''
          const params = Object.assign({}, this.form, { index: this.ruleListIndex })
          if(this.curModeIndex === 0) {
            this.ruleList.push(params)
          }
          else {
            this.ruleList.splice(this.ruleListIndex - 1, 1, params)
          }
          this.$emit('success', this.ruleList)
          this.$message.success('OK')
          this.close()
        }
        else {
          this.$message.info('等级添加失败')
        }
      })
    },
  }
}
</script>

<style scoped>

</style>
