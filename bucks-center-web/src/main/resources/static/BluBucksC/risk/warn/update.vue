<template xmlns="http://www.w3.org/1999/html">
  <div>
    <el-dialog :title="title" :visible.sync="dialogVisible" width="60%" @close="close()" center>
      <el-card class="box-card" shadow="none">
        <el-form ref="form" :rules="rules" :model="form" :inline="true" label-width="120px">

          <el-row>

            <el-col :span="8">
              <el-form-item label="类型：" prop="abnType">
                <el-select filterable clearable v-model="form.abnType" class="input2" placeholder="请选择">
                  <el-option
                      v-for="item in abnList"
                      :key="item.abnType"
                      :label="item.name"
                      :value="item.abnType">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>


          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="时间单位：" prop="timeUnit" v-if="form.ruleType == 1">
                <el-radio-group v-model="form.timeUnit" size="medium">
                  <el-radio border :label="1">每分钟</el-radio>
                  <el-radio border :label="2">每小时</el-radio>
                  <el-radio border :label="3">每天</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="单位时间次数:" prop="unitTimeCount" v-if="form.ruleType == 1">
                <el-input-number class="input2" v-model="form.unitTimeCount"></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>

        </el-form>
      </el-card>
      <span slot="footer" class="dialog-footer">
          <el-button @click="save()" type="primary">保 存</el-button>
          <el-button @click="close()">取 消</el-button>
        </span>
    </el-dialog>
  </div>
</template>

<script>

const abnList = [{abnType: 1, name: "积分发放"}, {abnType: 2, name: "积分回退"}];
const timeUnitList = [{type: 1, name: "分钟"}, {abnType: 2, name: "小时"}, {abnType: 3, name: "天"}];

module.exports = {
  data: function () {

    return {
      timeUnitList: timeUnitList,
      abnList: abnList,
      dialogVisible: false,
      title: "修改规则",
      form: {
        id: '',
        ruleType: 1,
        abnType: '',
        timeUnit: '',
        unitTimeCount: ''
      },
      loading: null,
      rules: {
        abnType: [
          {required: true, message: '异常规则不能为空', trigger: 'change'},
        ]
      }
    }
  },
  methods: {

    close() {
      this.dialogVisible = false
      this.$refs.form.resetFields();
      this.$emit("close")
    },
    save() {

      if(this.form.ruleType === 1 && this.form.timeUnit === ''){
        this.$message('时间单位不能为空')
        return
      }
      if(this.form.ruleType === 1 && (this.form.unitTimeCount <=0 || this.form.unitTimeCount=='')){
        this.$message('单位时间次数有误')
        return
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/pointsAbnDataRule/update"), this.form).then(res => {
            if (res.msgCode == 0) {
              this.$message.success("保存成功");
              this.dialogVisible = false;
              this.$emit("success");
            }
          })
        }
      })
    },

    visible(row) {
      this.dialogVisible = true;
      this.form = JSON.parse(JSON.stringify(row));
    },
    loadingFunc() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.dialogVisible = false;
    }
  }
}
</script>

<style scoped>

.input2 {
  width: 220px;
}

</style>
