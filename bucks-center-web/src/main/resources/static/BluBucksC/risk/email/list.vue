<template>
  <div>
    <el-card class="box-card" shadow="none">
      <el-form ref="form" :model="form" :inline="true" size="mini" label-width="90px">


        <el-row>
          <el-col :span="6">
            <el-form-item label=" ">
              <el-button type="primary" @click="search()">查询</el-button>
              <el-button type="primary" @click="resetForm()">重置</el-button>
              <el-button type="primary" v-has="'182334cebbef79115229d154abd'" @click="addItem()">新增</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-row>
      <el-col :span="24">
        <el-table :data="dataList" border stripe v-loading="dataListLoading" size="small">
          <el-table-column align="center" show-overflow-tooltip header-align="center" type="index"></el-table-column>

          <el-table-column align="center" show-overflow-tooltip prop="name" label="姓名" header-align="center">
          </el-table-column>

          <el-table-column align="center" show-overflow-tooltip prop="email" label="邮件" header-align="center">
          </el-table-column>

          <el-table-column align="center" show-overflow-tooltip prop="department" label="部门" header-align="center">
          </el-table-column>


          <el-table-column align="center" show-overflow-tooltip prop="enable" label="是否通知"
                           header-align="center">
            <template slot-scope="scope">
              <el-tag type="success" v-if="scope.row.isSend=== true">{{ "是" }}</el-tag>
              <el-tag type="danger" v-if="scope.row.isSend === false ">{{ "否" }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column align="center" show-overflow-tooltip prop="creTime" label="创建时间" width="200px" header-align="center"></el-table-column>

          <el-table-column align="center" width="300" label="操作" header-align="center">
            <template slot-scope="scope">

              <el-button size="mini" type="primary" v-has="'182334c6fedf79115229d154abb'" @click="update(scope.row)">编辑</el-button>

              <el-button
                  size="mini" type="danger"
                  @click="del(scope.row.id)" v-has="'182334c1ff5f79115229d154aba'" >删除
              </el-button>

              <el-button
                  v-if="scope.row.isSend == true"
                  size="mini" type="danger"
                  @click="enableOrDisable(scope.row.id)" v-has="'182334b358ef79115229d154ab7'">取消通知
              </el-button>

              <el-button
                  v-if="scope.row.isSend == false"
                  size="mini" type="success"
                  @click="enableOrDisable(scope.row.id)" v-has="'182334b358ef79115229d154ab7'">开启通知
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination @size-change="sizeChangeHandle"
                       @current-change="currentChangeHandle"
                       :current-page="form.pageNo"
                       :page-sizes="[10, 20, 50, 100]"
                       :page-size="form.pageSize"
                       :total="form.totalPage"
                       layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>
      </el-col>
    </el-row>
    <el-row>
      <el-col>
        <add ref="add" @success="getList()"/>
      </el-col>
    </el-row>
    <el-row>
      <el-col>
        <edit ref="edit" @success="getList()"/>
      </el-col>
    </el-row>

  </div>
</template>
<script>
module.exports = {
  components: {
    "add": httpVueLoader("/BluBucksC/risk/email/add.vue"),
    "edit": httpVueLoader("/BluBucksC/risk/email/update.vue"),
  },
  data: function () {
    return {
      form: {
        pageNo: 1,
        pageSize: 10,
        totalPage: 0,
        isSend: "",
      },
      dataListLoading: false,
      dataList: [],
    }
  },
  methods: {

    enableOrDisable(id) {
      win.OkHttp.get($.api($.apiModule.bluBucksCenter, "/mgt/emailWarn/enableOrDisable/") + id).then(res => {
        if (res.msgCode == 0) {
          this.$message.success("操作成功");
          this.getList(this.form.id);
        }
      })
    },

    del(id) {
      this.$confirm('确认删除? ', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        win.OkHttp.get($.api($.apiModule.bluBucksCenter, "/mgt/emailWarn/del/" + id)).then(res => {
          if (res.msgCode == 0) {
            this.$message.success("操作成功");
            this.getList(this.form.id);
          }
        })
      }).catch(() => {
      });
    },

    search() {
      this.form.pageNo = 1;
      this.getList();
    },
    getList() {
      this.dataListLoading = true
      win.OkHttp.post($.api($.apiModule.bluBucksCenter, '/mgt/emailWarn/page'), this.form).then(res => {
        if (res.msgCode == 0) {
          this.dataList = res.data.content;
          this.form.totalPage = Number(res.data.totalElements)
        }
      })
      this.dataListLoading = false
    },
    // 当前页
    currentChangeHandle(val) {
      this.form.pageNo = val
      this.getList()
    },
    // 每页数
    sizeChangeHandle(val) {
      this.form.pageSize = val
      this.form.pageNo = 1
      this.getList()
    },

    addItem() {
      this.$refs.add.visible(true);
    },
    update(row) {
      this.$refs.edit.visible(row);
    },

    resetForm() {
      this.$refs["form"].resetFields();
      this.getList();
    },
  },
  mounted() {
    this.$nextTick(function () {
      this.getList();
    })
  }
}
</script>