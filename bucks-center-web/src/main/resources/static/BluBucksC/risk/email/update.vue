<template xmlns="http://www.w3.org/1999/html">
  <div>
    <el-dialog :title="title" :visible.sync="dialogVisible" width="30%" @close="close()" center>
      <el-card class="box-card" shadow="none">
        <el-form ref="form" :rules="rules" :model="form" :inline="true" label-width="120px">

            <el-row>
              <el-col :span="24">
                <el-form-item label="姓名:" prop="name" >
                  <el-input class="input2" v-model="form.name"></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="邮件:" prop="email" >
                  <el-input class="input2" v-model="form.email"></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="部门:" prop="department" >
                  <el-input class="input2" v-model="form.department"></el-input>
                </el-form-item>
              </el-col>
            </el-row>

        </el-form>
      </el-card>
      <span slot="footer" class="dialog-footer">
          <el-button @click="save()" type="primary">保 存</el-button>
          <el-button @click="close()">取 消</el-button>
        </span>
    </el-dialog>
  </div>
</template>

<script>

module.exports = {
  data: function () {

    return {
      dialogVisible: false,
      title: "修改人员",
      form: {
        id:"",
        email: '',
        name: '',
        department: ''
      },
      loading: null,
      rules: {
        name: [
          {required: true, message: '姓名不能为空', trigger: 'change'},
        ],
        email: [
          {required: true, message: '邮件不能为空', trigger: 'change'},
        ],
        department: [
          {required: true, message: '部门不能为空', trigger: 'change'},
        ]
      }
    }
  },
  methods: {

    close() {
      this.dialogVisible = false
      this.$refs.form.resetFields();
      this.$emit("close")
    },
    save() {

      this.$refs.form.validate((valid) => {
        if (valid) {
          win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/emailWarn/update"), this.form).then(res => {
            if (res.msgCode == 0) {
              this.$message.success("保存成功");
              this.dialogVisible = false;
              this.$emit("success");
            }
          })
        }
      })
    },

    visible(row) {
      this.dialogVisible = true;
      this.form = JSON.parse(JSON.stringify(row));
    },
    loadingFunc() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.dialogVisible = false;
    }
  }
}
</script>

<style scoped>

.input2 {
  width: 220px;
}

</style>
