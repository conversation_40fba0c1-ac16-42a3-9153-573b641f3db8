<template>
  <div>
    <el-dialog title="当前赛季" width="80%" :visible.sync="dialogClose " @close="close()" center>
      <el-form ref="form" :model="form" :inline="true" size="medium" label-width="100px">
        <el-table :data="detailList" border stripe size="small">
          <el-table-column align="center" show-overflow-tooltip type="index"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="countryId" label="国家/地区"
                           :formatter="countryFormat"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="activityId" label="活动"
                           :formatter="activityFormat"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="channelCode" label="来源渠道"
                           :formatter="channelFormat"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="userName" label="用户名称"
                           header-align="center"></el-table-column>
<!--          <el-table-column align="center" show-overflow-tooltip prop="userAccount" label="用户账户"-->
<!--                           header-align="center"></el-table-column>-->
          <el-table-column align="center" show-overflow-tooltip prop="points" label="积分值"
                           header-align="center"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="abnType" label="类型" header-align="center">
            <template slot-scope="scope">
              <el-tag type="danger" v-if="scope.row.abnType=== 1">{{ "积分发放" }}</el-tag>
              <el-tag type="success" v-if="scope.row.abnType=== 2">{{ "积分回退" }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="journalId" label="流水ID" width="167px"
                           header-align="center"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="orderId" label="订单ID" width="167px"
                           header-align="center"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="desc" label="异常说明" width="167px"
                           header-align="center"></el-table-column>

          <el-table-column align="center" show-overflow-tooltip prop="abnTime" label="原数据时间" width="167px"
                           header-align="center"></el-table-column>

          <el-table-column align="center" show-overflow-tooltip prop="creTime" label="捕获数据生成时间" width="167px"
                           header-align="center"></el-table-column>

          <el-table-column align="center" label="操作" header-align="center" width="300">
            <template slot-scope="scope">
              <el-button size="mini" type="primary" @click="config(scope.row.activityId)" v-if="scope.row.activityId!==null&&scope.row.activityId!==''">活动配置</el-button>
              <el-button size="mini" type="primary" @click="rule(scope.row)" v-has="'1826bb7e11cf79115229d154ba6'">规则</el-button>
              <el-button
                  v-if="scope.row.isPullBlack == 1"
                  size="mini" type="danger"
                  @click="remove(scope.row)" v-has="'1820589df51f79115229d154a65'">取消拉黑
              </el-button>

              <el-button
                  v-if="scope.row.isPullBlack == 0"
                  size="mini" type="success"
                  @click="join(scope.row)" v-has="'182053297f7f79115229d154a62'">拉黑
              </el-button>
            </template>
          </el-table-column>
        </el-table>


        <el-pagination @size-change="sizeChangeHandle"
                       @current-change="currentChangeHandle"
                       :current-page="form.pageNo"
                       :page-sizes="[10, 20, 50, 100]"
                       :page-size="form.pageSize"
                       :total="form.totalPage"
                       layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>


      </el-form>
      <span slot="footer" class="dialog-footer">
          <el-button @click="close()">关 闭</el-button>
        </span>
    </el-dialog>

    <el-row>
      <el-col>
        <join ref="join" @success="getDetailList()"/>
      </el-col>
    </el-row>

    <el-row>
      <el-col>
        <rule ref="rule" @success="getDetailList()"/>
      </el-col>
    </el-row>

    <el-row>
      <el-col>
        <config ref="config" @success="getDetailList()"/>
      </el-col>
    </el-row>

  </div>
</template>

<script>
module.exports = {

  components: {
    "join": httpVueLoader("/BluBucksC/risk/statistics/join-black.vue"),
    "config": httpVueLoader("/BluBucksC/risk/statistics/config.vue"),
    "rule": httpVueLoader("/BluBucksC/risk/statistics/rule.vue"),
  },

  data: function () {
    return {
      form: {
        abnDays: "",
        pageNo: 1,
        pageSize: 10,
        totalPage: 0,
      },

      blackForm: {
        uid: "",
        countryId: "",
      },

      detailList: [],
      dialogClose: false,
      channel: [],
      countryList: [],
      activity: []
    }
  },
  methods: {

    channelFormat(row) {
      if (row.channelCode === null || row.channelCode === '') return row.channelCode;
      let result = null;
      this.channel.forEach(function (item) {
        if (item.code === row.channelCode) {
          result = item.name;
        }
      })
      return result;

    },


    activityFormat(row) {
      if (row.activityId === null || row.activityId === '') return row.activityId;
      let result = null;
      this.activity.forEach(function (item) {
        if (item.id === row.activityId) {
          result = item.name;
        }
      })
      return result;
    },
    countryFormat(row) {
      if (row.countryId === null || row.countryId === '') return row.countryId;


      let result = null;
      this.countryList.forEach(function (item) {
        if (item.geoNameId === row.countryId) {
          result = item.countryName;
        }
      })
      return result;


    },


    currentChangeHandle(val) {
      this.form.pageNo = val
      this.getDetailList()
    },
    // 每页数
    sizeChangeHandle(val) {
      this.form.pageSize = val
      this.form.pageNo = 1
      this.getDetailList()
    },
    getDetailList() {

      win.OkHttp.get($.api($.apiModule.midpMasterData, "~/iso/country/v1/localized/all-countries")).then(res => {
        if (res.msgCode == 0) {
          this.countryList = res.data.countries;
        }
      })

      win.OkHttp.get($.api($.apiModule.bluBucksCenter, "/mgt/channel/list")).then(res => {
        if (res.msgCode == 0) {
          this.channel = res.data;
        }
      })

      win.OkHttp.get($.api($.apiModule.bluBucksCenter, "/mgt/activity/list")).then(res => {
        if (res.msgCode == 0) {
          this.activity = res.data;
        }
      })


      win.OkHttp.post($.api($.apiModule.bluBucksCenter, '/mgt/pointsAbnData/detailList'), this.form).then(res => {
        if (res.msgCode == 0) {
          this.detailList = res.data.content;
          this.form.totalPage = Number(res.data.totalElements)
          this.form.total = Number(res.data.totalElements)
        }
      })
    },

    remove(row) {
      this.blackForm.uid = row.uid;
      this.blackForm.countryId = row.countryId;
      this.$confirm('是否确认将此用户移出黑名单', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/blacklist/out"), this.blackForm).then(res => {
          if (res.msgCode == 0) {
            this.$message.success("操作成功");
            this.getDetailList();
          } else {
            this.$message.error(res.message);
          }
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
      });
    },

    join(row) {
      this.$refs.join.visible(row);
    },
    config(row) {
      this.$refs.config.visible(row);
    },
    rule(row) {
      this.$refs.rule.visible(row);
    },

    init(val) {
      this.form.abnDays = val;
      this.dialogClose = true;
      this.getDetailList();
    },
    close() {
      this.dialogClose = false;
      this.form.pageNo=1;
      this.form.pageSize=10;
    },

  }
}
</script>

<style scoped>

.el-form-item {
  margin-bottom: 8px;
}
</style>
