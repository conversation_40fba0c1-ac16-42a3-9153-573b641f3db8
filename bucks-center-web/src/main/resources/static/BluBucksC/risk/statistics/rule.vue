<template xmlns="http://www.w3.org/1999/html">
  <div>
    <el-dialog :title="title" :visible.sync="dialogVisible" width="60%" @close="close()" center>
        <el-form ref="form"  :model="form" :inline="true" label-width="120px">
          <el-row>

            <el-col :span="8">
              <el-form-item label="活动：" prop="activityId" >
                <el-select v-model="form.activityId" class="input2" disabled>
                  <el-option
                      v-for="item in activityList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="规则：" prop="ruleType" >
                <el-select disabled filterable clearable v-model="form.ruleType" class="input2">
                  <el-option
                      v-for="item in ruleList"
                      :key="item.ruleType"
                      :label="item.name"
                      :value="item.ruleType">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="渠道标识:" prop="channelCode">
                <el-input class="input2" v-model="form.channelCode" disabled></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="积分值:" prop="points">
                <el-input class="input2" v-model="form.points" disabled></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="额外积分奖励:" prop="extraReward" >
                <el-input class="input2" v-model="form.extraReward" disabled> </el-input>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="发放门槛:" prop="earnLimit" >
                <el-input class="input2" v-model="form.earnLimit" disabled></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="每日获取次数:" prop="dailyNum">
                <el-input class="input2" v-model="form.spendLimit" disabled></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="发放类型" prop="earnType">
                <el-tag type="primary" v-if="form.earnType == 1">{{ "一次性" }}</el-tag>
                <el-tag type="success" v-if="form.earnType == 2">{{ "无限次" }}</el-tag>
              </el-form-item>
            </el-col>

          </el-row>
        </el-form>
      <span slot="footer" class="dialog-footer">
          <el-button @click="close()">取 消</el-button>
        </span>
    </el-dialog>
  </div>
</template>
<script>
const ruleList = [{ruleType: "1", name: "签到"},{ruleType: "2", name: "货币兑换"},{ruleType: "3", name: "光伏发电"},{ruleType: "9", name: "通用"}];
module.exports = {
  data: function () {

    return {
      dialogVisible: false,
      title: "规则详情",
      ruleList: ruleList,
      countryList: [],
      activityList: [],
      channelList: [],
      form: {
        earnType: '',
        extraReward: "",
        earnLimit: "",
        activityId: '',
        dailyNum: '',
        enable: "",
        ruleType: "",
        points: ''
      },

      ruleForm:{
        channelCode:"",
        activityId:""
      },
      loading: null,
    }
  },
  methods: {
    getCountry(){
      win.OkHttp.get($.api($.apiModule.midpMasterData, "~/iso/country/v1/localized/all-countries")).then(res => {
        if (res.msgCode == 0) {
          this.countryList = res.data.countries;
        }
      })
    },

    getChannel(){
      win.OkHttp.get($.api($.apiModule.bluBucksCenter, "/mgt/channel/list")).then(res => {
        if (res.msgCode == 0) {
          this.channelList = res.data;
        }
      })
    },
    getRule(row){
      this.ruleForm.activityId = row.activityId;
      this.ruleForm.channelCode = row.channelCode;
      win.OkHttp.post($.api($.apiModule.bluBucksCenter, '/mgt/pointsAbnData/earnRuleDetail'),this.ruleForm).then(res => {
        if (res.msgCode === 0) {
          if (res.data) {
            this.form = res.data;
            this.getCountry();
            this.getChannel();
            this.getActivity();
            this.dialogVisible = true;
          }
          else {
            this.$message.error("规则不存在/已更改");
          }
        }
      })
    },

    getActivity(){
      win.OkHttp.get($.api($.apiModule.bluBucksCenter, "/mgt/activity/list")).then(res => {
        if (res.msgCode == 0) {
          this.activityList = res.data;
        }
      })
    },

    visible(row) {
      this.getRule(row);

    },
    loadingFunc() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },
    close() {
      this.$refs.form.resetFields();
      this.dialogVisible = false
      this.$emit("close")
    },
  }
}
</script>

<style scoped>

.input2 {
  width: 220px;
}
</style>
