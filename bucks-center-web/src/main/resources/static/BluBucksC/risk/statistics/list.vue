<template>
  <div>
    <el-card class="box-card" shadow="none">
      <el-form ref="form" :model="form" :inline="true" size="mini" label-width="90px">


        <el-row>
          <el-col :span="6">
            <el-form-item label=" ">
              <el-button type="primary" @click="search()">查询</el-button>
              <el-button type="primary" @click="resetForm()">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-row>
      <el-col :span="24">
        <el-table :data="dataList" border stripe v-loading="dataListLoading" size="small">
          <el-table-column align="center" show-overflow-tooltip header-align="center" type="index"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="abnDays" label="日期" header-align="center"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="abnCount" label="异常数量"  header-align="center"></el-table-column>

          <el-table-column align="center" width="240" label="操作" header-align="center">
            <template slot-scope="scope">
              <el-button size="mini" type="primary" v-has="'182334e6016f79115229d154abe'" @click="detail(scope.row.abnDays)">明细</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination @size-change="sizeChangeHandle"
                       @current-change="currentChangeHandle"
                       :current-page="form.pageNo"
                       :page-sizes="[10, 20, 50, 100]"
                       :page-size="form.pageSize"
                       :total="form.totalPage"
                       layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>
      </el-col>
    </el-row>
    <el-row>
      <el-col>
        <detail ref="detail" @success="getList()"/>
      </el-col>
    </el-row>

  </div>
</template>
<script>
module.exports = {
  components: {
    "detail": httpVueLoader("/BluBucksC/risk/statistics/detail.vue"),
  },
  data: function () {
    return {
      form: {
        pageSize:10,
        pageNo:1,
        ruleType: "",
        activityId: "",
        channelCode: "",

      },
      dataListLoading: false,
      dataList: [],
    }
  },
  methods: {

    search() {
      this.form.pageNo = 1;
      this.getList();
    },
    getList() {
      this.dataListLoading = true
      win.OkHttp.post($.api($.apiModule.bluBucksCenter, '/mgt/pointsAbnData/page'), this.form).then(res => {
        if (res.msgCode == 0) {
          this.dataList = res.data.content;
          this.form.totalPage = Number(res.data.totalElements)
        }
      })
      this.dataListLoading = false
    },
    // 当前页
    currentChangeHandle(val) {
      this.form.pageNo = val
      this.getList()
    },
    // 每页数
    sizeChangeHandle(val) {
      this.form.pageSize = val
      this.form.pageNo = 1
      this.getList()
    },

    detail(val) {
      this.$refs.detail.init(val);
    },
    resetForm() {
      this.$refs["form"].resetFields();
      this.getList();
    },
  },
  mounted() {
    this.$nextTick(function () {
      this.getList();
    })
  }
}
</script>