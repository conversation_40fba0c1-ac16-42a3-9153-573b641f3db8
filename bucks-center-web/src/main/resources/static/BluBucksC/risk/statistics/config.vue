<template>
  <div>
    <el-dialog :title="title" center :visible.sync="dialogVisible" width="40%" :close-on-click-modal="false"
               @close="resetForm('form')">
      <el-form ref="form"  :model="form" :inline="true" size="mini" label-width="125px">
        <el-row>
          <el-col >
            <el-form-item label="活动名称" prop="name" >
              <el-input class="input1" v-model="form.name" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="来源渠道" prop="channelCode" >
              <el-select v-model="form.channelCode" disabled>
                <el-option
                    v-for="item in channel"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>

          </el-col>
        </el-row>

        <el-row>
          <el-col>
            <el-form-item label="国家/地区" prop="country" >
              <el-select v-model="form.countryId" placeholder="请选择" disabled >
                <el-option
                    v-for="item in countryList"
                    :key="item.geoNameId"
                    :label="item.countryName"
                    :value="item.geoNameId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col>
            <el-form-item label="积分发放规则" prop="earnRule" >
              <el-select v-model="form.earnRule" placeholder="请选择" disabled>
                <el-option
                    v-for="item in ruleDataList1"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col>
            <el-form-item label="积分消耗规则" prop="spendRule" >
              <el-select v-model="form.spendRule" placeholder="请选择" disabled>
                <el-option
                    v-for="item in ruleDataList2"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col >
            <el-form-item label="活动编码" prop="code" >
              <el-input class="input1" v-model="form.code" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" content="">
        <el-button @click="resetForm('form')">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
module.exports = {
  components: {},
  data: function () {

    return {
      ruleDataList1:[{code: '1', name: "签到"}, {code: '2', name: "货币兑换"},{code: '3', name: "光伏发电"},{code: '9', name: "通用"}],
      ruleDataList2 :[{code:'1',name:"券卡兑换"},{code: '2', name: "物品兑换"},{code: '3', name: "货币抵扣"},{code: '9', name: "通用"}],
      ruleDataList3 :[{code:'Y',name:"开"},{code: 'N', name: "关"}],
      channel:[],
      form:{

      },
      dialogVisible:false,
      title: "活动配置",
      countryList:[],

    }
  },
  methods: {
    visible(val) {
      win.OkHttp.get($.api($.apiModule.midpMasterData, "~/iso/country/v1/localized/all-countries")).then(res => {
        if (res.msgCode == 0) {
          this.countryList = res.data.countries;
        }
      })
      win.OkHttp.get($.api($.apiModule.bluBucksCenter, "/mgt/channel/list")).then(res => {
        if (res.msgCode == 0) {
          this.channel = res.data;
        }
      })

      this.dialogVisible = true;
      win.OkHttp.get($.api($.apiModule.bluBucksCenter, "/mgt/activity/get/") + val).then(res => {
        if (res.msgCode == 0) {
          this.form = res.data
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.dialogVisible = false;
    },
  }
}
</script>
<style>

</style>