<template>
  <div>
    <el-card class="box-card" shadow="none">
      <el-form :model="searchForm" ref="form" :inline="true" size="mini" label-width="110px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="来源渠道" prop="channelCode">
              <el-select v-model="searchForm.channelCode" placeholder="请选择" filterable clearable>
                <el-option
                    v-for="item in channel"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>


        <el-row type="flex">
          <el-col :span="10">
            <el-form-item label=" ">
              <el-button type="primary" @click="search()">查询</el-button>
              <el-button type="primary" @click="resetForm()">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>


      </el-form>

    </el-card>
    <el-row>
      <el-col :span="24">
        <el-table :data="dataList" border stripe  size="small">
          <el-table-column align="center" show-overflow-tooltip header-align="center" type="index"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="channelName" label="来源渠道名称" ></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="activityName" label="活动名称" ></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="countryId" label="国家/地区" :formatter="countryFormat"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="totalPoints" label="已发放积分总数" ></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="num" label="持有人数" ></el-table-column>



        </el-table>

        <el-pagination @size-change="sizeChangeHandle"
                       @current-change="currentChangeHandle"
                       :current-page="searchForm.pageNo"
                       :page-sizes="[10, 20, 50, 100]"
                       :page-size="searchForm.pageSize"
                       :total="searchForm.totalPage"
                       layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>


      </el-col>
    </el-row>


  </div>
</template>
<script>


module.exports = {
  components: {},
  data: function () {
    return {
      countryList:[],
      channel:[],
      dataList: [],
      searchForm: {
        pageNo: 1,
        pageSize: 10,
        totalPage: 0,
        channelCode: '',

      },
    }
  },
  methods: {

    countryFormat(row) {
      if ( row.countryId === null || row.countryId === '') return row.countryId;
      return this.countryList.filter(item => item.geoNameId === row.countryId)[0].countryName;
    },

    getList() {
      win.OkHttp.get($.api($.apiModule.midpMasterData, "~/iso/country/v1/localized/all-countries")).then(res => {
        if (res.msgCode == 0) {
          this.countryList = res.data.countries;
        }
      })

      win.OkHttp.get($.api($.apiModule.bluBucksCenter, "/mgt/channel/list")).then(res => {
        if (res.msgCode == 0) {
          this.channel = res.data;
        }
      })

      win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/pointsJournalGroup/pageList"), this.searchForm).then(res => {
        if (res.msgCode == 0) {
          this.dataList = res.data.content;
          this.searchForm.totalPage = Number(res.data.totalElements)
        }
      })
    },


    search() {
      this.searchForm.pageNo = 1;
      this.getList();
    },
    currentChangeHandle(val) {
      this.searchForm.pageNo = val
      this.getList()
    },
    // 每页数
    sizeChangeHandle(val) {
      this.searchForm.pageSize = val
      this.searchForm.pageNo = 1
      this.getList()
    },
    resetForm() {
      this.$refs.form.resetFields();
      this.getList();
    },

  },
  mounted() {
    this.$nextTick(function () {
      this.getList();
    })
  }
}
</script>
<style>


</style>