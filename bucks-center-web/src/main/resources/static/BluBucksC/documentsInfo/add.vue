<template>
  <div>
    <el-dialog :title="title" center :visible.sync="dialogVisible" width="80%" :close-on-click-modal="false"
               @close="resetForm('form')">
      <el-form ref="form" :rules="formRules" :model="form" :inline="true" size="mini">

        <el-row>
          <el-col :span="6">
            <el-form-item label="类型" prop="type" label-width="110px">
              <el-select v-model="form.type" placeholder="请选择" clearable filterable>
                <el-option
                    v-for="item in types"
                    :key="item.code"
                    :label="item.name+'/'+item.code"
                    :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="国家/地区" prop="countryId" label-width="110px">
              <el-select v-model="form.countryId" placeholder="请选择" clearable filterable @change="getActivity()">
                <el-option
                    v-for="item in countryList"
                    :key="item.geoNameId"
                    :label="item.countryName +'/'+ item.countryCode"
                    :value="item.geoNameId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="渠道" prop="channelCode" label-width="110px">
              <el-select v-model="form.channelCode" placeholder="请选择" clearable filterable @change="getActivity()">
                <el-option
                    v-for="item in channel"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>


          <el-col :span="6">
            <el-form-item label="活动" prop="activityId" label-width="110px">
              <el-select filterable clearable v-model="form.activityId" placeholder="请选择" @change="changeActivity()">
                <el-option
                    v-for="item in activity"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

        </el-row>

        <el-row>
        <el-col :span="6">
          <el-form-item label="语种" prop="localeCode" label-width="110px">
            <el-select filterable clearable v-model="form.localeCode" placeholder="请选择">
              <el-option
                  v-for="item in locales"
                  :key="item.localeCode"
                  :label="item.localeName"
                  :value="item.localeCode">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        </el-row>


        <el-row>
          <el-col :span="24">
            <el-form-item label="新闻内容" prop="content" label-width="110px">
              <mavon-editor ref="mavonEditor" v-model="form.content"
                            @save="saveMarkDown" @change="changeMarkDown"/>
            </el-form-item>
          </el-col>
        </el-row>


      </el-form>
      <div slot="footer" class="dialog-footer" content="">
        <el-button type="primary" @click="submit">新增</el-button>
        <el-button @click="resetForm('form')">取 消</el-button>
      </div>
    </el-dialog>


  </div>
</template>
<script>


module.exports = {
  components: {},
  data: function () {
    return {

      dialogVisible: false,
      countryList: [],
      activity: [],
      channel: [],
      types: [],
      locales:[],
      loading: null,
      title: '新增文案信息',
      formRules: {
        channelCode: [
          {required: true, message: '必须选择渠道', trigger: ["blur"]}
        ],
        countryId: [
          {required: true, message: '必须选择国家', trigger: ["blur"]}
        ],
        type: [
          {required: true, message: '必须选择类型', trigger: ["blur", 'change']},
        ],
        content: [
          {required: true, message: '必须输入内容', trigger: ["blur", 'change']},
        ],

        localeCode: [
          {required: true, message: '必须选择语种', trigger: ["blur", 'change']},
        ]
      },
      form: {
        type:null,
        localeCode:'',
        countryId: '',
        channelCode: '',
        activityId: '',
        activityVal: '',
        content: '',
        editFlag: false
      }
    }
  },
  methods: {
    loadingFunc() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },
    changeActivity() {
      this.activity.forEach(a => {
        if (a.id === this.form.activityId) {
          this.form.channelCode = a.channelCode;
          this.form.countryId = a.countryId;
        }
      })
    },
    getActivity() {
      this.form.activityId = ''
      win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/activity/getList"), this.form).then(res => {
        if (res.msgCode == 0) {
          this.activity = res.data;
        }
      })
    },

    submit() {

      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$confirm('是否确定新增?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.loadingFunc();
            win.OkHttp.post($.api($.apiModule.bluBucksCenter, '/mgt/DocumentsInfo/add'), this.form).then(res => {
              this.loading.close();
              if (res.msgCode == 0) {
                this.$message.success("新增成功");
                this.resetForm("form");
                this.$emit("success");
              }
            }).catch(e => {
              this.loading.close();
            })
          }).catch(() => {
            this.$message.info('已取消新增');
          });


        } else {
          return false;
        }
      })
    },
    saveMarkDown(markdown, html) {
      this.form.content = html;
    },
    changeMarkDown(markdown, html) {
      this.form.content = html;
    },
    resetForm() {
      this.form.localeCode = null
      this.form.activityId = ''
      this.form.activityVal = ''
      this.form.content = ''
      this.dialogVisible = false;
      this.$refs['form'].resetFields();
    },
    visible(types, countryList, channel,locales) {
      this.getActivity();
      this.locales=locales
      this.types = types
      this.countryList = countryList
      this.channel = channel
      this.dialogVisible = true
    },
    selectActivity() {
      let val = this.form.activityVal;
      let arr = val.split("-");
      this.form.activityId = arr[1];
    },
  }
}
</script>
<style>


</style>