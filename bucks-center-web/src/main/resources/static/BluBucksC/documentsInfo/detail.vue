<template>
  <div>
    <el-dialog :title="title" center :visible.sync="dialogVisible" width="80%" :close-on-click-modal="false"
               @close="resetForm('form')">
      <el-form ref="form"  :model="form" :inline="true" size="mini">

        <el-row>


          <el-col :span="6">
            <el-form-item label="类型" prop="type" label-width="110px">
              <el-select v-model="form.type" placeholder="" clearable filterable disabled>
                <el-option
                    v-for="item in types"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="国家/地区" prop="countryId" label-width="110px">
              <el-select v-model="form.countryId" placeholder="" clearable filterable @change="getActivity()" disabled>
                <el-option
                    v-for="item in countryList"
                    :key="item.geoNameId"
                    :label="item.countryName +'/'+ item.countryCode"
                    :value="item.geoNameId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="渠道" prop="channelCode" label-width="110px">
              <el-select v-model="form.channelCode" placeholder="" clearable filterable @change="getActivity()" disabled>
                <el-option
                    v-for="item in channel"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="活动" prop="activityId"  label-width="110px">
              <el-select filterable clearable v-model="form.activityId"   placeholder="" disabled>
                <el-option
                    v-for="item in activity"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

        </el-row>

        <el-row>
          <el-col :span="6">
            <el-form-item label="语种" prop="localeCode" label-width="110px">
              <el-select filterable clearable v-model="form.localeCode" placeholder="请选择" disabled>
                <el-option
                    v-for="item in locales"
                    :key="item.localeCode"
                    :label="item.localeName"
                    :value="item.localeCode">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="新闻内容" prop="content" label-width="110px">
              <mavon-editor ref="mavonEditor" v-model="form.content"
                            @save="saveMarkDown" @change="changeMarkDown"/>
            </el-form-item>
          </el-col>



        </el-row>






      </el-form>
      <div slot="footer" class="dialog-footer" content="">
        <el-button @click="resetForm('form')">确 定</el-button>
      </div>
    </el-dialog>


  </div>
</template>
<script>


module.exports = {
  components: {},
  data: function () {
    return {
      dialogVisible:false,
      countryList:[],
      activity:[],
      channel:[],
      types:[],
      loading: null,
      title:'详情文案信息',

      locales:[],
      form:{
        localeCode:null,
        countryId: null,
        channelCode:'',
        activityId:'',
        content:'',
        editFlag:true,
        type:'',
      }
    }
  },
  methods: {
    getActivity(){
      win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/activity/getList"), this.form).then(res => {
        if (res.msgCode == 0) {
          this.activity = res.data;
        }
      })
    },


    loadingFunc() {
      this.loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },

    saveMarkDown(markdown, html) {
      this.form.content = html;
    },
    changeMarkDown(markdown, html) {
      this.form.content = html;
    },
    resetForm(){
      this.form.activityId = ''
      this.form.content=''
      this.form.localeCode=null
      this.form.type=''
      this.dialogVisible = false;
      this.$refs['form'].resetFields();
    },
    visible(row,types,countryList,channel,locales){
      this.getActivity()
      this.locales=locales
      this.form.id=row.id
      this.form.localeCode=row.localeCode
      this.form.activityId=row.activityId
      this.form.channelCode=row.channelCode
      this.form.countryId=row.countryId
      this.form.content=row.content
      this.form.type=row.type
      this.dialogVisible=true
      this.types=types
      this.countryList=countryList
      this.channel=channel
    },

  }
}
</script>
<style>


</style>