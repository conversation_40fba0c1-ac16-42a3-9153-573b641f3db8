<template>
  <div>
    <el-card class="box-card" shadow="none">
      <el-form :model="form" ref="form" :inline="true" size="mini" label-width="110px">
        <el-row>
          <el-col :span="6">
            <el-form-item label="类型" prop="type" label-width="110px">
              <el-select v-model="form.type" placeholder="请选择" clearable filterable>
                <el-option
                    v-for="item in types"
                    :key="item.code"
                    :label="item.name+'/'+item.code"
                    :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="国家/地区" prop="countryId" label-width="110px">
              <el-select v-model="form.countryId" placeholder="请选择" clearable filterable @change="getActivity()">
                <el-option
                    v-for="item in countryList"
                    :key="item.geoNameId"
                    :label="item.countryName +'/'+ item.countryCode"
                    :value="item.geoNameId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="渠道" prop="channelCode" label-width="110px">
              <el-select v-model="form.channelCode" placeholder="请选择" clearable filterable @change="getActivity()">
                <el-option
                    v-for="item in channel"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>



          <el-col :span="6">
            <el-form-item label="活动" prop="activityId" label-width="110px">
              <el-select filterable clearable v-model="form.activityId" placeholder="请选择"  @change="changeActivity()">
                <el-option
                    v-for="item in activity"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

        </el-row>

        <el-row>
          <el-col :span="6">
            <el-form-item label="语种" prop="localeCode" label-width="110px">
              <el-select filterable clearable v-model="form.localeCode" placeholder="请选择" >
                <el-option
                    v-for="item in locales"
                    :key="item.localeCode"
                    :label="item.localeName"
                    :value="item.localeCode">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row type="flex">
          <el-col :span="10">
            <el-form-item label=" ">
              <el-button type="primary" @click="search()">查询</el-button>
              <el-button type="primary" @click="resetForm()">重置</el-button>
              <el-button type="primary" @click="add()" v-has="'182386eff04f79115229d154ad5'">新增</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>


    <el-row>
      <el-col :span="24">
        <el-table :data="dataList" border stripe size="small">
          <el-table-column align="center" show-overflow-tooltip header-align="center" type="index"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip  label="文案类型">
            <template slot-scope="scope">
              {{scope.row.typeName+"/"+scope.row.type}}
            </template>
          </el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="countryId" label="国家/地区"
                           :formatter="countryFormat"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip prop="channelCode" label="渠道名称"
                           :formatter="channelFormat"></el-table-column>



          <el-table-column align="center" show-overflow-tooltip prop="activityName" label="活动名称"></el-table-column>

          <el-table-column align="center" show-overflow-tooltip prop="localeCode" label="语种" :formatter="localeFormat"></el-table-column>
          <!--          <el-table-column align="center" show-overflow-tooltip prop="content" label="内容" ></el-table-column>-->


          <el-table-column align="center" label="操作" min-width="200px">
            <template slot-scope="scope">
              <el-button size="mini" type="primary" @click="detail( scope.row)">详情</el-button>
              <el-button size="mini" type="primary" @click="edit( scope.row)" v-has="'182425355dff79115229d154aee'">编辑
              </el-button>
              <el-button size="mini" type="danger" @click="remove( scope.row)" v-has="'1823873b2ccf79115229d154ad7'">
                删除
              </el-button>
            </template>
          </el-table-column>

        </el-table>

        <el-pagination @size-change="sizeChangeHandle"
                       @current-change="currentChangeHandle"
                       :current-page="form.pageNo"
                       :page-sizes="[10, 20, 50, 100]"
                       :page-size="form.pageSize"
                       :total="form.totalPage"
                       layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>


      </el-col>
    </el-row>


    <el-row>
      <el-col>
        <add ref="add" @success="getList()"/>
      </el-col>
    </el-row>
    <el-row>
      <el-col>
        <edit ref="edit" @success="getList()"/>
      </el-col>
    </el-row>

    <el-row>
      <el-col>
        <detail ref="detail" @success="getList()"/>
      </el-col>
    </el-row>

  </div>
</template>
<script>


module.exports = {
  components: {
    "add": httpVueLoader("/BluBucksC/documentsInfo/add.vue"),
    "edit": httpVueLoader("/BluBucksC/documentsInfo/edit.vue"),
    "detail": httpVueLoader("/BluBucksC/documentsInfo/detail.vue"),
  },
  data: function () {
    return {
      localeList:[{key:'zh-CN',value:"中文"},{key:"en-US",value:"英文"},{key:"de",value:"德文"},{key:"ja",value:"日文"}],
      channel: [],
      types: [],
      locales:[],
      form: {
        activityId: '',
        activityVal: '',
        pageNo: 1,
        pageSize: 10,
        totalPage: 0,
        activityCode: '',
        channelCode: '',
        countryId: '',
        type: '',
        localeCode:'',
      },
      dataList: [],
      countryList: [],
      activity: [],
      temVal: '',
    }
  },
  methods: {
    changeActivity() {
      this.activity.forEach(a => {
        if (a.id === this.form.activityId) {
          this.form.channelCode = a.channelCode;
          this.form.countryId = a.countryId;
        }
      })
    },
    detail(row) {
      this.$refs.detail.visible(row, this.types, this.countryList, this.channel,this.locales);
    },
    edit(row) {
      this.$refs.edit.visible(this.types, row, this.countryList, this.channel,this.locales);
    },
    add() {
      this.$refs.add.visible(this.types, this.countryList, this.channel,this.locales);
    },
    remove(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {

        win.OkHttp.get($.api($.apiModule.bluBucksCenter, '/mgt/DocumentsInfo/delete/') + row.id).then(res => {

          if (res.msgCode == 0) {
            this.$message.success("删除成功");
            this.form.pageNo = 1
            this.getList();
          }
        }).catch(e => {

        })
      }).catch(() => {
        this.$message.info('已取消删除');
      });


    },
    resetForm() {
      this.form.activityVal = "";
      this.$refs.form.resetFields();
      this.getList();
      this.getActivity();

    },
    typeFormat(row) {
      if (row.type === null || row.type === '') return row.type;
      let result = null;
      this.types.forEach(function (item) {
        if (item.code === row.type) {
          result = item.name;
        }
      })
      return result;
    },

    localeFormat(row) {
      if (row.localeCode === null || row.localeCode === '') return row.localeCode
      let result = null;
      this.locales.forEach(function (item) {
        if (item.localeCode === row.localeCode) {
          result = item.localeName;
        }
      })
      return result;
    },

    channelFormat(row) {
      if (row.channelCode === null || row.channelCode === '') return row.channelCode;
      let result = null;
      this.channel.forEach(function (item) {
        if (item.code === row.channelCode) {
          result = item.name;
        }
      })
      return result;
    },
    activityFormat(row) {
      if (row.activityCode === null || row.activityCode === '') return row.activityCode;
      let result = null;
      this.activity.forEach(function (item) {
        if (item.code === row.activityCode) {
          result = item.name;
        }
      })
      return result;
    },
    countryFormat(row) {
      if (row.countryId === null || row.countryId === '') return row.countryId;
      let result = null;
      this.countryList.forEach(function (item) {
        if (item.geoNameId === row.countryId) {
          result = item.countryName+"/"+item.countryCode;
        }
      })
      return result;
    },

    getList() {


      win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/DocumentsInfo/page"), this.form).then(res => {
        if (res.msgCode == 0) {
          this.dataList = res.data.content;
          this.form.totalPage = Number(res.data.totalElements)
        }
      })


    },

    search() {
      this.form.pageNo = 1;
      this.getList();
    },
    currentChangeHandle(val) {
      this.form.pageNo = val
      this.getList()
    },
    // 每页数
    sizeChangeHandle(val) {
      this.form.pageSize = val
      this.form.pageNo = 1
      this.getList()
    },
    getCountry() {
      win.OkHttp.get($.api($.apiModule.midpMasterData, "~/iso/country/v1/localized/all-countries")).then(res => {
        if (res.msgCode == 0) {
          this.countryList = res.data.countries;
        }
      })
    },
    getChannel() {
      win.OkHttp.get($.api($.apiModule.bluBucksCenter, "/mgt/channel/existsList")).then(res => {
        if (res.msgCode == 0) {
          this.channel = res.data;
        }
      })
    },

    getActivity() {
      this.form.activityId = ''
      win.OkHttp.post($.api($.apiModule.bluBucksCenter, "/mgt/activity/getList"), this.form).then(res => {
        if (res.msgCode == 0) {
          this.activity = res.data;
        }
      })
    },

    getTypes() {

      win.OkHttp.get($.api($.apiModule.bluBucksCenter, "/mgt/DocumentsType/list")).then(res => {
        if (res.msgCode == 0) {
          this.types = res.data;
        }
      })

      win.OkHttp.get($.api($.apiModule.bluBucksCenter, "/mgt/DocumentsLocale/list")).then(res => {
        if (res.msgCode == 0) {
          this.locales = res.data;
        }
      })


    }


  },
  mounted() {
    this.getList();
    this.getCountry();
    this.getChannel();
    this.getActivity();
    this.getTypes();
  }
}
</script>
<style>


</style>