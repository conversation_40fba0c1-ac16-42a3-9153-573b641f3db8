package net.poweroak.saas.bucksc.web;

import net.poweroak.PropertyPrefix;
import net.poweroak.framework.core.web.AbstractBaseController;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * 【Bluetti SaaS】积分中心Web前端服务启动类
 * <AUTHOR>
 * @Mailto <EMAIL> || <EMAIL>
 * @Create 2022-05-04 17:13:14
 */
@Controller
@SpringBootApplication
public class BluBucksCenterWebApplication extends AbstractBaseController {
    public static void main(String[] args) {
        SpringApplication.run(BluBucksCenterWebApplication.class, args);
    }

    @GetMapping("/")
    public String index() {
        return redirect(PropertyPrefix.DEBUGGED_INDEX_HTML);
    }
}
