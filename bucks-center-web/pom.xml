<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>net.poweroak.cloud-saas.bucks-center</groupId>
		<artifactId>bucks-center</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>bucks-center-web</artifactId>

    <dependencies>
        <dependency>
            <groupId>net.poweroak.framework</groupId>
            <artifactId>framework-ui</artifactId>
        </dependency>

        <dependency>
            <groupId>net.poweroak.framework</groupId>
            <artifactId>framework-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <!-- 不需要将此应用JAR包发布到Maven本地私库 -->
                    <skip>true</skip>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>