<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>net.poweroak.cloud-saas.bucks-center</groupId>
		<artifactId>bucks-center</artifactId>
		<version>1.0.0-SNAPSHOT</version>
	</parent>

	<artifactId>bucks-center-provider</artifactId>

	<dependencies>

		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper-spring-boot-starter</artifactId>
			<version>1.2.5</version>
		</dependency>

		<!-- Alibaba Easy Excel -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>easyexcel</artifactId>
		</dependency>


	    <!-- PaaS服务：主数据中心模块Client -->
		<dependency>
			<groupId>net.poweroak.cloud-midp.master-data</groupId>
			<artifactId>master-data-api</artifactId>
			<version>${midp-master-data.version}</version>
		</dependency>
		<!-- PaaS服务：鉴权中心模块Client -->
		<dependency>
			<groupId>net.poweroak.cloud-midp.auth-center</groupId>
			<artifactId>auth-center-api</artifactId>
			<version>${midp-auth-center.version}</version>
		</dependency>

		<!-- Saas服务：积分模块Client -->
		<!-- 当前项目API模块 -->
		<dependency>
			<groupId>net.poweroak.cloud-saas.bucks-center</groupId>
			<artifactId>bucks-center-api</artifactId>
			<version>${blu-bucks-center.version}</version>
		</dependency>

		<!-- Saas服务：用户中心模块Client -->
		<dependency>
			<groupId>net.poweroak.cloud-saas.user-center</groupId>
			<artifactId>user-center-api</artifactId>
			<version>${blu-user-center.version}</version>
		</dependency>

		<!-- 调度中心 -->
		<dependency>
			<groupId>net.poweroak.cloud-midp.schedule-center</groupId>
			<artifactId>schedule-center-api</artifactId>
			<version>${midp-schedule-center.version}</version>
		</dependency>

		<!-- Bluetti开发框架 -->
		<dependency>
			<groupId>net.poweroak.framework</groupId>
			<artifactId>framework-cloud-member</artifactId>
		</dependency>

		<!-- 调度中心 -->
		<dependency>
			<groupId>net.poweroak.cloud-midp.schedule-center</groupId>
			<artifactId>schedule-center-api</artifactId>
			<version>${midp-schedule-center.version}</version>
		</dependency>


		<dependency>
			<groupId>net.poweroak.framework</groupId>
			<artifactId>framework-cache</artifactId>
		</dependency>

		<dependency>
			<groupId>org.redisson</groupId>
			<artifactId>redisson</artifactId>
		</dependency>

		<!-- 开发框架：MyBatis框架 -->
		<dependency>
			<groupId>net.poweroak.framework</groupId>
			<artifactId>framework-data-mybatis</artifactId>
		</dependency>
		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
		</dependency>
		<dependency>
			<groupId>net.poweroak.cloud-midp.notify-center</groupId>
			<artifactId>notify-center-api</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<!-- MQTT -->
        <dependency>
            <groupId>org.springframework.integration</groupId>
            <artifactId>spring-integration-stream</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.integration</groupId>
            <artifactId>spring-integration-mqtt</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>

	</dependencies>

	<build>
		<finalName>${parent.artifactId}-${project.version}</finalName>

		<plugins>
			<!-- 禁止发布到私库中 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>

            <!-- 打包成可执行JAR包 -->
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>

		<resources>
			<resource>
				<directory>src/main/resources</directory>
			</resource>
			<resource>
				<directory>src/env/${env}</directory>
			</resource>
		</resources>
	</build>

	<profiles>
		<!-- 开发环境 -->
		<profile>
			<id>dev</id>
			<properties>
				<env>dev</env>
			</properties>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
		</profile>

		<!-- 测试环境 -->
		<profile>
			<id>test</id>
			<properties>
				<env>test</env>
			</properties>
		</profile>

		<!-- UAT环境 -->
		<profile>
			<id>uat</id>
			<properties>
				<env>uat</env>
			</properties>
		</profile>

		<!-- 沙箱环境 -->
		<profile>
			<id>sandbox</id>
			<properties>
				<env>sandbox</env>
			</properties>
		</profile>

		<!-- 生产环境 -->
		<profile>
			<id>prod</id>
			<properties>
				<env>prod</env>
			</properties>
		</profile>
	</profiles>
</project>
