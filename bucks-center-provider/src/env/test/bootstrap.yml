server:
  port: 20112
info:
  component: "Bluetti SaaS Bucks Center Service"

spring:
  application:
    name: blu-bucks-center${spring.application.developer:}
    abbr: BluBucksC
  messages:
    use-code-as-default-message: true
  http:
    encoding:
      force: true
  cloud:
    nacos:
      server-addr: test.nacos.poweroak.ltd:8848
      config:
        # 加载多个远程配置文件的写法
        # name: other-config-file,${spring.application.name}
        enabled: true
        file-extension: yml
        shared-configs[0]:
          data-id: common.yml
          refresh: true
      # 服务注册与发现
      discovery:
        enabled: true

# mybatis配置
mybatis:
  mapperLocations: classpath*:mapper/**/*.xml
  configuration:
    # 禁用字段名驼峰配置
    map-underscore-to-camel-case: false
    default-fetch-size: 100
    default-statement-timeout: 30

poweroak:
  framework:
    core:
      i18n-enabled: true
      i18n-mode: 1
    web:
      cros-enabled: true
      swagger-enabled: true