spring:
  profiles:
    active: dev

  datasource:
    username: root
    password: 123456
    url: ****************************************************************************************************************************************************************************************************
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
  redis:
    port: 6379
    password: 12345678
    host: dev.redis.poweroak.ltd
    database: 0
  rabbitmq:
    host: ${spring.profiles.active}.mq.poweroak.ltd
    port: 5672
    username: admin
    password: admin123456
    #开启发送端确认
    publisher-confirms: true
    #开启发送端消息抵达队列的确认
    publisher-returns: true
    #只要抵达队列，以异步发送优先回调我们这个returnConfirm
    template:
      mandatory: true
    #手动ack消息
    listener:
      simple:
        acknowledge-mode: manual
        concurrency: 2
        max-concurrency: 10

poweroak:
  framework:
    distributed-lock:
      threads: 10
      netty-threads: 10
      redis-single:
        password: ${spring.redis.password}
        address: "redis://${spring.redis.host}:${spring.redis.port}"
        database: 2
        dnsMonitoringInterval: -1
  ###同步第三方用户基础信息
  sync:
    user:
      bluettipower: https://api.bluettipower.com/userapi #南山域名
      growave: https://api.growave.io/v2 #第三方域名
      userName: poweroak #用户名称
      pwd: tqqnJbxcs7 #密码

xxl:
  job:
    enabled: true
    ### 执行器通讯TOKEN [选填]：非空时启用；
    accessToken:
    executor:
      port: 2112

logging:
  level:
    ROOT: INFO
    net.poweroak: DEBUG
    net.poweroak.framework.data: INFO