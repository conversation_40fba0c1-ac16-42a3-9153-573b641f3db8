package net.poweroak;


import net.poweroak.framework.api.data.perms.norm.RouteMenu;
import net.poweroak.framework.pedestal.annotate.RouteRoot;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 【Bluetti SaaS】积分中心微服务启动类
 * <AUTHOR>
 * @Mailto <EMAIL> || <EMAIL>
 * @Create 2022-05-04 17:18:43
 */
@RefreshScope
@EnableFeignClients
@EnableScheduling
@EnableDiscoveryClient
@SpringBootApplication
@RouteRoot
@EnableAsync
@RouteMenu(label = "积分中心", module = "blu-bucks-center")
public class BluBucksCenterApplication {
    public static void main(String[] args) {
        SpringApplication.run(BluBucksCenterApplication.class, args);
    }
}
