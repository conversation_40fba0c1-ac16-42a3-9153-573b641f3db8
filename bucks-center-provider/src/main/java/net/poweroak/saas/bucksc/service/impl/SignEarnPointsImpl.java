package net.poweroak.saas.bucksc.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.exception.ApplicationRuntimeException;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.saas.bucksc.BucksCenterError;
import net.poweroak.saas.bucksc.common.ActivityEarnCode;
import net.poweroak.saas.bucksc.enums.WeekCode;
import net.poweroak.saas.bucksc.model.PO.PointsEarnRule;
import net.poweroak.saas.bucksc.model.TO.PointsEranCommTO;
import net.poweroak.saas.bucksc.model.VO.PointsEarnCommonVO;
import net.poweroak.saas.bucksc.service.AbstractEarnPointsService;
import net.poweroak.saas.bucksc.service.factory.PointsEarnFactory;
import net.poweroak.saas.bucksc.utils.LocalDateUtil;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2022/7/4 14:20
 */
@Slf4j
@Service
public class SignEarnPointsImpl extends AbstractEarnPointsService {

    @Override
    public PointsEarnCommonVO calcPoints(PointsEranCommTO request) {
        DateTime dateTime = LocalDateUtil.getLocalDateTime(request.getCountryId(), new Date());
        log.debug("calcPoints dayOfWeek,{}", dateTime.dayOfWeek());
        int dayOfWeek = WeekCode.getCodeByOriCode(dateTime.dayOfWeek());
        Map<String, String> map = pointsEarnRuleDao.getSignDate(request.getCountryId(), request.getChannelCode(), request.getUid(), dateTime);
        if (map.containsKey(dayOfWeek + "")) {
            log.error("已签到,checkEarnRuleCondition,Sign,参数：{}", JSONUtil.toJsonStr(request));
            throw new ApplicationRuntimeException(BucksCenterError.EARN_SIGN_HAS_EXISTS);
        }


        PointsEarnRule pointsEarnRuleToday = getPointsEarnRule(request.getActivityId(), request.getChannelCode(), dayOfWeek);
        if (pointsEarnRuleToday == null) {
            log.error("不满足发放门槛,pointsEarnRule == null,Sign,参数：{}", JSONUtil.toJsonStr(request));
            throw new ApplicationRuntimeException(BucksCenterError.EARN_RULE_CONDITION_NOT_COMPLETE);
        }


        PointsEarnCommonVO pointsEarnCommonVO = new PointsEarnCommonVO();
        pointsEarnCommonVO.setPointsEarnRuleId(pointsEarnRuleToday.getId());

        String sourceId = pointsEarnRuleToday.getId() + "_" + dateTime.dayOfYear() + "_" + dayOfWeek;
        if (pointsJournalDao.hasExtra(sourceId, request.getUid())) {
            //持续天数，额外奖励
            int days = this.getContinueDays(map, dayOfWeek);
            PointsEarnRule pointsEarnRuleExtra = getPointsEarnRule(request.getActivityId(), request.getChannelCode(), days);
            if (pointsEarnRuleExtra != null) {
                TreeMap<String, Integer> extraMap = new TreeMap<>();
                extraMap.put(sourceId, pointsEarnRuleExtra.getExtraReward().intValue());
                pointsEarnCommonVO.setExtraRewordMap(extraMap);
            }
        }
        //向上取整
        BigDecimal points = pointsEarnRuleToday.getPoints().setScale(0, RoundingMode.CEILING);
        pointsEarnCommonVO.setPoints(points.intValue());
        return pointsEarnCommonVO;
    }

    private PointsEarnRule getPointsEarnRule(String activityId, String channelCode, int days) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsEarnRule::getActivityId).is(activityId));
        query.addCriterion(Criterion.where(PointsEarnRule::getChannelCode).is(channelCode));
        query.addCriterion(Criterion.where(PointsEarnRule::getRuleType).is(ActivityEarnCode.SIGN.getRuleType()));
        query.addCriterion(Criterion.where(PointsEarnRule::getEarnLimit).is(days));
        query.addCriterion(Criterion.where(PointsEarnRule::getEnable).is(1));
        return pointsEarnRuleDao.findOne(query);
    }

    /**
     * 获得签到天数
     *
     * @param map
     * @param today
     * @return
     */
    private int getContinueDays(Map<String, String> map, int today) {
        int days = 0;
        for (int i = today - 1; i > 0; i--) {
            String signDay = map.get(i + "");
            if (Strings.isEmpty(signDay)) {
                break;
            }
            days += 1;
        }
        return days + 1;
    }

    @Override
    public void afterPropertiesSet() {
        PointsEarnFactory.register(ActivityEarnCode.SIGN.getModel(), this);
    }
}
