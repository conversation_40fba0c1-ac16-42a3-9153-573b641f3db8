package net.poweroak.saas.bucksc.enums;

/**
 * Created by zx on 2022/6/1 17:51 时间单位类型
 */

public enum TimeUnitType {

    MINUTE(1, "分钟"),
    HOUR(2, "小时"),
    DAY(3, "天");

    private int code;
    private String name;

    TimeUnitType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
