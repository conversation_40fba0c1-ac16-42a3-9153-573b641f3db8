package net.poweroak.saas.bucksc.model.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/25 11:06
 * @description
 */
@Data
public class PointsJournalCumulativeVO {

    @ApiModelProperty(name="用户id")
    private String uid;
    @ApiModelProperty(name="累计增加")
    private Integer points;
    @ApiModelProperty(name="用户余额")
    private Integer balance;
}
