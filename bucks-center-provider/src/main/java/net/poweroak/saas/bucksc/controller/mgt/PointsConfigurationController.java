package net.poweroak.saas.bucksc.controller.mgt;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.BluBucksCenterApplication;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.perms.norm.RouteMenu;
import net.poweroak.framework.pedestal.annotate.RequirePermission;
import net.poweroak.framework.pedestal.annotate.RouteAction;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.dao.PointsConfigurationDao;
import net.poweroak.saas.bucksc.model.PO.PointsConfiguration;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/7/28 9:35
 * @description
 */

@Slf4j
@ApiIgnore
@RestController
@RequestMapping("/" + RouteEndpoint.MGT + "/pointsConfiguration")
@Api(tags = "积分配置")
@RouteMenu(module = "pointsConfigurationModule", label = "积分配置管理", parent = BluBucksCenterApplication.class,order = 7)

public class PointsConfigurationController {
    @Resource
    PointsConfigurationDao pointsConfigurationDao;



    /**
     * 查询积分配置
     * @param
     * @return
     */
    @GetMapping("/get")
    @ApiOperation(value = "查询积分配置")
    @RouteAction(id = "1824275bffef79115229d154aef", action = "get", label = "查询积分配置", view = "/BluBucksC/pointsConfiguration/list.vue", linkToModule = true)
    @RequirePermission
    public UnifyResponse<PointsConfiguration> getConfiguration() {
        return this.pointsConfigurationDao.getConfiguration();
    }


    @PostMapping("/update")
    @RouteAction(action = "update",id = "18242863ff0f79115229d154af4", label = "更新积分配置")
    @RequirePermission
    public UnifyResponse<Boolean> updateConfiguration(@RequestBody PointsConfiguration request) {
        return new UnifyResponse(pointsConfigurationDao.updateConfiguration(request));
    }


}
