package net.poweroak.saas.bucksc.controller.mgt;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.data.perms.norm.RouteMenu;
import net.poweroak.framework.pedestal.annotate.RequirePermission;
import net.poweroak.framework.pedestal.annotate.RouteAction;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.dao.PointsJournalDao;
import net.poweroak.saas.bucksc.model.TO.JournalDayExportTO;
import net.poweroak.saas.bucksc.model.TO.journalDayTO;
import net.poweroak.saas.bucksc.model.VO.journalDayVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/17 9:50
 * @description
 */

//@ApiIgnore
@Slf4j
@RestController
@RequestMapping("/" + RouteEndpoint.MGT + "/pointsJournalByDays")
@Api(tags = "分日期统计积分")
@RouteMenu(module = "journalByDaysMGT", label = "分日期统计积分", parent = PointsCountController.class,order = 997)
public class PointsJournalByDaysController {
    @Resource
    PointsJournalDao pointsJournalDao;



    /**
     * 分渠道统计积分
     * @param request
     * @return
     */
    @PostMapping("/pageList")
    @ApiOperation(value = "分日期统计积分")
    @RouteAction(id = "180cfedaea9f791157c8280bac0", action = "page", label = "分日期统计积分", view = "/BluBucksC/journalByDays/list.vue", linkToModule = true)
    @RequirePermission
    public UnifyResponse<Pagination<journalDayVO>> pageList(@RequestBody journalDayTO request) {
        return this.pointsJournalDao.pageByDay(request);
    }


    /**
     * 定时清空
     */
    @PostMapping("/empty")
    public void empty() {
        pointsJournalDao.empty();
    }



//    public void time()  throws Exception{
//        Calendar date = Calendar.getInstance();
//        //设置时间为 xx-xx-xx 00:00:00
//        date.set(date.get(Calendar.YEAR), date.get(Calendar.MONTH), date.get(Calendar.DATE), 0, 0, 0);
//        //⼀天的毫秒数
//        long daySpan = 24 * 60 * 60 * 1000;
//        //得到定时器实例
//        Timer t = new Timer();
//        //使⽤匿名内⽅式进⾏⽅法覆盖
//        t.schedule(new TimerTask() {
//            @Override
//            public void run() {
//                empty();
//            }
//        }, date.getTime(), daySpan); //daySpan是⼀天的毫秒数，也是执⾏间隔
//    }


    /**
     * 分日期统计积分导出
     * @param request
     * @param response
     */
    @GetMapping("/export")
    @ApiOperation(value = "分日期统计积分导出")
    public void export(JournalDayExportTO request, HttpServletResponse response) {
        pointsJournalDao.export(request, response);
    }


}
