package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.page.PageInfo;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/7 9:15
 */
@Data
@ApiModel("保存发放规则")
public class PointsEarnRuleSaveTO {

    private String id;

    /**
     * 渠道编码
     */
    @ApiModelProperty(value = "渠道编码")
    private String channelCode;

    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID")
    private String activityId;

    /**
     * 规则类型
     */
    @ApiModelProperty(value = "规则类型")
    private Integer ruleType;

    /**
     * 积分值
     */
    @ApiModelProperty(value = "积分值")
    private BigDecimal points;

    /**
     * 额外积分奖励
     */
    @ApiModelProperty(value = "额外积分奖励")
    private BigDecimal extraReward;

    /**
     * 会员等级
     */
    @ApiModelProperty(value = "会员等级")
    private Integer memberLevel;

    /**
     * 发放门槛
     */
    @ApiModelProperty(value = "发放门槛")
    private BigDecimal earnLimit;

    /**
     * 发放门槛
     */
    @ApiModelProperty(value = "单用户每日获取次数")
    private Integer dailyNum;


    @ApiModelProperty(value = "发放类型 1=一次性 2=无限次")
    private Integer earnType;

    @ApiModelProperty(value = "状态 0=禁用 1=启用")
    private Integer enable;

    @ApiModelProperty(value = "规则编码")
    private String ruleCode;

    private String title;

    private List<PointsEarnRuleSaveTO> items;
}
