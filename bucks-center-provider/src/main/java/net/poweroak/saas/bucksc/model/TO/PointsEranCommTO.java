package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.saas.bucksc.enums.EarnRuleType;
import net.poweroak.saas.bucksc.model.PO.PointsBalance;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/6/11 14:54
 */
@Data
public class PointsEranCommTO {

    public PointsEranCommTO() {
        this.isSend = true;
    }

    @ApiModelProperty(value = "用户ID")
    private String uid;

    @ApiModelProperty(value = "渠道标识 例如APP,PC")
    private String channelCode;

    @ApiModelProperty(value = "活动编码 签到，货物兑换，光伏发电可不传,通用必须传")
    private String activityCode;

    @ApiModelProperty(value = "活动ID")
    private String activityId;

//    @ApiModelProperty(value = "国家编码")
//    private String countryCode;

    @ApiModelProperty(value="国家Id")
    private Long countryId;

    private BigDecimal earnLimit;

    private Integer balance = 0;

    /*******光伏start*********/
    @ApiModelProperty(value = "设备sn号")
    private String sn;

    @ApiModelProperty(value = "数据读取开始时间")
    private String beginTime;

    @ApiModelProperty(value = "数据结束时间")
    private String endTime;

    @ApiModelProperty(value = "发电度数")
    private BigDecimal degree;
    /*******光伏end*********/


    /*******货物兑换（订单）start*********/
    @ApiModelProperty(value = "订单Id")
    private String orderId;

    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "累计消费金额")
    private BigDecimal totalAmount;
    /*******货物兑换（订单）end*********/


    /*******通用start*********/
    @ApiModelProperty(value = "发放积分")
    private Integer points;
    /*******通用 end*********/


    private Integer subType;

    private EarnRuleType earnRuleType;

    private PointsBalance pointsBalance;

    private boolean isSend;

    @ApiModelProperty(value = "订单编码")
    private String orderNo;

    @ApiModelProperty(value = "费金额增减：默认true加、false减")
    private Boolean amountSymbol;

    @ApiModelProperty(value = "积分说明")
    private String desc;
}
