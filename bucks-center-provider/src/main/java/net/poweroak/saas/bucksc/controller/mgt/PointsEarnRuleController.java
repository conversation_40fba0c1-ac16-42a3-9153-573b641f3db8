package net.poweroak.saas.bucksc.controller.mgt;

import io.swagger.annotations.Api;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.dao.PointsEarnRuleDao;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @date 2022/5/7 9:15
 */
@ApiIgnore
@RestController
@RequestMapping("/" + RouteEndpoint.MGT + "/pointsEarnRule")
@Api(tags = "发放规则管理")
public class PointsEarnRuleController {

    @Resource
    private PointsEarnRuleDao pointsEarnRuleDao;

}
