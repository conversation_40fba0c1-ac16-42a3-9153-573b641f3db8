package net.poweroak.saas.bucksc.dao;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.exception.ApplicationRuntimeException;
import net.poweroak.framework.data.mybatis.GenericRepository;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.framework.enums.OrderBy;
import net.poweroak.framework.user.UserInfoForServer;
import net.poweroak.framework.util.StringUtils;
import net.poweroak.saas.bucksc.BucksCenterError;
import net.poweroak.saas.bucksc.mapper.PointsExchangeRegistrationMapper;
import net.poweroak.saas.bucksc.model.PO.PointsExchangeRegistration;
import net.poweroak.saas.bucksc.model.TO.PointsExchangeByOrderTO;
import net.poweroak.saas.bucksc.model.TO.PointsExchangeRegistrationIssueExcelTO;
import net.poweroak.saas.bucksc.model.TO.PointsExchangeRegistrationPageTO;
import net.poweroak.saas.bucksc.model.VO.PointsExchangeRegistrationExportVO;
import net.poweroak.saas.bucksc.model.VO.PointsExchangeRegistrationVO;
import net.poweroak.saas.uc.client.UserClient;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2023/11/6
 */
@Slf4j
@Repository
public class PointsExchangeRegistrationDao extends GenericRepository<PointsExchangeRegistration, PointsExchangeRegistrationMapper, String> {

    @Resource
    private UserClient userClient;

    public PointsExchangeRegistrationDao(PointsExchangeRegistrationMapper genericMapper) {
        super(genericMapper);
    }

    /**
     * 检查订单号是否重复
     *
     * @param params
     * @return
     */
    public boolean checkOrderNumber(PointsExchangeByOrderTO params) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsExchangeRegistration::getOrderNumber).is(params.getOrderNumber()));
        if (this.exists(query)) {
            query.addCriterion(Criterion.where(PointsExchangeRegistration::getEmail).is(params.getEmail()))
                    .addCriterion(Criterion.where(PointsExchangeRegistration::getPlatform).is(params.getPlatform()));
            if (this.exists(query)) {
                throw new ApplicationRuntimeException(BucksCenterError.DUPLICATE_SUBMISSION);
            }
            return true;
        } else {
            return false;
        }
    }

    /**
     * 根据uid查询账户信息
     *
     * @param uid
     * @return
     */
    public UserInfoForServer findAccountByUid(String uid) {
        UnifyResponse<List<UserInfoForServer>> unifyResponse = userClient.list(CollectionUtil.toList(uid));
        if (unifyResponse.isOK() && CollectionUtil.isNotEmpty(unifyResponse.getData())) {
            return unifyResponse.getData().get(0);
        }
        log.error("根据UID无法获取用户信息,UID:{}", uid);
        throw new ApplicationRuntimeException(BucksCenterError.BALANCE_IS_NOT_EXISTS);
    }

    public Pagination<PointsExchangeRegistrationVO> page(PointsExchangeRegistrationPageTO params) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsExchangeRegistration::getEnable).is(1))
                .addCriterion(Criterion.where(PointsExchangeRegistration::getEmail).regex(params.getEmail()), () -> StringUtils.isNotEmpty(params.getEmail()))
                .addCriterion(Criterion.where(PointsExchangeRegistration::getPlatform).is(params.getPlatform()), () -> StringUtils.isNotEmpty(params.getPlatform()))
                .addCriterion(Criterion.where(PointsExchangeRegistration::getModel).regex(params.getModel()), () -> StringUtils.isNotEmpty(params.getModel()))
                .addCriterion(Criterion.where(PointsExchangeRegistration::getOrderNumber).regex(params.getOrderNumber()), () -> StringUtils.isNotEmpty(params.getOrderNumber()))
                .addCriterion(Criterion.where(PointsExchangeRegistration::getCountryCode).is(params.getCountryCode()), () -> StringUtils.isNotEmpty(params.getCountryCode()))
                .addCriterion(Criterion.where(PointsExchangeRegistration::getStatus).is(params.getStatus()), () -> null != params.getStatus())
                .orderBy(PointsExchangeRegistration::getCreTime, OrderBy.DESC)
                .orderBy(PointsExchangeRegistration::getId, OrderBy.DESC)
                .join(PointsExchangeRegistrationVO.class);
        return this.findByJoin(query, params);
    }

    public List<PointsExchangeRegistrationExportVO> list(PointsExchangeRegistrationPageTO params) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsExchangeRegistration::getEnable).is(1))
                .addCriterion(Criterion.where(PointsExchangeRegistration::getEmail).is(params.getEmail()), () -> StringUtils.isNotEmpty(params.getEmail()))
                .addCriterion(Criterion.where(PointsExchangeRegistration::getPlatform).is(params.getPlatform()), () -> StringUtils.isNotEmpty(params.getPlatform()))
                .addCriterion(Criterion.where(PointsExchangeRegistration::getModel).is(params.getModel()), () -> StringUtils.isNotEmpty(params.getModel()))
                .addCriterion(Criterion.where(PointsExchangeRegistration::getOrderNumber).is(params.getOrderNumber()), () -> StringUtils.isNotEmpty(params.getOrderNumber()))
                .addCriterion(Criterion.where(PointsExchangeRegistration::getCountryCode).is(params.getCountryCode()), () -> StringUtils.isNotEmpty(params.getCountryCode()))
                .addCriterion(Criterion.where(PointsExchangeRegistration::getStatus).is(params.getStatus()), () -> null != params.getStatus())
                .orderBy(PointsExchangeRegistration::getCreTime, OrderBy.DESC)
                .join(PointsExchangeRegistrationExportVO.class);
        return this.findByJoin(query);
    }

    public PointsExchangeRegistration byId(String id) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsExchangeRegistration::getEnable).is(1))
                .addCriterion(Criterion.where(PointsExchangeRegistration::getId).is(id));
        return this.findOne(query);
    }

    public PointsExchangeRegistration getRegistration(PointsExchangeRegistrationIssueExcelTO params) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsExchangeRegistration::getEnable).is(1))
                .addCriterion(Criterion.where(PointsExchangeRegistration::getEmail).is(params.getEmail()))
                .addCriterion(Criterion.where(PointsExchangeRegistration::getPlatform).is(params.getPlatform()))
                .addCriterion(Criterion.where(PointsExchangeRegistration::getOrderNumber).is(params.getOrderNumber()));
        return this.findOne(query);
    }
}
