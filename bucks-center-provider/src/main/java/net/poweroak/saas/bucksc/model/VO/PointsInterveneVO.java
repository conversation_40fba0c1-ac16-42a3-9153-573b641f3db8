package net.poweroak.saas.bucksc.model.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.sqltag.TableField;
import net.poweroak.saas.bucksc.model.PO.Activity;
import net.poweroak.saas.bucksc.model.PO.Channel;
import net.poweroak.saas.bucksc.model.PO.PointsIntervene;
import net.poweroak.saas.bucksc.model.PO.PointsJournal;

import java.util.Date;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/30 10:00
 * @description
 */
@Data
@ApiModel("人工后台干预")
public class PointsInterveneVO {

    @ApiModelProperty("录入时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(table = PointsIntervene.class, field = "time")
    private Date time;
    @ApiModelProperty("录入人id")
    @TableField(table = PointsIntervene.class, field = "uid")
    private String uid;
    @ApiModelProperty("录入人名字")
    private String uname;
    @ApiModelProperty("流水id")
    @TableField(table = PointsJournal.class, field = "id")
    private String journalId;
    @ApiModelProperty("活动名称")
    @TableField(table = PointsJournal.class, field = "activityId")
    private String activityId;
    @ApiModelProperty("国家代码")
    @TableField(table = PointsJournal.class, field = "countryId")
    private Long countryId;
    @ApiModelProperty("类型")
    @TableField(table = PointsJournal.class, field = "genType")
    private Integer genType;
    @ApiModelProperty("分数")
    @TableField(table = PointsJournal.class, field = "points")
    private Integer points;
    @ApiModelProperty("渠道名称")
    @TableField(table = PointsJournal.class, field = "channelCode")
    private String channelCode;

    @ApiModelProperty("流水人名字")
    private String journalUname;
}
