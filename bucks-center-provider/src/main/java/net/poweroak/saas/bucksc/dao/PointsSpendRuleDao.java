package net.poweroak.saas.bucksc.dao;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.data.page.PageInfo;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.exception.ApplicationRuntimeException;
import net.poweroak.framework.data.mybatis.GenericRepository;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.enums.OrderBy;
import net.poweroak.iso.CountryCode;
import net.poweroak.iso.vo.country.CountryRich;
import net.poweroak.saas.bucksc.BucksCenterError;
import net.poweroak.saas.bucksc.mapper.PointsSpendRuleMapper;
import net.poweroak.saas.bucksc.model.PO.Activity;
import net.poweroak.saas.bucksc.model.PO.PointsSpendRule;
import net.poweroak.saas.bucksc.model.TO.SpendRulePageTO;
import net.poweroak.saas.bucksc.model.VO.RegionCountryVO;
import net.poweroak.saas.bucksc.model.VO.SpendRulePageVO;
import net.poweroak.saas.bucksc.model.VO.SpendRuleVO;
import net.poweroak.saas.bucksc.service.IBaseService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by zx on 2022/5/9 16:35
 */

@Repository
@Slf4j
public class PointsSpendRuleDao extends GenericRepository<PointsSpendRule, PointsSpendRuleMapper, String> {

    public PointsSpendRuleDao(PointsSpendRuleMapper genericMapper) {
        super(genericMapper);
    }

    @Resource
    private ActivityDao activityDao;

    @Resource
    private IBaseService baseService;

    public Pagination<SpendRulePageVO> page(SpendRulePageTO pageTO) {
        final MybatisQuery query = MybatisQuery.where()
                .addCriterion(Criterion.where(PointsSpendRule::getRuleType).is(pageTO.getRuleType()), () -> pageTO.getRuleType() != null)
                .addCriterion(Criterion.where(PointsSpendRule::getActivityId).is(pageTO.getActivityId()), () -> StringUtils.isNotEmpty(pageTO.getActivityId()))
                .addCriterion(Criterion.where(PointsSpendRule::getChannelCode).is(pageTO.getChannelCode()), () -> StringUtils.isNotEmpty(pageTO.getChannelCode()))
                .addCriterion(Criterion.where(PointsSpendRule::getCountryId).is(pageTO.getCountryId()), () -> pageTO.getCountryId() != null)
                .orderBy(PointsSpendRule::getCreTime, OrderBy.DESC);

        Pagination<PointsSpendRule> pagination = this.find(query, pageTO.getPageNumber(), pageTO.getPageSize());

        List<SpendRulePageVO> list = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(pagination.getContent())) {

            //活动相关信息
            List<String> activityIds = pagination.getContent().stream().map(PointsSpendRule::getActivityId).distinct().collect(Collectors.toList());
            final List<Activity> activities = activityDao.find(MybatisQuery.where().addCriterion(Criterion.where(Activity::getId).in(activityIds)).addCriterion(Criterion.where(Activity::getEnable).is(true)));
            Map<String,String > activityMap  = CollectionUtils.isNotEmpty(activities) ? activities.stream().collect(Collectors.toMap(Activity::getId,Activity::getName)) : Maps.newHashMap();

            pagination.getContent().forEach(rule -> {
                SpendRulePageVO spendRulePageVO = new SpendRulePageVO();
                BeanUtils.copyProperties(rule,spendRulePageVO);
                spendRulePageVO.setId(rule.getId());
                spendRulePageVO.setEnable(rule.getEnable());
                spendRulePageVO.setActivityName(activityMap.get(rule.getActivityId()));
                try {
                    CountryRich countryRich = CountryCode.getByGeoId(rule.getCountryId());
                    spendRulePageVO.setNameZh(countryRich.getCountryCode()+"/"+countryRich.getCountryName());
                }catch (Exception e){
                    e.printStackTrace();
                }
                list.add(spendRulePageVO);
            });
        }

        return new Pagination(list, new PageInfo(pageTO.getPageNumber() - 1, pageTO.getPageSize()), pagination.getTotalElements());
    }



    public void update(SpendRuleVO spendRuleVO) {

        final Optional<PointsSpendRule> optional1 = this.findById(spendRuleVO.getId());

        if (optional1.isPresent()) {
            PointsSpendRule pointsSpendRule = optional1.get();
            pointsSpendRule.setPoints(spendRuleVO.getPoints());
            pointsSpendRule.setSpendLimit(spendRuleVO.getSpendLimit());
            pointsSpendRule.setExchangeRatio(spendRuleVO.getExchangeRatio());
            pointsSpendRule.setRuleType(spendRuleVO.getRuleType());
            pointsSpendRule.modify();
            this.updateById(pointsSpendRule);
        }else{
            final Optional<Activity> optional = activityDao.findById(spendRuleVO.getActivityId());

            if(!optional.isPresent()) {
                throw new ApplicationRuntimeException(BucksCenterError.ACTIVITY_IS_NOT_EXISTS);
            }

            //校验规则
            final PointsSpendRule spendRule = this.findOne(MybatisQuery.where()
                    .addCriterion(Criterion.where(PointsSpendRule::getActivityId).is(spendRuleVO.getActivityId()))
                    .addCriterion(Criterion.where(PointsSpendRule::getChannelCode).is(optional.get().getChannelCode()))
                    .addCriterion(Criterion.where(PointsSpendRule::getCountryId).is(optional.get().getCountryId()))
                    .addCriterion(Criterion.where(PointsSpendRule::getRuleType).is(spendRuleVO.getRuleType()))
                    .addCriterion(Criterion.where(PointsSpendRule::getEnable).is(true)));

            if (spendRule != null) throw new ApplicationRuntimeException(BucksCenterError.RULE_EXISTED);

            PointsSpendRule pointsSpendRule = new PointsSpendRule();
            BeanUtils.copyProperties(spendRuleVO, pointsSpendRule);
            pointsSpendRule.setChannelCode(optional.get().getChannelCode());
            pointsSpendRule.setCountryId(optional.get().getCountryId());
            pointsSpendRule.create();
            this.insert(pointsSpendRule);


        }

    }

    public void enableOrDisable(String ruleId) {

        final Optional<PointsSpendRule> optional = this.findById(ruleId);

        if (optional.isPresent()) {
            PointsSpendRule pointsSpendRule = optional.get();
            pointsSpendRule.setEnabled(pointsSpendRule.getEnable() == 1 ? false : true);
            pointsSpendRule.modify();
            this.updateById(pointsSpendRule);
        }

    }

}
