package net.poweroak.saas.bucksc.model.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/5/7 9:15
 */
@Data
@ApiModel("货币兑换列表")
public class PointsEarnRulePageVO {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 渠道编码
     */
    @ApiModelProperty(value = "渠道编码")
    private String channelCode;

    /**
     * 国家
     */
    @ApiModelProperty(value = "国家")
    private Long country;

    /**
     * 渠道编码
     */
    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID")
    private String activityId;

    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动名称")
    private String activityName;

    /**
     * 规则类型
     */
    @ApiModelProperty(value = "规则类型")
    private Integer ruleType;

    /**
     * 规则类型
     */
    @ApiModelProperty(value = "规则名称")
    private String ruleTypeName;

    /**
     * 积分值
     */
    @ApiModelProperty(value = "积分值")
    private BigDecimal points;

    /**
     * 额外积分奖励
     */
    @ApiModelProperty(value = "额外积分奖励")
    private BigDecimal extraReward;

    /**
     * 会员等级
     */
    @ApiModelProperty(value = "会员等级")
    private Integer memberLevel;

    /**
     * 发放门槛
     */
    @ApiModelProperty(value = "发放门槛")
    private BigDecimal earnLimit;

    /**
     * 发放门槛
     */
    @ApiModelProperty(value = "发放类型 1=一次性 2=无限次")
    private Integer  earnType;

    @ApiModelProperty(value = "单用户每日获取次数")
    private Integer dailyNum;

    @ApiModelProperty(value = "状态")
    private Integer enable;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "规则编码")
    private String ruleCode;
}
