package net.poweroak.saas.bucksc.model.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.saas.bucksc.model.PO.Activity;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @date 2022/5/7 9:15
 */
@Data
@ApiModel("积分汇总查询接口")
public class PointsBalanceTotalVO  {

    @ApiModelProperty(value = "发放积分")
    private Integer earnTotal;

    @ApiModelProperty(value = "消耗")
    private Integer spendTotal;

    @ApiModelProperty(value = "余额")
    private Integer balanceTotal;
}
