package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.poweroak.saas.bucksc.request.PointsEarnBaseRequest;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 通用
 *
 * <AUTHOR>
 * @date 2022/5/31 14:09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("发放积分-后台")
public class PointsEarnSendTO extends PointsEarnBaseRequest {

    @ApiModelProperty(value = "积分")
    @NotNull(message = "积分")
    private Integer points;
}
