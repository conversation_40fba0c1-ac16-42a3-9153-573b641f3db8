package net.poweroak.saas.bucksc.model.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Created by zx on 2022/5/31 17:33
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PointsAbnDataVO {

    @ApiModelProperty(value = "渠道标识")
    private String channelCode;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date creTime;

    @ApiModelProperty(value = "活动ID")
    private String activityId;

    @ApiModelProperty(value = "积分值")
    private Integer points;

    @ApiModelProperty(value = "国家编码")
    private Long countryId;

    @ApiModelProperty(value = "适用的积分消耗规则：1-积分发放；2-积分回退")
    private Integer abnType;

    @ApiModelProperty(value = "异常记录时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date abnTime;

    @ApiModelProperty(value = "用户账户")
    private String userAccount;

    @ApiModelProperty(value = "用户ID")
    private String uid;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "积分流水ID")
    private String journalId;

    @ApiModelProperty(value = "订单ID")
    private String orderId;

    @ApiModelProperty(value = "异常说明")
    private String desc;

    @ApiModelProperty(value = "是否拉黑")
    private Boolean isPullBlack;

}
