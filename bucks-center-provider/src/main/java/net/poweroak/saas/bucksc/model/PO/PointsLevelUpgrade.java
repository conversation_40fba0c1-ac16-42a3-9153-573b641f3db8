package net.poweroak.saas.bucksc.model.PO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.DomainEntity;
import net.poweroak.framework.sqltag.Table;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2024/6/7
 */
@Data
@ApiModel("积分等级升级记录")
@Table(name = "points_level_upgrade")
public class PointsLevelUpgrade extends DomainEntity<String> {

    @ApiModelProperty(value = "用户uid")
    private String uid;

    @ApiModelProperty(value = "国家id")
    private Long countryId;

    @ApiModelProperty(value = "所绑定的等级id")
    private String pointsLevelId;

    @ApiModelProperty(value = "升级到的等级规则id")
    private String levelRuleId;

    @ApiModelProperty(value = "等级排序下标")
    private Integer levelRuleIndex;

    @ApiModelProperty(value = "赠送的积分数")
    private Integer giftPoints;

    @ApiModelProperty(value = "是否已告知用户")
    private Integer hasInform;

}
