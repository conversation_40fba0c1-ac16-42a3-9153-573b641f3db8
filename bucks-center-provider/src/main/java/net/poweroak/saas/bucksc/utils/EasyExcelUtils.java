package net.poweroak.saas.bucksc.utils;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.support.ExcelTypeEnum;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

@Slf4j
public class EasyExcelUtils {

    /**
     * excel Write Response
     */
    public static void excelWriteResponse(String fileName, HttpServletResponse response) throws UnsupportedEncodingException {
        //这里使用swagger 会导致各种问题，请直接用浏览器或者用postman
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyExcel没有关系
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", StrUtil.concat(Boolean.TRUE, "attachment;filename=", fileName, ExcelTypeEnum.XLSX.getValue()));
    }
}
