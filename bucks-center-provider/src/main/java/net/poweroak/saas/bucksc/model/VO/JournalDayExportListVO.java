package net.poweroak.saas.bucksc.model.VO;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023/12/10 9:29
 */


@ApiModel(value = "分日期统计积分导出的导出字段")
@Data
public class JournalDayExportListVO {

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "日期",index = 0)
    @ColumnWidth(20)
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    @ApiModelProperty(value = "日期")
    private String days;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "渠道名称",index = 1)
    @ColumnWidth(20)
    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "活动名称",index = 2)
    @ColumnWidth(20)
    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "发放积分",index = 3)
    @ColumnWidth(20)
    @ApiModelProperty(value = "发放积分")
    private BigDecimal type1Points;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "发放人数",index = 4)
    @ColumnWidth(20)
    @ApiModelProperty(value = "发放积分人数")
    private Integer type1Num;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "消耗积分",index = 5)
    @ColumnWidth(20)
    @ApiModelProperty(value = "消耗积分")
    private BigDecimal type2Points;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "消耗人数",index = 6)
    @ColumnWidth(20)
    @ApiModelProperty(value = "消耗积分人数")
    private Integer type2Num;

    @ExcelIgnore
    @ApiModelProperty(value = "回退积分")
    private BigDecimal type3Points;

    @ExcelIgnore
    @ApiModelProperty(value = "回退积分人数")
    private Integer type3Num;

    @ExcelIgnore
    @ApiModelProperty(value = "消耗率")
    private BigDecimal ratio;

}
