package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2023/11/7
 */
@Data
public class PointsExchangeRegistrationIssueTO {
    private String id;

    @ApiModelProperty(value = "发放积分（正整数）")
    @NotNull
    @Min(value = 1, message = "发放积分必须为正整数")
    private Integer points;

    @ApiModelProperty(value = "发放积分账户国家编码")
    @NotNull
    private Long countryId;

    @ApiModelProperty(value = "备注")
    private String comment;
}
