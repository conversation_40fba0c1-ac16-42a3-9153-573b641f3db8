package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.page.PageInfo;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/9 15:59
 * @description
 */
@Data
public class ActivitySearchTO extends PageInfo {
    @ApiModelProperty(value = "国家")
    private Long countryId;
    @ApiModelProperty(value = "活动名称")
    private String name;
    @ApiModelProperty(value = "渠道编码")
    private String channelCode;
    @ApiModelProperty(value = "活动类型")
    private String  activeType;
    @ApiModelProperty(value = "活动枚举code")
    private String activeCode;

}
