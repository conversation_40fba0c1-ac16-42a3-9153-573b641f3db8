package net.poweroak.saas.bucksc.enums;

/**
 * <AUTHOR>
 * @date 2022/5/10 8:46
 */
public enum EarnTimesType {
    ONE(1, "一次性"),
    INFINITE(2, "无限次");


    private Integer code;
    private String name;

    EarnTimesType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(Integer code){
        EarnTimesType[] earnRuleTypes =  EarnTimesType.values();
        for (EarnTimesType earnRuleType: earnRuleTypes){
            if(earnRuleType.code.equals(code)){
                return earnRuleType.name;
            }
        }
        return "";
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
