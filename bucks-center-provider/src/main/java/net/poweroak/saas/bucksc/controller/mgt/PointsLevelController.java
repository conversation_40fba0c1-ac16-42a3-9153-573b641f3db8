package net.poweroak.saas.bucksc.controller.mgt;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.BluBucksCenterApplication;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.data.perms.norm.RouteMenu;
import net.poweroak.framework.pedestal.annotate.RouteAction;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.model.TO.PointsLevelPageTO;
import net.poweroak.saas.bucksc.model.TO.PointsLevelTO;
import net.poweroak.saas.bucksc.model.VO.PointsLevelDetailVO;
import net.poweroak.saas.bucksc.model.VO.PointsLevelVO;
import net.poweroak.saas.bucksc.service.IPointsLevelService;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2024/4/29
 */
@Slf4j
@RequestMapping("/" + RouteEndpoint.MGT + "/pointsLevel")
@Api(tags = "积分等级管理")
@RestController
@RouteMenu(module = "pointsLevelModule", label = "积分等级管理", parent = BluBucksCenterApplication.class, order = 8)
@RequiredArgsConstructor
public class PointsLevelController {

    private final IPointsLevelService levelService;

    @PostMapping("/page")
    @RouteAction(id = "18f27cabd96f791153a32a140c1", action = "page", label = "积分等级管理", view = "/BluBucksC/level/page.vue", linkToModule = true)
    @ApiOperation(value = "积分等级管理分页", httpMethod = "POST")
    public UnifyResponse<Pagination<PointsLevelVO>> page(@RequestBody PointsLevelPageTO params) {
        return new UnifyResponse<>(levelService.page(params));
    }

    @PostMapping("/add")
    @ApiOperation(value = "积分等级新增", httpMethod = "POST")
    public UnifyResponse<Void> add(@RequestBody @Valid PointsLevelTO params) {
        levelService.add(params);
        return new UnifyResponse<>();
    }

    @GetMapping("/detail")
    @ApiOperation(value = "积分等级详情", httpMethod = "GET")
    public UnifyResponse<PointsLevelDetailVO> detail(@RequestParam("id") String id) {
        return new UnifyResponse<>(levelService.detail(id));
    }

    @PutMapping("/edit")
    @ApiOperation(value = "积分等级编辑", httpMethod = "PUT")
    public UnifyResponse<Void> edit(@RequestBody @Valid PointsLevelTO params) {
        levelService.edit(params);
        return new UnifyResponse<>();
    }

    @DeleteMapping("/delete")
    @ApiOperation(value = "积分等级删除", httpMethod = "DELETE")
    public UnifyResponse<Void> delete(@RequestParam("id") String id) {
        levelService.delete(id);
        return new UnifyResponse<>();
    }
}
