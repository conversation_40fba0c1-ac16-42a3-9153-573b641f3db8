package net.poweroak.saas.bucksc.model.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.sqltag.TableField;
import net.poweroak.saas.bucksc.model.PO.PointsBalance;

import java.util.Date;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023/12/7 9:32
 */

@ApiModel(value = "积分余额表导出的vo")
@Data
public class PointsExportVO {

    @TableField(table = PointsBalance.class, field = "uid")
    @ApiModelProperty(value = "用户ID")
    private String uid;

    @TableField(table = PointsBalance.class, field = "creTime")
    @ApiModelProperty(value="注册时间")
    private Date creTime;

    @TableField(table = PointsBalance.class, field = "countryId")
    @ApiModelProperty(value="国家/地区id")
    private Long countryId;

//    @ApiModelProperty(value="国家/地区")
//    private String countryName;

    @TableField(table = PointsBalance.class, field = "earn")
    @ApiModelProperty(value = "累积发放")
    private Integer earn;

    @TableField(table = PointsBalance.class, field = "spend")
    @ApiModelProperty(value = "累积消耗")
    private Integer spend;

    @TableField(table = PointsBalance.class, field = "balance")
    @ApiModelProperty(value = "余额")
    private Integer balance;

}
