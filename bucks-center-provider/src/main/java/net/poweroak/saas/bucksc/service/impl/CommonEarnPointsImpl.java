package net.poweroak.saas.bucksc.service.impl;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.exception.ApplicationRuntimeException;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.enums.OrderBy;
import net.poweroak.saas.bucksc.BucksCenterError;
import net.poweroak.saas.bucksc.common.ActivityEarnCode;
import net.poweroak.saas.bucksc.model.PO.PointsEarnRule;
import net.poweroak.saas.bucksc.model.TO.PointsEranCommTO;
import net.poweroak.saas.bucksc.model.VO.PointsEarnCommonVO;
import net.poweroak.saas.bucksc.service.AbstractEarnPointsService;
import net.poweroak.saas.bucksc.service.factory.PointsEarnFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/4 14:20
 */
@Slf4j
@Service
public class CommonEarnPointsImpl extends AbstractEarnPointsService {

    @Override
    public PointsEarnCommonVO calcPoints(PointsEranCommTO request) {
        MybatisQuery query = MybatisQuery.where().addCriterion(Criterion.where(PointsEarnRule::getActivityId).is(request.getActivityId()))
                .addCriterion(Criterion.where(PointsEarnRule::getChannelCode).is(request.getChannelCode()))
                .addCriterion(Criterion.where(PointsEarnRule::getRuleType).is(ActivityEarnCode.COMMON.getRuleType()))
                .addCriterion(Criterion.where(PointsEarnRule::getEnable).is(1));

        if (request.getEarnLimit() == null) {
            request.setEarnLimit(BigDecimal.ZERO);
        }
//        query.addCriterion(Criterion.where(PointsEarnRule::getEarnLimit).lte(request.getEarnLimit()));
        query.orderBy(PointsEarnRule::getEarnLimit, OrderBy.DESC);
        List<PointsEarnRule> pointsEarnRuleList = pointsEarnRuleDao.find(query);

        PointsEarnCommonVO pointsEarnCommonVO = new PointsEarnCommonVO();
        pointsEarnCommonVO.setRuleExist(false);
        if (pointsEarnRuleList.size() > 0) {
            //有规则的时候，要判断是否符合规则
            PointsEarnRule pointsEarnRule = null;
            for (PointsEarnRule item : pointsEarnRuleList) {
                if (item.getEarnLimit().compareTo(request.getEarnLimit()) <= 0) {
                    pointsEarnRule = item;
                    break;
                }
            }
            if (pointsEarnRule == null) {
                log.error("不满足发放门槛,pointsEarnRule==null,Common,参数：{}", JSONUtil.toJsonStr(request));
                throw new ApplicationRuntimeException(BucksCenterError.EARN_RULE_CONDITION_NOT_COMPLETE);
            }

            if (!pointsEarnRuleDao.checkEarnRuleCondition(request.getUid(), request.getCountryId(), pointsEarnRule)) {
                log.error("发放次数不足,checkEarnRuleCondition,Common,参数：{}", JSONUtil.toJsonStr(request));
                throw new ApplicationRuntimeException(BucksCenterError.EARN_RULE_OVER_LIMIT);
            }
            pointsEarnCommonVO.setPointsEarnRuleId(pointsEarnRule.getId());
            pointsEarnCommonVO.setPoints(pointsEarnRule.getPoints().intValue());
            //匹配上规则
            pointsEarnCommonVO.setRuleExist(true);
        }
        //积分,传了按传的参数发放积分，没传用配置默认的发放
        if (request.getPoints() > 0) {
            pointsEarnCommonVO.setPoints(request.getPoints());
        }
        pointsEarnCommonVO.setRatio(BigDecimal.ZERO);
        return pointsEarnCommonVO;
    }

    @Override
    public void afterPropertiesSet() {
        PointsEarnFactory.register(ActivityEarnCode.COMMON.getModel(), this);
    }

}
