package net.poweroak.saas.bucksc.controller.mgt;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


import lombok.extern.slf4j.Slf4j;
import net.poweroak.BluBucksCenterApplication;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.data.perms.norm.RouteMenu;
import net.poweroak.framework.pedestal.annotate.RequirePermission;
import net.poweroak.framework.pedestal.annotate.RouteAction;
import net.poweroak.saas.bucksc.common.ActivityEarnCode;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.dao.ActivityDao;
import net.poweroak.saas.bucksc.dao.PointsEarnRuleDao;
import net.poweroak.saas.bucksc.dao.PointsSpendRuleDao;
import net.poweroak.saas.bucksc.model.PO.Activity;
import net.poweroak.saas.bucksc.model.TO.*;
import net.poweroak.saas.bucksc.model.VO.*;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;


/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/9 14:42
 * @description
 */

@Slf4j
@ApiIgnore
@RequestMapping("/" + RouteEndpoint.MGT + "/activity")
@Api(tags = "积分活动")
@RestController
@RouteMenu(module = "activityModule", label = "积分活动", parent = BluBucksCenterApplication.class,order = 2)
public class ActivityController {
    @Resource
    ActivityDao activityDao;

    @Resource
    private PointsEarnRuleDao pointsEarnRuleDao;

    @Resource
    private PointsSpendRuleDao pointsSpendRuleDao;
    /**
     * 分页查询积分活动
     * @param request
     * @return
     */
    @PostMapping("/pageList")
    @ApiOperation(value = "分页查询积分活动")
    @RouteAction(id = "180acb762cdf791157c8280ba7f", action = "page", label = "积分活动查询", view = "/BluBucksC/activity/list.vue", linkToModule = true)
    public UnifyResponse<Pagination<ActivityVO>> pageList(@RequestBody ActivitySearchTO request) {
        return this.activityDao.pageList(request);
    }


    /**
     * 删除积分活动
     * @param id
     * @return
     */
    @GetMapping("/delete/{id}")
    @ApiOperation(value = "删除积分活动")
    @RouteAction(action = "del",id = "182399d7887f79115229d154ade", label = "删除积分活动")
    @RequirePermission
    public UnifyResponse<Boolean> delete(@PathVariable("id") String id) {
        return this.activityDao.deleteRequest(id);
    }


    /**
     * 更新积分活动
     * @param request
     * @return
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新积分活动")
    @RouteAction(action = "update",id = "182052a0a8ef79115229d154a5f", label = "更新积分活动")
    @RequirePermission
    public UnifyResponse<Boolean> update(@RequestBody ActivityUpdateTO request) {
        return this.activityDao.updateActivity(request);
    }


    /**
     * 根据id获取单个活动
     * @param id
     * @return
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "积分活动单个查询")
    public UnifyResponse<ActivityVO> get(@PathVariable("id") String id) {
        return new UnifyResponse<>(activityDao.getOne(id));
    }


    /**
     * 不分页查询积分活动
     * @return
     */
    @GetMapping("/existsList")
    @ApiOperation(value = "不分页查询积分活动")
    public UnifyResponse<List<Activity>> existsList() {
        return this.activityDao.existsList();
    }


    /**
     * 不分页查询积分活动
     * @return
     */
    @GetMapping("/list")
    @ApiOperation(value = "不分页查询积分活动")
    public UnifyResponse<List<Activity>> list() {
        return this.activityDao.page();
    }


    @GetMapping("/commonList")
    @ApiOperation(value = "前台查询积分活动")
    public UnifyResponse<Pagination<ActivityCommonVO>> commonList() {
        return this.activityDao.common();
    }

    @GetMapping("/commonSpendList")
    @ApiOperation(value = "前台查询积分活动")
    public UnifyResponse<Pagination<ActivityCommonVO>> commonSpendList() {
        return this.activityDao.commonSpend();
    }



    /**
     * 新增积分活动
     * @param request
     * @return
     * @throws Exception
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增积分活动")
    @RouteAction(action = "add",id = "1826cd80c45f79115229d154bae", label = "新增积分活动")
    @RequirePermission
    public UnifyResponse<Boolean> add(@RequestBody ActivityAddTO request) throws Exception {
        return this.activityDao.add(request);

    }

    @GetMapping("/listByCountryCode/{countryId}")
    @ApiOperation(value = "根据国家code查询列表")
    public UnifyResponse<List<Activity>> listByCountryCode(@PathVariable("countryId") Long countryId){
        return this.activityDao.listByCountryCode(countryId);
    }


    /**
     * 根据国家渠道查询列表
     * @return
     */
    @PostMapping("/getList")
    @ApiOperation(value = "根据国家渠道查询列表")
    public UnifyResponse<List<Activity>> list(@RequestBody ActivityGetTO request) {
        return this.activityDao.getList(request);
    }



    /**************************发放规则 start*******************************/

    /**
     * 发放规则
     * @param pointsEarnRuleListTO
     * @return
     */
    @PostMapping("/getEarnRuleList")
    @RouteAction(id = "180a763a177f791157c8280ba22", action = "getEarnRuleList", label = "积分活动发放规则列表")
    @RequirePermission
    public UnifyResponse<List<PointsEarnRulePageVO>> getEarnRuleList(@RequestBody PointsEarnRuleListTO pointsEarnRuleListTO) {
        return new UnifyResponse<>(pointsEarnRuleDao.getListByActivityId(pointsEarnRuleListTO));
    }


    /**
     * 删除发放规则
     *
     * @param
     * @return
     */
    @PostMapping("/deleteEarn/{id}")
    @RouteAction(id = "18204fc1e89f79115229d154a57", action = "deleteEarn", label = "删除积分活动发放规则")
    @RequirePermission
    public UnifyResponse<Void> deleteEarn(@PathVariable("id") String id) {
        pointsEarnRuleDao.delete(id);
        return new UnifyResponse<>();
    }

    /**
     * 保存发放规则
     *
     * @param
     * @return
     */
    @PostMapping("/addEarn")
    @RouteAction(id = "18204fbe641f79115229d154a56", action = "addEarn", label = "新增积分活动发放规则")
    @RequirePermission
    public UnifyResponse<Boolean> addEarn(@RequestBody PointsEarnRuleSaveTO pointsEarnRuleSaveTO) {
        if (pointsEarnRuleSaveTO.getItems() != null) {
            return new UnifyResponse<>(pointsEarnRuleDao.saveList(pointsEarnRuleSaveTO.getItems(), pointsEarnRuleSaveTO.getRuleType()));
        }
        return new UnifyResponse<>(pointsEarnRuleDao.save(pointsEarnRuleSaveTO));
    }

    /**
     * 保存发放规则
     *
     * @param
     * @return
     */
    @PostMapping("/updateEarn")
    @RouteAction(id = "182050468dbf79115229d154a5d", action = "updateEarn", label = "编辑积分活动发放规则")
    @RequirePermission
    public UnifyResponse<Boolean> updateEarn(@RequestBody PointsEarnRuleSaveTO pointsEarnRuleSaveTO) {
        if (pointsEarnRuleSaveTO.getItems() != null) {
            return new UnifyResponse<>(pointsEarnRuleDao.saveList(pointsEarnRuleSaveTO.getItems(), pointsEarnRuleSaveTO.getRuleType()));
        }
        return new UnifyResponse<>(pointsEarnRuleDao.save(pointsEarnRuleSaveTO));
    }

    @GetMapping("/earnRuleList")
    public UnifyResponse<List<RuleTypeVO>> ruleList() {
        ActivityEarnCode[] activityEarnCodes = ActivityEarnCode.values();
        List<RuleTypeVO> list = new ArrayList<>();
        for (ActivityEarnCode activityEarnCode : activityEarnCodes) {
            RuleTypeVO ruleTypeVO = new RuleTypeVO();
            ruleTypeVO.setCode(activityEarnCode.getCode());
            ruleTypeVO.setName(activityEarnCode.getName());
            ruleTypeVO.setRuleType(activityEarnCode.getRuleType() + "");
            list.add(ruleTypeVO);
        }
        return new UnifyResponse<>(list);
    }

    /************************发放规则 end*******************************/

    /************************消耗规则 start***********************************/
    @PostMapping("/pageSpend")
    @RouteAction(action = "pageSpend",id = "180a7f0507ff791157c8280ba5d", label = "积分活动消耗规则列表")
    @RequirePermission
    public UnifyResponse<Pagination> pageSpend(@RequestBody SpendRulePageTO pageTO) {
        return new UnifyResponse(pointsSpendRuleDao.page(pageTO));
    }
    /************************消耗规则 end***********************************/






    @PostMapping("/updateSpend")
    @RouteAction(action = "updateSpend",id = "18242eb0d0cf79115229d154af9", label = "保存积分活动消耗规则")
    @RequirePermission
    public UnifyResponse<Void> update(@RequestBody @Valid SpendRuleVO spendRuleVO) {
        pointsSpendRuleDao.update(spendRuleVO);
        return new UnifyResponse();
    }


    @ApiOperation(value = "启用、禁用")
    @GetMapping("/enableOrDisable/{ruleId}")
    @RouteAction(action = "enableOrDisable",id = "18242ec912ef79115229d154afa", label = "启用或禁用积分活动消耗规则")
    @RequirePermission
    public UnifyResponse<Void> enableOrDisable(@PathVariable("ruleId") String  ruleId) {
        pointsSpendRuleDao.enableOrDisable(ruleId);
        return new UnifyResponse();
    }

}
