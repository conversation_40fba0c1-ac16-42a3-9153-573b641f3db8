package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2023/11/6
 */
@Data
public class PointsExchangeByOrderTO {
    @ApiModelProperty(value = "用户提交邮箱")
    @NotBlank
    private String email;

    @ApiModelProperty(value = "购买平台(编号)")
    @NotBlank
    private String platform;

    @ApiModelProperty(value = "其他平台")
    private String otherPlatform;

    @ApiModelProperty(value = "订单号")
    @NotBlank
    private String orderNumber;

    @ApiModelProperty(value = "购买产品型号")
    @NotBlank
    private String model;

    @ApiModelProperty(value = "订单金额")
    @NotNull
    @Digits(integer = 14, fraction = 2, message = "金额格式不正确")
    @DecimalMin(value = "0.00", message = "金额不能为负数")
    private BigDecimal amount;

    @ApiModelProperty(value = "币种")
    @NotBlank
    private String currency;
}
