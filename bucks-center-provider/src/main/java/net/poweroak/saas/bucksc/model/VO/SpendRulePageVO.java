package net.poweroak.saas.bucksc.model.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by zx on 2022/5/11 14:17
 */
@Data
public class SpendRulePageVO {

    private String id;

    @ApiModelProperty(value = "渠道标识")
    private String channelCode;

    @ApiModelProperty(value = "活动ID")
    private String activityId;

    @ApiModelProperty(value = "1-券卡兑换；2-物品兑换；3-货币抵扣；4-抽奖；9-通用")
    private Integer ruleType;

    @ApiModelProperty(value = "积分值")
    private Integer points;

    @ApiModelProperty(value = "国家编码")
    private Long countryId;

    @ApiModelProperty(value = "国家名称")
    private String nameZh;

    @ApiModelProperty(value = "消耗门槛（设置只有达到或超过该门槛值时，才可以使用该规则）")
    private Integer spendLimit;

    @ApiModelProperty(value = "兑换比例")
    private BigDecimal exchangeRatio;

    @ApiModelProperty(value = "会员等级")
    private Integer memberLevel;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty("订单创建时间")
    @JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
    private Date creTime;

    @ApiModelProperty("状态")
    private Integer enable;

}
