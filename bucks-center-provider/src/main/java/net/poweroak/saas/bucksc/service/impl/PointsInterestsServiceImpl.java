package net.poweroak.saas.bucksc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.iso.CountryCode;
import net.poweroak.iso.vo.country.CountryRich;
import net.poweroak.saas.bucksc.dao.PointsInterestsDao;
import net.poweroak.saas.bucksc.model.PO.PointsInterests;
import net.poweroak.saas.bucksc.model.TO.PointsInterestsPageTO;
import net.poweroak.saas.bucksc.model.TO.PointsInterestsTO;
import net.poweroak.saas.bucksc.model.VO.PointsInterestsVO;
import net.poweroak.saas.bucksc.service.IPointsInterestsService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2024/5/7
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PointsInterestsServiceImpl implements IPointsInterestsService {
    private final PointsInterestsDao interestsDao;

    @Override
    public void add(PointsInterestsTO params) {
        params.setId(null);
        PointsInterests interests = BeanUtil.copyProperties(params, PointsInterests.class);
        interests.setEnable(1);
        interests.create();
        interestsDao.insert(interests);
    }

    @Override
    public Pagination<PointsInterestsVO> page(PointsInterestsPageTO params) {
        Pagination<PointsInterestsVO> page = interestsDao.page(params);
        page.forEach(interestsVO -> {
            CountryRich countryRich = CountryCode.getByGeoId(interestsVO.getCountryId());
            interestsVO.setCountryName(countryRich != null ? CountryCode.getByAlpha2Code(countryRich.getCountryCode()).getNameZh() : String.valueOf(interestsVO.getCountryId()));
        });
        return page;
    }

    @Override
    public void edit(PointsInterestsTO params) {
        PointsInterests interests = interestsDao.byId(params.getId());
        BeanUtil.copyProperties(params, interests);
        interests.modify();
        interestsDao.updateById(interests);
    }

    @Override
    public void delete(String id) {
        PointsInterests interests = interestsDao.byId(id);
        interests.setEnable(0);
        interests.modify();
        interestsDao.updateById(interests);
    }

    @Override
    public List<PointsInterestsVO> list(Long countryId) {
        return interestsDao.voByIdListAndCountry(new ArrayList<>(), countryId);
    }
}
