package net.poweroak.saas.bucksc.controller.mgt;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.saas.bucksc.model.TO.PointsExchangeRegistrationIssueTO;
import net.poweroak.saas.bucksc.model.TO.PointsExchangeRegistrationPageTO;
import net.poweroak.saas.bucksc.model.TO.PointsExchangeRegistrationRefuseTO;
import net.poweroak.saas.bucksc.model.VO.PointsExchangeRegistrationVO;
import net.poweroak.saas.bucksc.service.IPointsExchangeRegistrationService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2023/11/3
 */
@Api(tags = "积分登记兑换管理后台")
@RestController
@RequiredArgsConstructor
//@RequestMapping("/" + RouteEndpoint.MGT + "/pointsExchangeRegistration")
//@RouteMenu(module = "pointsExchangeRegistration", label = "积分登记兑换管理", parent = BluBucksCenterApplication.class)
public class PointsExchangeRegistrationController {

    private final IPointsExchangeRegistrationService registrationService;

    @PostMapping("/page")
    @ApiOperation(value = "积分登记兑换分页", httpMethod = "POST")
//    @RouteAction(id = "18ba7aaf3ccf79115700ffbf6a8", action = "page", label = "积分登记兑换分页", view = "/BluBucksC/exchangeRegistration/page.vue", linkToModule = true)
    public UnifyResponse<Pagination<PointsExchangeRegistrationVO>> page(@RequestBody PointsExchangeRegistrationPageTO params) {
        return new UnifyResponse<>(registrationService.page(params));
    }

    @PutMapping("/refuse")
    @ApiOperation(value = "不发放", httpMethod = "PUT")
    public UnifyResponse<Boolean> refuse(@RequestBody PointsExchangeRegistrationRefuseTO params) {
        registrationService.refuse(params);
        return new UnifyResponse<>(Boolean.TRUE);
    }

    @PutMapping("/issue")
    @ApiOperation(value = "发放积分", httpMethod = "PUT")
    public UnifyResponse<Boolean> issue(@RequestBody @Valid PointsExchangeRegistrationIssueTO params) {
        registrationService.issue(params);
        return new UnifyResponse<>(Boolean.TRUE);
    }

    @PutMapping("/retract")
    @ApiOperation(value = "撤回积分", httpMethod = "PUT")
    public UnifyResponse<Boolean> retract(@RequestParam("id") String id) {
        registrationService.retract(id);
        return new UnifyResponse<>(Boolean.TRUE);
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出登记记录表", httpMethod = "POST")
    public UnifyResponse<Boolean> export(@RequestBody PointsExchangeRegistrationPageTO params, HttpServletResponse response) {
        registrationService.export(params, response);
        return new UnifyResponse<>(Boolean.TRUE);
    }

    @PostMapping("/importExcel")
    @ApiOperation(value = "导入批量发放数据", httpMethod = "POST")
    public UnifyResponse<Boolean> importExcel(MultipartFile file) {
        registrationService.importExcel(file);
        return new UnifyResponse<>(Boolean.TRUE);
    }
}
