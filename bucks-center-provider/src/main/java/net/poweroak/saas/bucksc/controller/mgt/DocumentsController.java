package net.poweroak.saas.bucksc.controller.mgt;

import net.poweroak.BluBucksCenterApplication;
import net.poweroak.framework.api.data.perms.norm.RouteMenu;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/7/21 11:06
 * @description
 */
@RestController
@ApiIgnore
@RouteMenu(module = "documents", label = "文案管理", parent = BluBucksCenterApplication.class,order = 6)
public class DocumentsController {


}