package net.poweroak.saas.bucksc.model.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2024/5/6
 */
@Data
public class PointsLevelUserGeneralVO {

    @ApiModelProperty("当前用户剩余积分")
    private Integer balance;

    @ApiModelProperty(value = "当前等级名称")
    private String levelName;

    @ApiModelProperty(value = "当前等级logo的链接")
    private String logoUrl;

    @ApiModelProperty(value = "升级进度(范围0-100)")
    private Integer upgradeProgress;

}
