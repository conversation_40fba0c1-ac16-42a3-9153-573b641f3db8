package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2024/5/6
 */
@Data
public class PointsLevelTO {
    private String id;

    @ApiModelProperty(value = "国家ID")
    @NotNull
    private Long countryId;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "货币单位")
    @NotBlank
    private String currencyUnit;

    @ApiModelProperty(value = "等级说明")
    private String instructions;

    @ApiModelProperty(value = "等级说明的html形式")
    private String instructionsHtml;

    @ApiModelProperty(value = "规则列表")
    private List<RuleTO> ruleList;

    @Data
    public static class RuleTO {
        @ApiModelProperty(value = "所绑定的等级id", hidden = true)
        private String pointsLevelId;

        @ApiModelProperty(value = "logo的链接")
        private String logoUrl;

        @ApiModelProperty(value = "该等级的名称")
        @NotBlank
        private String name;

        @ApiModelProperty(value = "消费门槛")
        @NotBlank
        private String consumptionThreshold;

        @ApiModelProperty(value = "赠送积分数")
        private Integer giftPoints;

        @ApiModelProperty(value = "积分权益id列表")
        private List<String> interestsIdList;
    }
}
