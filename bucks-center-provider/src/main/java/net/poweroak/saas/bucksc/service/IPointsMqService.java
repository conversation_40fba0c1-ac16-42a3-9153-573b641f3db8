package net.poweroak.saas.bucksc.service;

import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.saas.bucksc.request.IntegralPageRequest;
import net.poweroak.saas.bucksc.request.IntegralQueryRequest;
import net.poweroak.saas.bucksc.request.IntegralSpendRequest;
import net.poweroak.saas.bucksc.response.ShopifyBalanceJournalResponse;
import net.poweroak.saas.bucksc.response.ShopifyPointsQueryResponse;

/**
 * <AUTHOR>
 * @Mailto <EMAIL>
 * @Create 2024/5/9 15:26
 **/
public interface IPointsMqService {

    /** 积分变动处理 */
    void shopifyEarn(IntegralSpendRequest request);

    /** 积分账户信息查询 */
    ShopifyPointsQueryResponse getShopifyAccountInfo(IntegralQueryRequest request);

    /** 分页查询 */
    Pagination<ShopifyBalanceJournalResponse> getBalanceJournalPage(IntegralPageRequest pageRequest);

    /** 单笔积分明细 */
    ShopifyBalanceJournalResponse getBalanceJournalDetail(String id);

    /** 积分变动处理：MQ消费处理 */
    Boolean processingMessages(String recordId);

}
