package net.poweroak.saas.bucksc.service.impl;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.data.user.ContextUtils;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.enums.OrderBy;
import net.poweroak.framework.util.StringUtils;
import net.poweroak.saas.bucksc.dao.PointsBalanceDao;
import net.poweroak.saas.bucksc.dao.PointsJournalDao;
import net.poweroak.saas.bucksc.common.GenSubType;
import net.poweroak.saas.bucksc.enums.GenType;
import net.poweroak.saas.bucksc.model.PO.PointsBalance;
import net.poweroak.saas.bucksc.model.PO.PointsJournal;
import net.poweroak.saas.bucksc.model.TO.JournalAddTO;
import net.poweroak.saas.bucksc.request.PointsChangeRequest;
import net.poweroak.saas.bucksc.request.PointsRecordPageRequest;
import net.poweroak.saas.bucksc.request.TotalPointsRequest;
import net.poweroak.saas.bucksc.response.ActivityPointsRecordResponse;
import net.poweroak.saas.bucksc.response.PointsDetailResponse;
import net.poweroak.saas.bucksc.response.PointsRecordResponse;
import net.poweroak.saas.bucksc.service.IPointsAppletsUserService;
import net.poweroak.saas.bucksc.service.IPointsChange;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * Created by zx on 2022/8/11 10:36
 */
@Service
@Slf4j
public class PointsAppletsUserServiceImpl implements IPointsAppletsUserService {

    @Resource
    private IPointsChange pointsChange;

    @Override
    public void pointsChange(List<PointsChangeRequest> changeTOS) {

        changeTOS.forEach(changeTO -> {

            final String activityId = pointsChange.checkActivity(changeTO.getChannelCode(), changeTO.getActivityCode(), changeTO.getCountryId());

            //更新积分账户
            balanceDao.changeBalance(changeTO.getFlag() ? changeTO.getScore() : -changeTO.getScore(), changeTO.getUserId(), changeTO.getCountryId());

            //流水
            JournalAddTO addTO = JournalAddTO.builder()
                    .uid(changeTO.getUserId())
                    .channelCode(changeTO.getChannelCode())
                    .activityId(activityId)
                    .points(changeTO.getScore())
                    .countryId(changeTO.getCountryId())
                    .genTime(new Date())
                    .subType(changeTO.getIntegralType())
                    .genType(changeTO.getFlag() ? GenType.EARN.getStatusCode() : GenType.BACK.getStatusCode())
                    .desc(changeTO.getDesc())
                    .typeDesc(changeTO.getIntegralTypeDesc())
                    .build();

            //增加积分流水
            journalDao.add(addTO);

        });

    }

    @Override
    public Long totalPointsByType(TotalPointsRequest request) {

        final GenSubType subType = GenSubType.match(request.getIntegralType());

        switch (subType) {

            case APPLET_DEFAULT:
                return balancePoints(request.getUserId(), request.getCountryId());

            case ACTIVITY_CLOCK:
                return clockPoints(request.getUserId(), request.getCountryId());

            case INVITE:
                return invitePoints(request.getUserId(), request.getCountryId());

            case SHARE:
                return interactivePoints(request.getUserId(), request.getCountryId());

            case APPLET_SIGN:
                return signPoints(request.getUserId(), request.getCountryId());

            default:
                return 0L;
        }

    }

    /**
     * 积分明细分页
     */
    private Pagination<PointsDetailResponse> pointsDetailPage(Pagination<PointsJournal> pagination) {

        List<PointsDetailResponse> detailVOS = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(pagination.getContent())) {
            pagination.getContent().forEach(detail -> {
                PointsDetailResponse detailResponse = new PointsDetailResponse();
                detailResponse.setIntegralTypeDesc(detail.getTypeDesc());
                detailResponse.setDesc(detail.getDesc());
                detailResponse.setCreTime(detail.getCreTime());
                detailResponse.setScore(detail.getPoints().longValue());
                detailResponse.setFlag(detail.getGenType() == GenType.EARN.getStatusCode() ? true : false);
                detailVOS.add(detailResponse);
            });
        }

        return new Pagination<>(detailVOS, pagination.getPageable(), pagination.getTotalElements());
    }

    /**
     * * 积分记录 - 我的积分 (活动积分、租赁积分、消费积分)、积分明细分页
     * * @return
     */
    @Override
    public PointsRecordResponse pointsRecord(PointsRecordPageRequest recordPageTO) {

        PointsRecordResponse response = new PointsRecordResponse();

        //第一页时 页头信息
        if (recordPageTO.getPageNumber() == 1) {
            final PointsBalance pointsBalance = pointsBalance(ContextUtils.get().getUID(), recordPageTO.getCountryId());
            response.setIntegral(pointsBalance.getBalance().longValue());
            response.setConsumptionIntegral(0L);
            response.setLeaseIntegral(leasePoints(ContextUtils.get().getUID(), recordPageTO.getCountryId()));
            response.setActivityIntegral(activityPoints(ContextUtils.get().getUID(), recordPageTO.getCountryId()));
            response.setSignIntegral(signPoints(ContextUtils.get().getUID(), recordPageTO.getCountryId()));
        }
        //分页信息
        Pagination<PointsJournal> pagination = journalDao.find(mybatisQuery(false, false, false, false, false, false, null, recordPageTO.getCountryId()), recordPageTO.getPageNumber(), recordPageTO.getPageSize());
        final Pagination<PointsDetailResponse> pointsDetailResponses = pointsDetailPage(pagination);
        response.setIntegralDetailPage(pointsDetailResponses);

        return response;
    }


    /**
     * 积分记录 - 活动积分 (打卡积分、互动积分、老带新积分)、积分明细分页
     */
    @Override
    public ActivityPointsRecordResponse activityPointsRecord(PointsRecordPageRequest recordPageTO) {

        final MybatisQuery mybatisQuery = mybatisQuery(true, false, false, false, false, false, null, recordPageTO.getCountryId());

        if (StringUtils.isNotEmpty(recordPageTO.getStartTime())) {
            mybatisQuery.addCriterion(Criterion.where(PointsJournal::getCreTime).gte(recordPageTO.getStartTime()));
        }

        if (StringUtils.isNotEmpty(recordPageTO.getEndTime())) {
            mybatisQuery.addCriterion(Criterion.where(PointsJournal::getCreTime).lte(recordPageTO.getEndTime()));
        }
        Pagination<PointsJournal> pagination = journalDao.find(mybatisQuery, recordPageTO.getPageNumber(), recordPageTO.getPageSize());
        final Pagination<PointsDetailResponse> pointsDetailResponses = pointsDetailPage(pagination);

        ActivityPointsRecordResponse response = new ActivityPointsRecordResponse();
        response.setIntegralDetailPage(pointsDetailResponses);

        response.setActivityIntegral(activityPoints(ContextUtils.get().getUID(), recordPageTO.getCountryId()));
        response.setInteractiveIntegral(interactivePoints(ContextUtils.get().getUID(),recordPageTO.getCountryId()));
        response.setInviteIntegral(invitePoints(ContextUtils.get().getUID(),recordPageTO.getCountryId()));
        response.setClockIntegral(clockPoints(ContextUtils.get().getUID(),recordPageTO.getCountryId()));
        return response;
    }

    @Resource
    private PointsBalanceDao balanceDao;

    @Resource
    private PointsJournalDao journalDao;


    public Long balancePoints(String uid, Long countryId) {
        final PointsBalance pointsBalance = pointsBalance(uid, countryId);
        return pointsBalance == null ? 0L : pointsBalance.getBalance();
    }

    /**
     * 积分账户
     */
    public PointsBalance pointsBalance(String uid, Long countryId) {


        String userId = StringUtils.isNotEmpty(uid) ? uid : ContextUtils.get().getUID();

        PointsBalance pointsBalance = balanceDao.findOne(MybatisQuery.where()
                .addCriterion(Criterion.where(PointsBalance::getUid).is(userId))
                .addCriterion(Criterion.where(PointsBalance::getCountryId).is(countryId))
                .addCriterion(Criterion.where(PointsBalance::getEnable).is(true)));

        if (pointsBalance == null) {
            pointsBalance = new PointsBalance();
            pointsBalance.setBalance(0);
            pointsBalance.setEarn(0);
            pointsBalance.setSpend(0);
            pointsBalance.setCountryId(countryId);
            pointsBalance.setUid(userId);
            pointsBalance.create();
            balanceDao.insert(pointsBalance);
        }
        return pointsBalance;
    }


    //打卡
    public Long activityPoints(String uid, Long countryId) {

        final List<PointsJournal> pointsJournals = journalDao.find(mybatisQuery(true, false, false, false, false, false, uid, countryId));

        return calculateIntegral(pointsJournals);

    }

    //打卡
    public Long clockPoints(String uid, Long countryId) {

        final List<PointsJournal> pointsJournals = journalDao.find(mybatisQuery(false, true, false, false, false, false, uid, countryId));

        return calculateIntegral(pointsJournals);

    }

    //分享
    public Long interactivePoints(String uid, Long countryId) {

        final List<PointsJournal> pointsJournals = journalDao.find(mybatisQuery(false, false, true, false, false, false, uid, countryId));

        return calculateIntegral(pointsJournals);

    }

    //邀请
    public Long invitePoints(String uid, Long countryId) {

        final List<PointsJournal> pointsJournals = journalDao.find(mybatisQuery(false, false, false, true, false, false, uid, countryId));

        return calculateIntegral(pointsJournals);
    }

    //签到
    public Long signPoints(String uid, Long countryId) {

        final List<PointsJournal> pointsJournals = journalDao.find(mybatisQuery(false, false, false, false, true, false, uid, countryId));

        return calculateIntegral(pointsJournals);
    }

    private long leasePoints(String uid, Long countryId) {

        final List<PointsJournal> pointsJournals = journalDao.find(mybatisQuery(false, false, false, false, false, true, uid, countryId));

        return calculateIntegral(pointsJournals);
    }


    /**
     * APPLET_DEFAULT(100, "默认"),
     * ACTIVITY_CLOCK(101, "活动打卡"),
     * ACTIVITY_CLOCK_PIC(102, "打卡图片分"),
     * ACTIVITY_CLOCK_EGG(103, "打卡彩蛋分"),
     * FOLLOW(104, "关注"),
     * SHARE(105, "分享"),
     * INVITE(106, "邀请"),
     * APPLET_SIGN(107, "签到"),
     * LEASE(108, "租赁");
     */
    private MybatisQuery mybatisQuery(boolean activity, boolean clock, boolean interactive, boolean invite, boolean sign, boolean lease, String uid, Long countryId) {

        final MybatisQuery mybatisQuery = MybatisQuery.where()
                .addCriterion(Criterion.where(PointsJournal::getEnable).is(true))
                .addCriterion(Criterion.where(PointsJournal::getUid).is(StringUtils.isNotEmpty(uid) ? uid : ContextUtils.get().getUID()))
                .addCriterion(Criterion.where(PointsJournal::getCountryId).is(countryId))
                //活动分
                .addCriterion(Criterion.where(PointsJournal::getSubType).in(101, 102, 103, 104, 105, 106), () -> activity)
                //活动打卡分
                .addCriterion(Criterion.where(PointsJournal::getSubType).in(101, 102, 103), () -> clock)
                //关注、分享
                .addCriterion(Criterion.where(PointsJournal::getSubType).in(104, 105), () -> interactive)
                //邀请
                .addCriterion(Criterion.where(PointsJournal::getSubType).in(106), () -> invite)
                //签到
                .addCriterion(Criterion.where(PointsJournal::getSubType).in(107), () -> sign)
                //租赁
                .addCriterion(Criterion.where(PointsJournal::getSubType).in(108), () -> lease)
                .orderBy(PointsJournal::getCreTime, OrderBy.DESC);
        return mybatisQuery;
    }

    //积分计算
    private long calculateIntegral(List<PointsJournal> pointsJournals) {

        //正
        final long positive = pointsJournals.stream().filter(detail -> GenType.EARN.getStatusCode() == detail.getGenType()).mapToLong(PointsJournal::getPoints).sum();
        //负
        final long negative = pointsJournals.stream().filter(detail -> GenType.BACK.getStatusCode() == detail.getGenType()).mapToLong(PointsJournal::getPoints).sum();

        return positive - negative;

    }


}
