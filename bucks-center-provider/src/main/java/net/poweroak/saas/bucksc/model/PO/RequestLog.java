package net.poweroak.saas.bucksc.model.PO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.DomainEntity;
import net.poweroak.framework.sqltag.Table;

/**
 * <AUTHOR>
 * @date 2022/5/9 10:32
 */
@Data
@ApiModel("积分活动")
@Table(name = "request_log")
public class RequestLog extends DomainEntity<String> {

    private String requestParams;

    private Integer type;

    private Integer subType;

    private String errMsg;

    @ApiModelProperty(value = "mq消息处理状态：0=处理中 1=成功 2=失败")
    private Integer mqStatus;

    @ApiModelProperty(value = "请求来源 1=Shopify")
    private Integer sourceType;

}
