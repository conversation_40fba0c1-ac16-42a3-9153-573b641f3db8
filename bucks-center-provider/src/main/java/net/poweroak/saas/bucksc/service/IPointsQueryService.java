package net.poweroak.saas.bucksc.service;

import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.saas.bucksc.request.PointsEarnRuleQueryRequest;
import net.poweroak.saas.bucksc.request.PointsQueryJournalRequest;
import net.poweroak.saas.bucksc.request.PointsQueryRequest;
import net.poweroak.saas.bucksc.response.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/31
 */
public interface IPointsQueryService {

    /**
     * 一定时间内过期积分
     *
     * @param uid
     * @param countryId
     * @param days
     * @return
     */
    Integer getInvalidPoints(String uid, Long countryId, int days);

    /**
     * 积分查询
     *
     * @param request
     * @return
     */
    PointsBalanceTotalResponse querySummary(PointsQueryRequest request);


    /**
     * 签到信息
     *
     * @param uid
     * @param countryId
     * @param channelCode
     * @return
     */
    SignInfoResponse getSignInfo(String uid, Long countryId, String channelCode);


    /**
     * 文案信息
     * @param countryId
     * @param channelCode
     * @param documentCode
     * @param activityCode
     * @return
     */
    DocumentResponse findDocument(Long countryId, String channelCode, String documentCode, String activityCode,String language);

    DocumentResponse findOneDocument(Long countryId, String channelCode, String documentCode, String activityCode, String language);

    /**
     * 分类
     * @param lang
     * @return
     */
    List<GenTypeResponse> getGenTypeList(String lang);


    /**
     * 积分明细
     * @param request
     * @return
     */
    Pagination<PointsBalanceJournalResponse> getJournalPageRes(PointsQueryJournalRequest request);


    /**
     * 根据用户消费金额查相应的等级
     * @param request
     * @return
     */
    PointsEarnRuleResponse getEarnRule(PointsEarnRuleQueryRequest request);
}
