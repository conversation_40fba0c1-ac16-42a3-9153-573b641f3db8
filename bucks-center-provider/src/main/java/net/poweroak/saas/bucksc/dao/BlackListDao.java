package net.poweroak.saas.bucksc.dao;

import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.exception.ApplicationRuntimeException;
import net.poweroak.framework.data.mybatis.GenericRepository;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.enums.OrderBy;
import net.poweroak.framework.user.UserInfoForServer;
import net.poweroak.framework.util.StringUtils;
import net.poweroak.saas.bucksc.BucksCenterError;
import net.poweroak.saas.bucksc.mapper.BlackListMapper;
import net.poweroak.saas.bucksc.model.PO.BlackList;
import net.poweroak.saas.bucksc.model.TO.BlackListAddTO;
import net.poweroak.saas.bucksc.model.TO.BlackListTO;
import net.poweroak.saas.bucksc.model.VO.BlackListVO;
import net.poweroak.saas.uc.client.UserClient;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/26 17:59
 * @description
 */


@Repository
@Slf4j
public class BlackListDao extends GenericRepository<BlackList, BlackListMapper, String> {
    private final UserClient userClient;

    public BlackListDao(BlackListMapper genericMapper, UserClient userClient) {
        super(genericMapper);
        this.userClient = userClient;
    }

    /**
     * 黑名单列表
     *
     * @param request
     * @return
     */
    public UnifyResponse<Pagination<BlackListVO>> pageList(BlackListTO request) {

        MybatisQuery mybatisQuery = new MybatisQuery();

        if (request.getCountryId() != null) {
            mybatisQuery.addCriterion(Criterion.where(BlackList::getCountryId).is(request.getCountryId()));
        }

        mybatisQuery.addCriterion(Criterion.where(BlackList::getEnable).is(1));
        mybatisQuery.orderBy(BlackList::getCountryId, OrderBy.DESC);
        mybatisQuery.orderBy(BlackList::getJoinTime, OrderBy.DESC);


        Pagination<BlackList> list = this.find(mybatisQuery, request.getPageNumber(), request.getPageSize());
        List<BlackListVO> result = new ArrayList<>();

        list.getContent().forEach(a -> {
            BlackListVO vo = new BlackListVO();
            BeanUtils.copyProperties(a, vo);

            UnifyResponse<List<UserInfoForServer>> unifyResponse = userClient.list(Arrays.asList(a.getUid()));
            Boolean flag = false;
            if (unifyResponse.isOK()) {
                List<UserInfoForServer> user = unifyResponse.getData();
                if (user != null && user.size() > 0) {
                    vo.setAccount(user.get(0).getUsername());
                    vo.setUemail(user.get(0).getEmail());
                    vo.setUphone(user.get(0).getPhone());
                    vo.setUname(user.get(0).getNickname());

                    if (StringUtils.isNotEmpty(request.getUname()) || StringUtils.isNotEmpty(request.getUemail())) {
                        if (StringUtils.isNotEmpty(request.getUname())) {
                            if (StringUtils.isNotEmpty(vo.getUname())) {
                                if (vo.getUname().contains(request.getUname())) {
                                    flag = true;
                                }
                            }

                        }
                        if (StringUtils.isNotEmpty(request.getUemail())) {
                            if (StringUtils.isNotEmpty(vo.getUemail())) {
                                if (vo.getUemail().contains(request.getUemail())) {
                                    flag = true;
                                }
                            }

                        }
                    } else {
                        flag = true;
                    }
                }
            }

            if (flag) {
                result.add(vo);
            }

        });


        return new UnifyResponse((new Pagination<>(result, list.getPageable(), list.getTotalElements())));

    }


    /**
     * 加入黑名单
     *
     * @param request
     * @return
     */

    public UnifyResponse<Boolean> join(BlackListAddTO request) {
        BlackList blackList = new BlackList();

        if(StringUtils.isEmpty(request.getUid())){
            log.error("uid不能为空");
            throw new ApplicationRuntimeException(BucksCenterError.UID_CANNOT_BE_EMPTY);
        }


//        UnifyResponse<List<UserVO>> unifyResponse = userClient.getUsersByIds(request.getUid());
//        if (unifyResponse.isOK()) {

//            if(unifyResponse.getData().size()>0){
//                UserVO user = unifyResponse.getData().get(0);
        blackList.setUid(request.getUid());
        blackList.setEnable(1);
        blackList.setCountryId(request.getCountryId());
        blackList.setJoinTime(new Date());
        if (StringUtils.isNotEmpty(request.getReason())) {
            blackList.setReason(request.getReason());
        }
//                blackList.setUemail(user.getEmail());
//                blackList.setUname(user.getNickname());
//                blackList.setUphone(user.getPhone());
        blackList.create();
        this.insert(blackList);
//            }else{
//                throw new ApplicationRuntimeException(BucksCenterError.USER_IS_NOT_EXIST);
//            }
//        }
        return new UnifyResponse<>(Boolean.TRUE);
    }

    /**
     * 退出黑名单
     *
     * @param request
     * @return
     */
    public UnifyResponse<Boolean> out(BlackListAddTO request) {
        MybatisQuery mybatisQuery = new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(BlackList::getEnable).is(1));
        mybatisQuery.addCriterion(Criterion.where(BlackList::getUid).is(request.getUid()));
        mybatisQuery.addCriterion(Criterion.where(BlackList::getCountryId).is(request.getCountryId()));
        List<BlackList> blackLists = this.find(mybatisQuery);
        if (blackLists.size() > 0) {
            BlackList blackList = blackLists.get(0);
            blackList.setEnable(0);
            blackList.modify();
            this.updateById(blackList);
        }
        return new UnifyResponse<>(Boolean.TRUE);
    }


    /**
     * 是否为黑名单用户
     *
     * @param uid
     * @return
     */
    public boolean verifyBlack(String uid, Long countryId) {
        MybatisQuery mybatisQuery = new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(BlackList::getEnable).is(1));
        mybatisQuery.addCriterion(Criterion.where(BlackList::getUid).is(uid));
        mybatisQuery.addCriterion(Criterion.where(BlackList::getCountryId).is(countryId));
        return this.findOne(mybatisQuery) != null;
    }
}
