package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/7/21 10:46
 * @description
 */
@Data
public class DocumentsTypeUpdateTO {
    @ApiModelProperty(value = "文案名称")
    private String name;
    @ApiModelProperty(value = "文案代码")
    private String code;
    @ApiModelProperty(value = "id")
    private String id;
}
