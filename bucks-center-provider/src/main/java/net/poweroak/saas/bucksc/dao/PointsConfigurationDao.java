package net.poweroak.saas.bucksc.dao;

import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.data.mybatis.GenericRepository;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.saas.bucksc.mapper.PointsBalanceDetailMapper;
import net.poweroak.saas.bucksc.mapper.PointsConfigurationMapper;
import net.poweroak.saas.bucksc.model.PO.PointsBalanceDetail;
import net.poweroak.saas.bucksc.model.PO.PointsConfiguration;
import net.poweroak.saas.bucksc.model.TO.ChannelTO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/7/28 9:35
 * @description
 */
@Repository
@Slf4j
public class PointsConfigurationDao extends GenericRepository<PointsConfiguration, PointsConfigurationMapper, String> {
    public PointsConfigurationDao(PointsConfigurationMapper genericMapper) {
        super(genericMapper);
    }

    public UnifyResponse<PointsConfiguration> getConfiguration() {
        MybatisQuery mybatisQuery=new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(PointsConfiguration::getEnable).is(1));


        PointsConfiguration one = this.findOne(mybatisQuery);
        return new UnifyResponse(one);



    }

    public Boolean updateConfiguration(PointsConfiguration request) {

        if(this.getConfiguration().getData()!=null){
            PointsConfiguration result=this.getConfiguration().getData();
            BeanUtils.copyProperties(request, result);
            result.modify();
            this.updateById(result);
        }else{
            request.create();
            this.insert(request);
        }
        return true;
    }
}
