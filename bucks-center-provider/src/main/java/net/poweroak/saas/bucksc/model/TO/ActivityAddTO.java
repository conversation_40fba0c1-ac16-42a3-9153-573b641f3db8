package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/9 17:21
 * @description
 */
@Data
public class ActivityAddTO {


    /**
     * 渠道名称
     */
    @ApiModelProperty(value = "渠道标识")
    private String channelCode;

    /**
     * 渠道标识
     */
    @ApiModelProperty(value = "国家/地区编码")
    private Long countryId;

    /**
     * 渠道名称
     */
    @ApiModelProperty(value = "活动名称")
    private String name;

    /**
     * 渠道标识
     */
    @ApiModelProperty(value = "适用的积分发放规则：1-签到；2-货币兑换；9-通用")
    private String earnRule;

    /**
     * 渠道名称
     */
    @ApiModelProperty(value = "适用的积分消耗规则：1-券卡兑换；2-物品兑换；3-货币抵扣；9-通用")
    private String spendRule;


    @ApiModelProperty(value = "活动编码")
    private String code;

//    /**
//     * 渠道标识
//     */
//    @ApiModelProperty(value = "单词获取上限")
//    private Integer earnLimit;
//
//    /**
//     * 渠道名称
//     */
//    @ApiModelProperty(value = "是否允许超限获取：Y-允许；N-不允许")
//    private String earnOverLimit;

    private List<String> date;
}
