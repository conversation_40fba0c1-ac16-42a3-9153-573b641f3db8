package net.poweroak.saas.bucksc.dao;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.PageInfo;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.data.mybatis.GenericRepository;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.framework.data.mybatis.query.join.JoinType;
import net.poweroak.framework.enums.OrderBy;
import net.poweroak.framework.util.StringUtils;
import net.poweroak.saas.bucksc.enums.GenType;
import net.poweroak.saas.bucksc.enums.JournalSubType;
import net.poweroak.saas.bucksc.mapper.PointsJournalMapper;
import net.poweroak.saas.bucksc.model.PO.*;
import net.poweroak.saas.bucksc.model.TO.*;
import net.poweroak.saas.bucksc.model.VO.*;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * Created by zx on 2022/5/10 17:03
 */
@Repository
@Slf4j
public class PointsJournalDao extends GenericRepository<PointsJournal, PointsJournalMapper, String> {

    private final ActivityDao activityDao;
    private final ChannelDao channelDao;

    @Resource
    @Lazy
    private final PointsConfigurationDao pointsConfigurationDao;
    @Resource
    private PointsBalanceDao pointsBalanceDao;

    public PointsJournalDao(PointsJournalMapper genericMapper, ActivityDao activityDao, ChannelDao channelDao, PointsConfigurationDao pointsConfigurationDao) {
        super(genericMapper);
        this.activityDao = activityDao;
        this.channelDao = channelDao;
        this.pointsConfigurationDao = pointsConfigurationDao;

        ;
    }


    /**
     * 新增
     *
     * @param journalAddTO
     * @return
     */
    public String add(JournalAddTO journalAddTO) {
        PointsJournal pointsJournal = new PointsJournal();
        BeanUtils.copyProperties(journalAddTO, pointsJournal);
        if (journalAddTO.getSubOriType() == null) {
            pointsJournal.setSubOriType(journalAddTO.getSubType());
        }
        pointsJournal.create();
        this.insert(pointsJournal);
        return pointsJournal.getId();
    }


    /**
     * 根据uid查明细
     *
     * @param request
     * @return
     */
    public UnifyResponse<Pagination<PointsJournalVO>> findDetail(PointsJournalTO request) {
        PageInfo pageInfo = new PageInfo(request.getPageNumber(), request.getPageSize());
        Pagination<PointsJournalVO> results = this.findByJoin(this.getDetailQuery(request), pageInfo);
        return new UnifyResponse<>(results);
    }

    private MybatisQuery getDetailQuery(PointsJournalTO request) {
        MybatisQuery mybatisQuery = MybatisQuery
                .where(Criterion.where(PointsJournal::getUid).is(request.getUid()))
                .addCriterion(Criterion.where(PointsJournal::getEnable).is(1))
                .addCriterion(Criterion.where(PointsJournal::getCountryId).is(request.getCountryId()))
                .addCriterion(Criterion.where(Channel::getCode).is(request.getChannelCode()), () -> StringUtils.isNotEmpty(request.getChannelCode()))
                .addCriterion(Criterion.where(Activity::getId).is(request.getActivityId()), () -> StringUtils.isNotEmpty(request.getActivityId()))
                .join(PointsJournalVO.class)
                .joinTable(JoinType.LEFT, Activity.class)
                .on(PointsJournal::getActivityId, Activity::getId).complete()
                .joinTable(JoinType.LEFT, Channel.class)
                .on(PointsJournal::getChannelCode, Channel::getCode)
                .complete().build()
                .orderBy(PointsJournal::getGenTime, OrderBy.DESC);
        return mybatisQuery;
    }

    public List<PointsJournalVO> findDetailList(PointsJournalTO request) {
        return this.findByJoin(this.getDetailQuery(request));
    }

    /**
     * 分渠道统计积分
     *
     * @param request
     * @return
     */
    public UnifyResponse<Pagination<journalGroupVO>> pageByGroup(journalGroupTO request) {
        //国外分页从0开始,需要减一

        List<journalGroupVO> result = this.getMapper().pageByGroup(request.getChannelCode(), (request.getPageNumber() - 1) * request.getPageSize(), request.getPageSize());

        List<journalGroupVO> list = this.getMapper().countGroup(request.getChannelCode());//获取总条数
        Pagination pagination = new Pagination(result, new PageInfo(request.getPageNumber() - 1, request.getPageSize()), list.size());
        return new UnifyResponse<>(pagination);
    }


    /**
     * 分日期统计积分
     *
     * @param request
     * @return
     */
    public UnifyResponse<Pagination<journalDayVO>> pageByDay(journalDayTO request) {
//        Page page = PageHelper.startPage(request.getPageNumber() - 1, request.getPageSize());
        String begin1 = "";
        String end1 = "";
        if (request.getDates() != null) {
            if (request.getDates().size() != 0) {
                begin1 = request.getDates().get(0) + " 00:00:00";
                end1 = request.getDates().get(1) + " 23:59:59";
            }
        }

        List<journalDayVO> result = this.getMapper().pageByDay(begin1, end1, (request.getPageNumber() - 1) * request.getPageSize(), request.getPageSize(), request.getActivityId(), request.getChannelCode());
        result.forEach(a -> {
            if (a.getRatio() == null) {
                a.setRatio(new BigDecimal("0"));
            }
        });
        List<journalDayVO> size = this.getMapper().countByDay(begin1, end1, request.getActivityId(), request.getChannelCode());
        return new UnifyResponse<>(new Pagination(result, new PageInfo(request.getPageNumber() - 1, request.getPageSize()), size.size()));
    }


    protected void excelWriteResponse(String fileName, HttpServletResponse response) throws UnsupportedEncodingException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", StrUtil.concat(Boolean.TRUE, "attachment;filename=", fileName, ExcelTypeEnum.XLSX.getValue()));
    }


    /**
     * 分日期统计积分导出
     *
     * @param response
     */
    public void export(JournalDayExportTO request, HttpServletResponse response) {

        // 对时间的处理；
        String begin1 = "";
        String end1 = "";
        if (!Strings.isEmpty(request.getStartTime()) && !Strings.isEmpty(request.getEndTime())) {
            begin1 = request.getStartTime() + " 00:00:00";
            end1 = request.getEndTime() + " 23:59:59";
        }

        // 查询的数据列表；
        List<JournalDayExportListVO> journalDayExportListVOList = this.getMapper().export(begin1, end1, request.getActivityId(), request.getChannelCode());

        // 导出到excel；
        try {
            excelWriteResponse("Journal-Day-Export", response);
            EasyExcel.write(response.getOutputStream(), JournalDayExportListVO.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("sheet1").doWrite(journalDayExportListVOList);
        } catch (IOException e) {
            log.error("分日期统计积分导出失败：{}", e);
        }
    }


    public List<PointsAccountVO> findAccount() {

        return this.getMapper().findAccount();
    }

    /**
     * 清空
     *
     * @param
     */
    public void empty() {

        //取的积分清零周期
        PointsConfiguration data = pointsConfigurationDao.getConfiguration().getData();
        if (data != null) {
            Integer month = data.getZeroingCycle();

            //计算日期，format是累计增加,format1是应回收列表
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.MONTH, -month);
            cal.add(Calendar.DATE, +1);
            Date time = cal.getTime();
            String format = formatter.format(time);
            List<PointsJournalEmptyListVO> list = this.getMapper().countEmpty(format);

            MybatisQuery mybatisQuery = new MybatisQuery();
            for (int i = 0; i < list.size(); i++) {
                PointsJournalEmptyListVO vo = list.get(i);
                if (StringUtils.isEmpty(vo.getUid()) || vo.getCountryId() == null) {
                    continue;
                }
                mybatisQuery.addCriterion(Criterion.where(PointsBalance::getUid).is(vo.getUid()));
                mybatisQuery.addCriterion(Criterion.where(PointsBalance::getCountryId).is(vo.getCountryId()));
                mybatisQuery.addCriterion(Criterion.where(PointsBalance::getEnable).is(1));
                PointsBalance one = this.pointsBalanceDao.findOne(mybatisQuery);
                mybatisQuery.reset();
                if (one != null) {
                    Integer points = one.getBalance() - vo.getPoints();
                    if (points > 0) {
                        one.setBalance(one.getBalance() - points);
                        one.setSpend(one.getSpend() + points);
                        one.modify();
                        this.pointsBalanceDao.updateById(one);
                        PointsJournal newJournel = new PointsJournal();
                        newJournel.setUid(vo.getUid());
                        newJournel.setCountryId(vo.getCountryId());
                        newJournel.setPoints(points);
                        newJournel.setSubType(JournalSubType.EMPTY.getCode());
                        newJournel.setGenType(GenType.CLEAR.getStatusCode());
                        newJournel.setGenTime(new Date());
                        newJournel.create();
                        this.insert(newJournel);
                    }
                }
            }


//            //应回收列表
//            List<PointsJournalEmptyListVO> list = this.getMapper().emptyList(format1);
//
//
//            //遍历应回收列表
//            for(int i=0;i<list.size();i++){
//                int result=0;
//                //根据日期，uid,国家找到累计增加和余额
//                PointsJournalCumulativeVO cumulative = this.getMapper().cumulative(format, list.get(i).getUid(),list.get(i).getCountryId());
//                if(cumulative!=null){
//                    if(cumulative.getBalance()>cumulative.getPoints()&&cumulative.getBalance()-cumulative.getPoints()<list.get(i).getPoints()){
//                        result=cumulative.getBalance()-cumulative.getPoints();
//                    }
//                }else{
//                    //累计增加为空，找到余额，根据余额计算
//                    PointsBalance byUidAndCountry = pointsBalanceDao.findByUidAndCountry(list.get(i).getUid(), list.get(i).getCountryId());
//                    if(byUidAndCountry!=null){
//                        if(byUidAndCountry.getBalance()>=list.get(i).getPoints()){
//                            result=list.get(i).getPoints();
//                        }else{
//                            result=byUidAndCountry.getBalance();
//                        }
//                    }
//
//                }
//                if(result>0){
//                    //设置enabled为空，以免重复计算
//                    MybatisQuery mybatisQuery1=new MybatisQuery();
//                    mybatisQuery1.addCriterion(Criterion.where(PointsJournal::getUid).is(list.get(i).getUid()));
//                    mybatisQuery1.addCriterion(Criterion.where(PointsJournal::getCountryId).is(list.get(i).getCountryId()));
//                    mybatisQuery1.addCriterion(Criterion.where(PointsJournal::getActivityId).is(list.get(i).getActivityId()));
//                    mybatisQuery1.addCriterion(Criterion.where(PointsJournal::getChannelCode).is(list.get(i).getChannelCode()));
//                    mybatisQuery1.addCriterion(Criterion.where(PointsJournal::getEnable).is(1));
//                    List<PointsJournal> pointsJournals = this.find(mybatisQuery1);
//                    if(pointsJournals!=null){
//                        pointsJournals.forEach(a->{
//                            PointsJournal tem=new PointsJournal();
//                            BeanUtils.copyProperties(a, tem);
//                            tem.setEnable(0);
//                            tem.modify();
//                            this.updateById(tem);
//                        });
//
//                    }
//
//
//                    //新增清零记录
//                    PointsJournal pointsJournal = new PointsJournal();
//                            pointsJournal.setGenTime(new Date());
//                            pointsJournal.setUid(list.get(i).getUid());
//                            pointsJournal.setCountryId(list.get(i).getCountryId());
//                            if(StringUtils.isNotEmpty(list.get(i).getActivityId())){
//                                pointsJournal.setActivityId(list.get(i).getActivityId());
//                            }
//                            if(StringUtils.isNotEmpty(list.get(i).getChannelCode())){
//                                pointsJournal.setChannelCode(list.get(i).getChannelCode());
//                            }
//                            pointsJournal.setPoints(result);
//                            pointsJournal.setSubType(JournalSubType.EMPTY.getCode());
//                            pointsJournal.setGenType(GenType.CLEAR.getStatusCode());
//                            pointsJournal.create();
//                            this.insert(pointsJournal);
//                            //修改余额，累计发放，累计消耗
//                            MybatisQuery mybatisQuery = new MybatisQuery();
//                            mybatisQuery.addCriterion(Criterion.where(PointsBalance::getUid).is(list.get(i).getUid()));
//                            mybatisQuery.addCriterion(Criterion.where(PointsBalance::getCountryId).is(list.get(i).getCountryId()));
//                            List<PointsBalance> pointsBalances = this.pointsBalanceDao.find(mybatisQuery);
//                            if (pointsBalances != null && pointsBalances.size() > 0) {
//                                PointsBalance balance = pointsBalances.get(0);
//                                balance.setSpend(balance.getSpend() + result);
//                                balance.setBalance(balance.getBalance() - result);
//                                balance.modify();
//                                this.pointsBalanceDao.updateById(balance);
//                            }
//                }
//            }
        }
    }

    public List<PointsAbnDataVO> findAbnDataListByAccount(PointsAccountVO accountVO, Integer second, Integer genType) {

        return this.getMapper().findAbnDataListByAccount(accountVO, second, genType);
    }


    /**
     * 获取当月签到日志
     *
     * @param uid
     * @param countryId
     * @param month
     * @param actIdList
     * @return
     */
    public List<PointsJournal> getMonthSignList(String uid, Long countryId, String month, List<String> actIdList) {
        String beginMonth = month + "-01 00:00:00";
        Date endDate = DateUtil.endOfMonth(DateUtil.parse(beginMonth));
        MybatisQuery mybatisQuery = new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(PointsJournal::getActivityId).in(actIdList));
        mybatisQuery.addCriterion(Criterion.where(PointsJournal::getUid).is(uid));
        mybatisQuery.addCriterion(Criterion.where(PointsJournal::getCountryId).is(countryId));
        mybatisQuery.addCriterion(Criterion.where(PointsJournal::getGenTime).gte(beginMonth));
        mybatisQuery.addCriterion(Criterion.where(PointsJournal::getGenTime).lte(DateUtil.format(endDate, DatePattern.NORM_DATETIME_FORMAT)));
        return this.find(mybatisQuery);
    }

    /**
     * 订单是否发放过积分
     *
     * @param orderId
     * @return
     */
    public boolean hasOrderExists(String orderId) {
        MybatisQuery mybatisQuery = MybatisQuery.where()
                .addCriterion(Criterion.where(PointsJournal::getOrderId).is(orderId))
                .addCriterion(Criterion.where(PointsJournal::getGenType).is(GenType.EARN.getStatusCode()));
        return this.findOne(mybatisQuery) != null;
    }

    /** 订单是否发放过积分 */
    public boolean hasOrderExistsByOrderNo(String orderNo) {
        MybatisQuery mybatisQuery = MybatisQuery.where()
                .addCriterion(Criterion.where(PointsJournal::getOrderNo).is(orderNo));
        return this.findOne(mybatisQuery) != null;
    }
    public PointsJournal findByOrderNo(String orderNo) {
        MybatisQuery mybatisQuery = MybatisQuery.where()
                .addCriterion(Criterion.where(PointsJournal::getOrderNo).is(orderNo))
                .addCriterion(Criterion.where(PointsJournal::getEnable).is(Boolean.TRUE));
        return this.findOne(mybatisQuery);
    }


    /**
     * 是否赠送过额外积分
     *
     * @param sourceId
     * @return
     */
    public boolean hasExtra(String sourceId, String uid) {
        return this.findExtraBySourceIdAndUid(sourceId, uid) == null;
    }


    /**
     * 查找额外赠送积分
     *
     * @param sourceId
     * @param uid
     * @return
     */
    public PointsJournal findExtraBySourceIdAndUid(String sourceId, String uid) {
        MybatisQuery mybatisQuery = MybatisQuery.where().addCriterion(Criterion.where(PointsJournal::getSourceId).is(sourceId)).
                addCriterion(Criterion.where(PointsJournal::getUid).is(uid));
        return this.findOne(mybatisQuery);
    }

    /**
     * 光伏设备，同一时间是否发放积分
     *
     * @param sn
     * @param beginTime
     * @param endTime
     * @return
     */
    public boolean hasSnExists(String sn, String beginTime, String endTime) {
        MybatisQuery mybatisQuery = MybatisQuery.where()
                .addCriterion(Criterion.where(PointsJournal::getPvBeginTime).is(beginTime))
                .addCriterion(Criterion.where(PointsJournal::getPvEndTime).is(endTime))
                .addCriterion(Criterion.where(PointsJournal::getPvSn).is(sn));
        return this.findOne(mybatisQuery) != null;
    }

    /**
     * 获取总消费金额在范围内的人数
     * rangeStart <= 范围 < rangeEnd
     */
    public long getTotalAmountInRangeNum(Long countryId, BigDecimal rangeStart, BigDecimal rangeEnd) {
        return this.getMapper().getTotalAmountInRangeNum(countryId, rangeStart, rangeEnd);
    }


}
