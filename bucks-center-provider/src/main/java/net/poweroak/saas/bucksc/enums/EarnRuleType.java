package net.poweroak.saas.bucksc.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2022/5/10 8:46
 */
public enum EarnRuleType {
//    SIGN(1, "签到", "sign"),
//    EXCHANGE(2, "货币兑换", "exchange"),
//    PV_POWER(3, "光伏发电", "pvPower"),
//    COMM(9, "通用", "common");
//
//
//    private Integer code;
//    private String name;
//    private String model;
//
//    EarnRuleType(Integer code, String name, String model) {
//        this.code = code;
//        this.name = name;
//        this.model = model;
//    }
//
//    public static String getNameByCode(Integer code) {
//        EarnRuleType[] earnRuleTypes = EarnRuleType.values();
//        for (EarnRuleType earnRuleType : earnRuleTypes) {
//            if (earnRuleType.code.equals(code)) {
//                return earnRuleType.name;
//            }
//        }
//        return "";
//    }
//
//    public static EarnRuleType get(Integer code) {
//        EarnRuleType[] earnRuleTypes = EarnRuleType.values();
//        for (EarnRuleType earnRuleType : earnRuleTypes) {
//            if (earnRuleType.code.equals(code)) {
//                return earnRuleType;
//            }
//        }
//        return null;
//    }
//
//
//    public String getModel() {
//        return model;
//    }
//
//    public Integer getCode() {
//        return code;
//    }
//
//    public String getName() {
//        return name;
//    }
}
