package net.poweroak.saas.bucksc.model.TO;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2023/11/10
 */
@Data
public class PointsExchangeRegistrationIssueExcelTO {

    @ExcelProperty("登记邮箱")
    private String email;

    @ExcelProperty("购买平台")
    private String platform;

    @ExcelProperty("订单号")
    private String orderNumber;

    @ExcelProperty("备注")
    private String comment;

    @ExcelProperty("提交人邮箱")
    private String accountEmail;

    @ExcelProperty("提交人电话")
    private String phone;

    @ExcelProperty("发放积分")
    private Integer distributePoints;

    @ExcelProperty("积分账户国家")
    private String distributeCountryCode;
}
