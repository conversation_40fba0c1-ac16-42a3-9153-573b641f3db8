package net.poweroak.saas.bucksc.model.PO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.DomainEntity;
import net.poweroak.framework.sqltag.Table;

import java.math.BigDecimal;

/**
 * Created by zx on 2022/5/9 16:14
 */

@Data
@ApiModel("积分消耗规则")
@Table(name = "points_spend_rule")
public class PointsSpendRule extends DomainEntity<String> {

    @ApiModelProperty(value = "渠道标识")
    private String channelCode;

    @ApiModelProperty(value = "活动ID")
    private String activityId;

    @ApiModelProperty(value = "1-券卡兑换；2-物品兑换；3-货币抵扣；4-抽奖；9-通用")
    private Integer ruleType;

    @ApiModelProperty(value = "积分值")
    private Integer points;

//    @ApiModelProperty(value = "国家编码")
//    private String countryCode;

    @ApiModelProperty(value="国家Id")
    private Long countryId;

    @ApiModelProperty(value = "消耗门槛（设置只有达到或超过该门槛值时，才可以使用该规则）")
    private Integer spendLimit;

    @ApiModelProperty(value = "兑换比例")
    private BigDecimal exchangeRatio;

    @ApiModelProperty(value = "会员等级")
    private Integer memberLevel;

}
