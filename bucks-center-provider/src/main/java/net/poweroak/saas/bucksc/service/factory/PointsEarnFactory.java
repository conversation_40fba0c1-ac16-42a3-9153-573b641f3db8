package net.poweroak.saas.bucksc.service.factory;


import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.exception.ApplicationRuntimeException;
import net.poweroak.framework.util.StringUtils;
import net.poweroak.saas.bucksc.BucksCenterError;
import net.poweroak.saas.bucksc.common.ActivityEarnCode;
import net.poweroak.saas.bucksc.service.AbstractEarnPointsService;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2022/7/4 14:20
 */
@Slf4j
@Service
public class PointsEarnFactory {

    private static Map<String, AbstractEarnPointsService> serviceMap = new ConcurrentHashMap<>();


    public static  AbstractEarnPointsService getService(ActivityEarnCode activityEarnCode) {
        AbstractEarnPointsService abstractEarnPointsService = serviceMap.get(activityEarnCode.getModel());
        if (abstractEarnPointsService == null) {
            throw new ApplicationRuntimeException(BucksCenterError.EARN_RULE_NOT_EXISTS);
        }
        return abstractEarnPointsService;
    }

    public static void register(String name, AbstractEarnPointsService abstractEarnPointsService) {
        if (StringUtils.isEmpty(name) || abstractEarnPointsService == null) {
            return;
        }
        serviceMap.put(name, abstractEarnPointsService);
    }
}
