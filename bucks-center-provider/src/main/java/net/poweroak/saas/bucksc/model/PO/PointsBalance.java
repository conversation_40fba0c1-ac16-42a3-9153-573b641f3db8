package net.poweroak.saas.bucksc.model.PO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.DomainEntity;
import net.poweroak.framework.sqltag.Table;

import java.math.BigDecimal;

/**
 * Created by zx on 2022/5/10 16:49
 */
@Data
@ApiModel("积分余额表")
@Table(name = "points_balance")
public class PointsBalance extends DomainEntity<String> {


    @ApiModelProperty(value = "用户ID")
    private String uid;

//    @ApiModelProperty(value = "国家编码")
//    private String countryCode;

    @ApiModelProperty(value="国家Id")
    private Long countryId;

    @ApiModelProperty(value = "累积消耗")
    private Integer spend;

    @ApiModelProperty(value = "累积发放")
    private Integer earn;

    @ApiModelProperty(value = "余额")
    private Integer balance;
}
