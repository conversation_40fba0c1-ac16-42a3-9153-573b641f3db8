package net.poweroak.saas.bucksc.model.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2024/5/6
 */
@Data
public class PointsLevelDetailVO {
    private String id;

    @ApiModelProperty(value = "国家ID")
    private Long countryId;

    @ApiModelProperty(value = "国家名称")
    private String countryName;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "货币单位")
    private String currencyUnit;

    @ApiModelProperty(value = "等级说明")
    private String instructions;

    @ApiModelProperty(value = "等级说明的html形式")
    private String instructionsHtml;

    @ApiModelProperty(value = "规则列表")
    private List<RuleVO> ruleList;

    @Data
    public static class RuleVO {
        @ApiModelProperty(value = "所绑定的等级id")
        private String pointsLevelId;

        @ApiModelProperty(value = "logo的链接")
        private String logoUrl;

        @ApiModelProperty(value = "排序下标")
        private Integer index;

        @ApiModelProperty(value = "该等级的名称")
        private String name;

        @ApiModelProperty(value = "消费门槛")
        private String consumptionThreshold;

        @ApiModelProperty(value = "当前等级的客户数量")
        private Long userNum;

        @ApiModelProperty(value = "赠送积分数")
        private Integer giftPoints;

        @ApiModelProperty(value = "积分权益id列表")
        private List<String> interestsIdList;
    }
}
