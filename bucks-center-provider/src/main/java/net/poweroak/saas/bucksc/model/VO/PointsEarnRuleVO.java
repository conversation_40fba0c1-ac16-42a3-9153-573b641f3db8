package net.poweroak.saas.bucksc.model.VO;

import lombok.Data;
import net.poweroak.framework.sqltag.TableField;
import net.poweroak.saas.bucksc.model.PO.Activity;
import net.poweroak.saas.bucksc.model.PO.PointsEarnRule;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/5/7 9:15
 */
@Data
public class PointsEarnRuleVO {

    /**
     * id
     */
    @TableField(table = PointsEarnRule.class, field = "id")
    private String id;

    /**
     * 渠道编码
     */
    @TableField(table = PointsEarnRule.class, field = "channelCode")
    private String channelCode;

    /**
     * 规则类型
     */
    @TableField(table = PointsEarnRule.class, field = "ruleType")
    private Integer ruleType;


    /**
     * 积分值
     */
    @TableField(table = PointsEarnRule.class, field = "points")
    private BigDecimal points;

    /**
     * 额外积分奖励
     */
    @TableField(table = PointsEarnRule.class, field = "extraReward")
    private BigDecimal extraReward;

    /**
     * 会员等级
     */
    @TableField(table = PointsEarnRule.class, field = "memberLevel")
    private Integer memberLevel;

    /**
     * 发放门槛
     */
    @TableField(table = PointsEarnRule.class, field = "earnLimit")
    private BigDecimal earnLimit;

    /**
     * 发放类型
     */
    @TableField(table = PointsEarnRule.class, field = "earnType")
    private String  earnType;

    /**
     * 每日次数
     */
    @TableField(table = PointsEarnRule.class, field = "dailyNum")
    private Integer dailyNum;


    /**
     * 国家
     */
    @TableField(table = Activity.class, field = "countryId")
    private Long countryId;

    /**
     * 活动ID
     */
    @TableField(table = Activity.class, field = "id")
    private String activityId;

    /**
     * 活动name
     */
    @TableField(table = Activity.class, field = "name")
    private String name;
}
