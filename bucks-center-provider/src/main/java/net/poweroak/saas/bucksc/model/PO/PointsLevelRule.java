package net.poweroak.saas.bucksc.model.PO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.DomainEntity;
import net.poweroak.framework.sqltag.Table;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2024/4/30
 */
@Data
@ApiModel("积分等级规则")
@Table(name = "points_level_rule")
public class PointsLevelRule extends DomainEntity<String> {
    @ApiModelProperty(value = "所绑定的等级id")
    private String pointsLevelId;

    @ApiModelProperty(value = "排序下标")
    private Integer index;

    @ApiModelProperty(value = "logo的链接")
    private String logoUrl;

    @ApiModelProperty(value = "该等级的名称")
    private String name;

    @ApiModelProperty(value = "消费门槛")
    private BigDecimal consumptionThreshold;

    @ApiModelProperty(value = "赠送积分数")
    private Integer giftPoints;

    @ApiModelProperty(value = "积分权益id列表json")
    private String interestsIdList;
}
