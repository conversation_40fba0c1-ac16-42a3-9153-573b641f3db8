package net.poweroak.saas.bucksc.enums;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/7/25 12:02
 * @description
 */
public enum ChannelType {
    appkey(1,"应用表示"),
    shop(2,"店铺");




    private int code;
    private String name;

    ChannelType()
    {

    }

    ChannelType(int code, String name)
    {

        this.code = code;
        this.name = name;
    }



    public int getCode()
    {

        return code;
    }

    public String getName()
    {

        return name;
    }
}
