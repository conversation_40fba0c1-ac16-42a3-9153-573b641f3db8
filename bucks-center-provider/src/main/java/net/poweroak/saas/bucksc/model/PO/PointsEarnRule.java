package net.poweroak.saas.bucksc.model.PO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.DomainEntity;
import net.poweroak.framework.sqltag.Table;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/5/9 10:32
 */
@Data
@ApiModel("积分发放规则")
@Table(name = "points_earn_rule")
public class PointsEarnRule extends DomainEntity<String> {

    /**
     * 渠道编码
     */
    @ApiModelProperty(value = "渠道编码")
    private String channelCode;

    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID")
    private String activityId;

    /**
     * 规则类型
     */
    @ApiModelProperty(value = "规则类型 适用的积分发放规则：1-签到；2-货币兑换；3-光伏发电;99-通用")
    private Integer ruleType;

    /**
     * 积分值
     */
    @ApiModelProperty(value = "积分值")
    private BigDecimal points;

    /**
     * 额外积分奖励
     */
    @ApiModelProperty(value = "额外积分奖励")
    private BigDecimal extraReward;

    /**
     * 会员等级
     */
    @ApiModelProperty(value = "会员等级")
    private Integer memberLevel;

    /**
     * 发放门槛
     */
    @ApiModelProperty(value = "发放门槛")
    private BigDecimal earnLimit;

    /**
     * 发放门槛
     */
    @ApiModelProperty(value = "发放类型 1=一次性 2=无限次")
    private Integer earnType;

    @ApiModelProperty(value = "单用户每日获取次数")
    private Integer dailyNum;

    @ApiModelProperty(value = "编码")
    private String ruleCode;

    @ApiModelProperty(value = "标题")
    private String title;
}
