package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/5/7 9:15
 */
@Data
public class BalanceDetailAddTO {

    @ApiModelProperty(value = "用户ID")
    private String uid;

    @ApiModelProperty(value = "国家编码")
    private Long countryId;

    @ApiModelProperty(value = "发放个数")
    private Integer earn;

    @ApiModelProperty(value = "消耗个数")
    private Integer spend;

    @ApiModelProperty(value = "剩余积分")
    private Integer balance;

    @ApiModelProperty(value = "产生时间")
    private Date genTime;

    @ApiModelProperty(value = "消耗时间")
    private Date speTime;

    @ApiModelProperty(value = "回收清零时间")
    private Date recTime;

    @ApiModelProperty(value = "是否回收清零 Y=是 N=否")
    private String isRec;

    @ApiModelProperty(value = "订单ID")
    private String orderId;

}
