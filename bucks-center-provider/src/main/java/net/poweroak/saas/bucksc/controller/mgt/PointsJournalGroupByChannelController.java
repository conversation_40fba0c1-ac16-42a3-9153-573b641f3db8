package net.poweroak.saas.bucksc.controller.mgt;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.data.perms.norm.RouteMenu;
import net.poweroak.framework.pedestal.annotate.RequirePermission;
import net.poweroak.framework.pedestal.annotate.RouteAction;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.dao.PointsJournalDao;
import net.poweroak.saas.bucksc.model.TO.journalGroupTO;
import net.poweroak.saas.bucksc.model.VO.journalGroupVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/16 10:20
 * @description
 */

@ApiIgnore
@Slf4j
@RestController
@RequestMapping("/" + RouteEndpoint.MGT + "/pointsJournalGroup")
@Api(tags = "渠道积分统计")
@RouteMenu(module = "journalGroupMGT", label = "渠道积分统计", parent = PointsCountController.class,order = 996)

public class PointsJournalGroupByChannelController {

    @Resource
    PointsJournalDao pointsJournalDao;
    /**
     * 分渠道统计积分
     * @param request
     * @return
     */
    @PostMapping("/pageList")
    @ApiOperation(value = "分渠道统计积分")
    @RouteAction(id = "180cb6838bcf791157c8280baad", action = "page", label = "分渠道统计积分", view = "/BluBucksC/journalGroup/list.vue", linkToModule = true)
    @RequirePermission
    public UnifyResponse<Pagination<journalGroupVO>> pageList(@RequestBody journalGroupTO request) {
        return this.pointsJournalDao.pageByGroup(request);
    }
}
