package net.poweroak.saas.bucksc.enums;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/8/22 9:57
 * @description
 */

public enum JournalSubType {
    OTHER(0, "其他"),
    SIGN(1, "签到"),
    ORDER(2, "订单"),
    PV(3, "光伏发电"),
    COUPON(4,"优惠券兑换"),
    GOOD(5,"物品兑换"),
    EMPTY(6,"积分清零"),
    TURNTABLE(7,"大转盘"),
    RETURN(8,"回退"),
    EXTRA_REWARD(9,"额外奖励"),
    ACTIVITYSIGN(101,"活动打卡"),
    SIGNPIC(102,"打卡图片分"),
    SIGNEGG(103,"打卡彩蛋分"),
    FOCUS(104,"关注"),
    SHARE(105,"分享"),
    INVITATION(106,"邀请"),
    APPSIGN(107,"小程序签到"),
    LEASING(108,"租赁"),
    ;


    private Integer code;
    private String name;

    JournalSubType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(Integer code) {
        JournalSubType[] earnRuleTypes = JournalSubType.values();
        for (JournalSubType earnRuleType : earnRuleTypes) {
            if (earnRuleType.code.equals(code)) {
                return earnRuleType.name;
            }
        }
        return "";
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
