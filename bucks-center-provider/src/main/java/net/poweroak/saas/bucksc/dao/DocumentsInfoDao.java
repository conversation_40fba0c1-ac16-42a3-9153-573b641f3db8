package net.poweroak.saas.bucksc.dao;

import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.PageInfo;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.exception.ApplicationRuntimeException;
import net.poweroak.framework.data.mybatis.GenericRepository;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.data.mybatis.query.join.JoinType;
import net.poweroak.framework.enums.OrderBy;
import net.poweroak.framework.util.StringUtils;
import net.poweroak.saas.bucksc.BucksCenterError;
import net.poweroak.saas.bucksc.mapper.DocumentsInfoMapper;
import net.poweroak.saas.bucksc.model.PO.*;
import net.poweroak.saas.bucksc.model.TO.DocumentsInfoTO;
import net.poweroak.saas.bucksc.model.TO.DocumentsInfoUpdateTO;
import net.poweroak.saas.bucksc.model.VO.DocumentsInfoVO;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/7/25 14:24
 * @description
 */
@Repository
@Slf4j
public class DocumentsInfoDao  extends GenericRepository<DocumentsInfo, DocumentsInfoMapper, String> {
    public DocumentsInfoDao(DocumentsInfoMapper genericMapper) {
        super(genericMapper);
    }

    public UnifyResponse<Pagination<DocumentsInfoVO>> pageList(DocumentsInfoTO request) {
        PageInfo pageInfo = new PageInfo(request.getPageNumber(), request.getPageSize());
        MybatisQuery mybatisQuery = new MybatisQuery();

        mybatisQuery.orderBy(DocumentsInfo::getModTime,OrderBy.DESC);
        if(StringUtils.isNotEmpty(request.getActivityId())){
            mybatisQuery.addCriterion(Criterion.where(DocumentsInfo::getActivityId).is(request.getActivityId()));
        }
        if(StringUtils.isNotEmpty(request.getType())){
            mybatisQuery.addCriterion(Criterion.where(DocumentsInfo::getType).is(request.getType()));
        }

        if(request.getCountryId() != null){
            mybatisQuery.addCriterion(Criterion.where(DocumentsInfo::getCountryId).is(request.getCountryId()));
        }

        if(StringUtils.isNotEmpty(request.getLocaleCode())){
            mybatisQuery.addCriterion(Criterion.where(DocumentsInfo::getLocaleCode).regex(request.getLocaleCode()));
        }

        if(StringUtils.isNotEmpty(request.getChannelCode())){
            mybatisQuery.addCriterion(Criterion.where(DocumentsInfo::getChannelCode).is(request.getChannelCode()));
        }
        mybatisQuery.addCriterion(Criterion.where(DocumentsInfo::getEnable).is(1))
                .addCriterion(Criterion.where(DocumentsType::getEnable).is(1))
                .join(DocumentsInfoVO.class)
                //inner全联
                .joinTable(JoinType.INNER, DocumentsType.class)
                .on(DocumentsInfo::getType,DocumentsType::getCode).complete()
                //left左联，右边可以不存在
                .joinTable(JoinType.LEFT, Activity.class)
                .on(DocumentsInfo::getActivityId,Activity::getId ).complete()
                .build();
        Pagination<DocumentsInfoVO> list=this.findByJoin(mybatisQuery, pageInfo);
        return new UnifyResponse<>(list);

    }

    public UnifyResponse<Boolean> edit(DocumentsInfoUpdateTO request) {
        if(StringUtils.isEmpty(request.getType())){
            log.error("文案类型不能为空");
            throw new ApplicationRuntimeException(BucksCenterError.DOCUMENT_TYPE_CANNOT_BE_EMPTY);
        }
        if(request.isEditFlag()){
            MybatisQuery mybatisQuery=new MybatisQuery();
            mybatisQuery.addCriterion(Criterion.where(DocumentsInfo::getEnable).is(1));
            mybatisQuery.addCriterion(Criterion.where(DocumentsInfo::getCountryId).is(request.getCountryId()));
            mybatisQuery.addCriterion(Criterion.where(DocumentsInfo::getChannelCode).is(request.getChannelCode()));
            if(StringUtils.isNotEmpty(request.getActivityId())){
                mybatisQuery.addCriterion(Criterion.where(DocumentsInfo::getActivityId).is(request.getActivityId()));
            }
            mybatisQuery.addCriterion(Criterion.where(DocumentsInfo::getLocaleCode).is(request.getLocaleCode()));
            mybatisQuery.addCriterion(Criterion.where(DocumentsInfo::getType).is(request.getType()));
            List<DocumentsInfo> documentsInfos = this.find(mybatisQuery);
            if(documentsInfos.size()!=0){
                documentsInfos.forEach(a->{
                    if(!Objects.equals(a.getId(), request.getId())){
                        log.error("同一国家，渠道，活动,文案类型已经存在相同语种");
                        throw new ApplicationRuntimeException(BucksCenterError.LOCALE_ALREADY_EXISTS_IN_SAME_ACTIVITY_AND_COUTNRY_AND_CHANNEL_AND_DOCUMENT_TYPE);
                    }
                });
            }



            final Optional<DocumentsInfo> optional = this.findById(request.getId());
            if (optional.isPresent()) {
                DocumentsInfo documentsInfo = optional.get();
                documentsInfo.setType(request.getType());
                documentsInfo.setContent(request.getContent());
                documentsInfo.setActivityId(request.getActivityId());
                documentsInfo.setChannelCode(request.getChannelCode());
                documentsInfo.setCountryId(request.getCountryId());
                documentsInfo.setLocaleCode(request.getLocaleCode());
                documentsInfo.modify();
                this.updateById(documentsInfo);
            }
        }
        return new UnifyResponse<>(Boolean.TRUE);

    }


    public UnifyResponse<Boolean> add(DocumentsInfoUpdateTO request) {
        if(StringUtils.isEmpty(request.getType())){
            log.error("文案类型不能为空");
            throw new ApplicationRuntimeException(BucksCenterError.DOCUMENT_TYPE_CANNOT_BE_EMPTY);
        }
        if(!request.isEditFlag()){
            MybatisQuery mybatisQuery=new MybatisQuery();
            mybatisQuery.addCriterion(Criterion.where(DocumentsInfo::getEnable).is(1));
            mybatisQuery.addCriterion(Criterion.where(DocumentsInfo::getCountryId).is(request.getCountryId()));
            mybatisQuery.addCriterion(Criterion.where(DocumentsInfo::getChannelCode).is(request.getChannelCode()));
            if(StringUtils.isNotEmpty(request.getActivityId())){
                mybatisQuery.addCriterion(Criterion.where(DocumentsInfo::getActivityId).is(request.getActivityId()));
            }
            mybatisQuery.addCriterion(Criterion.where(DocumentsInfo::getType).is(request.getType()));
            mybatisQuery.addCriterion(Criterion.where(DocumentsInfo::getLocaleCode).is(request.getLocaleCode()));
            DocumentsInfo one = this.findOne(mybatisQuery);
            if(one!=null){
                log.error("同一国家，渠道，活动,文案类型已经存在相同语种");
                throw new ApplicationRuntimeException(BucksCenterError.LOCALE_ALREADY_EXISTS_IN_SAME_ACTIVITY_AND_COUTNRY_AND_CHANNEL_AND_DOCUMENT_TYPE);
            }
            DocumentsInfo documentsInfo=new DocumentsInfo();
            documentsInfo.setContent(request.getContent());
            documentsInfo.setActivityId(request.getActivityId());
            documentsInfo.setType(request.getType());
            documentsInfo.setChannelCode(request.getChannelCode());
            documentsInfo.setCountryId(request.getCountryId());
            documentsInfo.setLocaleCode(request.getLocaleCode());
            documentsInfo.create();
            this.insert(documentsInfo);
        }
        return new UnifyResponse<>(Boolean.TRUE);
    }

    public UnifyResponse<Boolean> deleteDocumentsInfo(String id) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(DocumentsInfo::getId).is(id));
        query.addUpdateItem(DocumentsInfo::getEnable, 0);
        this.update(query);
        return new UnifyResponse<>(Boolean.TRUE);

    }
}
