package net.poweroak.saas.bucksc.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.AppMessage;
import net.poweroak.framework.api.data.user.ContextUtils;
import net.poweroak.framework.api.exception.ApplicationRuntimeException;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.framework.util.StringUtils;
import net.poweroak.saas.bucksc.BucksCenterError;
import net.poweroak.saas.bucksc.common.ActivitySpendCode;
import net.poweroak.saas.bucksc.common.GenSubType;
import net.poweroak.saas.bucksc.dao.*;
import net.poweroak.saas.bucksc.enums.AbnRuleType;
import net.poweroak.saas.bucksc.enums.GenType;
import net.poweroak.saas.bucksc.model.PO.*;
import net.poweroak.saas.bucksc.model.TO.EmailWarnTO;
import net.poweroak.saas.bucksc.model.VO.PointsAbnDataVO;
import net.poweroak.saas.bucksc.request.DeductRatioRequest;
import net.poweroak.saas.bucksc.request.LotteryRuleRequest;
import net.poweroak.saas.bucksc.request.PointsSpendRequest;
import net.poweroak.saas.bucksc.response.LotteryRuleResponse;
import net.poweroak.saas.bucksc.response.PointsSpendResponse;
import net.poweroak.saas.bucksc.service.IPointsChange;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created by zx on 2022/5/12 9:51
 */
@Service
@Slf4j
public class PointsChangeImpl implements IPointsChange {

    @Resource
    private PointsBalanceDao pointsBalanceDao;

    @Resource
    private PointsJournalDao pointsJournalDao;

    @Resource
    private ChannelDao channelDao;

    @Resource
    private ActivityDao activityDao;

    @Resource
    private PointsSpendRuleDao spendRuleDao;

    @Resource
    private PointsAbnDataDao abnDataDao;

    @Resource
    private BlackListDao blackListDao;
    @Resource
    private PointsAbnDataDao pointsAbnDataDao;
    @Resource
    private PointsEmailWarnDao pointsEmailWarnDao;

    /**
     * 积分消耗
     */
    private PointsSpendResponse spend(PointsSpendRequest request, Integer gentType) {

        //校验规则
        final String activityId = check(request.getChannelCode(), request.getActivityCode(), request.getCountryId(), request.getPoints());

        //积分余额变动
        balanceChange(request, gentType);

        //增加流水
        final String journalId = addPointsJournal(request, gentType, activityId);

        return PointsSpendResponse.builder().journalId(journalId).build();
    }


    //校验账户用户积分
    private void checkPointsBalance(PointsSpendRequest request) {
        MybatisQuery query = new MybatisQuery()
                .addCriterion(Criterion.where(PointsBalance::getUid).is(StringUtils.isNotEmpty(request.getUid()) ? request.getUid() : ContextUtils.get().getUID()))
                .addCriterion(Criterion.where(PointsBalance::getEnable).is(true))
                .addCriterion(Criterion.where(PointsBalance::getCountryId).is(request.getCountryId()));
        PointsBalance pointsBalance = pointsBalanceDao.findOne(query);
        if (pointsBalance == null || request.getPoints().compareTo(pointsBalance.getBalance()) > 0) {
            throw new ApplicationRuntimeException(BucksCenterError.MORE_THAN_AVAILABLE_POINTS);
        }

    }

    @Override
    @Transactional
    public PointsSpendResponse other(PointsSpendRequest request) {
        request.setResourceType(GenSubType.OTHER.getStatusCode());
        return spend(request, GenType.SPEND.getStatusCode());
    }

    @Override
    @Transactional
    public PointsSpendResponse lottery(PointsSpendRequest request) {
        request.setResourceType(GenSubType.LOTTERY.getStatusCode());
        return spend(request, GenType.SPEND.getStatusCode());
    }

    @Override
    @Transactional
    public PointsSpendResponse goodsExchange(PointsSpendRequest request) {
        request.setResourceType(GenSubType.EXCHANGE.getStatusCode());
        return spend(request, GenType.SPEND.getStatusCode());
    }

    @Override
    @Transactional
    public PointsSpendResponse couponExchange(PointsSpendRequest request) {
        request.setResourceType(GenSubType.COUPON.getStatusCode());
        return spend(request, GenType.SPEND.getStatusCode());
    }

    @Resource
    private PointsEmailWarnDao emailWarnDao;


    @Override
    public LotteryRuleResponse lotteryRule(LotteryRuleRequest request) {

        final String activityId = checkChannelAndActivity(request.getChannelCode(), request.getActivityCode(), request.getCountryId());

        final PointsSpendRule spendRule = spendRuleDao.findOne(MybatisQuery.where()
                .addCriterion(Criterion.where(PointsSpendRule::getEnable).is(true))
                .addCriterion(Criterion.where(PointsSpendRule::getActivityId).is(activityId)));

        if (spendRule == null || spendRule.getPoints() == null)
            throw new ApplicationRuntimeException(BucksCenterError.LOTTERY_SPEND_RULE_NOT_EXITS);

        final List<BlackList> listList = blackListDao.find(MybatisQuery.where()
                .addCriterion(Criterion.where(BlackList::getUid).is(ContextUtils.get().getUID()))
                .addCriterion(Criterion.where(BlackList::getEnable).is(true))
                .addCriterion(Criterion.where(BlackList::getCountryId).is(request.getCountryId())));

        return LotteryRuleResponse.builder().points(spendRule.getPoints()).isBlack(CollectionUtils.isNotEmpty(listList) ? true : false).build();
    }


    @Override
    public PointsSpendResponse backGoodsExchange(PointsSpendRequest request) {
        request.setResourceType(GenSubType.BACK_GOODS_EXCHANGE.getStatusCode());
        //校验规则
        final PointsJournal earnJournal = pointsJournalDao.findOne(MybatisQuery.where()
                .addCriterion(Criterion.where(PointsJournal::getGenType).is(GenType.SPEND.getStatusCode()))
                .addCriterion(Criterion.where(PointsJournal::getSubType).is(GenSubType.EXCHANGE.getStatusCode()))
                .addCriterion(Criterion.where(PointsJournal::getOrderId).is(request.getOrderId())));

        if (earnJournal == null) {
            log.info("物品兑换积分回退失败,没有物品兑换记录");
            throw new ApplicationRuntimeException(BucksCenterError.POINTS_BACK_ERROR);
        }
        return spend(request, GenType.EARN.getStatusCode());
    }


    @Override
    public PointsSpendResponse pointsBack(PointsSpendRequest request) {
        request.setResourceType(GenSubType.BACK.getStatusCode());
        //校验规则
        final PointsJournal earnJournal = pointsJournalDao.findOne(MybatisQuery.where()
                .addCriterion(Criterion.where(PointsJournal::getGenType).is(GenType.EARN.getStatusCode()))
                .addCriterion(Criterion.where(PointsJournal::getOrderId).is(request.getOrderId())));

        if (earnJournal == null) {
            final Activity activity = activityDao.findOne(MybatisQuery.where()
                    .addCriterion(Criterion.where(Activity::getCode).is(request.getActivityCode()))
                    .addCriterion(Criterion.where(Activity::getCountryId).is(request.getCountryId()))
                    .addCriterion(Criterion.where(Activity::getChannelCode).is(request.getChannelCode()))
                    .addCriterion((Criterion.where(Activity::getEnable).is(true))).limit(1));

            if (activity != null) {
                abnDataDao.earlyWarn(PointsAbnDataVO.builder()
                        .channelCode(activity.getChannelCode())
                        .countryId(activity.getCountryId())
                        .activityId(activity.getId())
                        .abnType(AbnRuleType.RULE_BACK.getCode())
                        .uid(ContextUtils.get().getUID())
                        .abnTime(new Date())
                        .desc("积分回退没有找到消费记录")
                        .build());

                //邮件通知
                emailWarnDao.sendMail(new EmailWarnTO(ContextUtils.get().getUID(), "积分回退没有找到消费记录"));
            }
            log.error("积分回退没有找到消费记录");
            throw new ApplicationRuntimeException(BucksCenterError.POINTS_BACK_ERROR);
        }

        return spend(request, GenType.BACK.getStatusCode());
    }

    @Override
    public PointsSpendResponse deductRatio(DeductRatioRequest request) {

        final String activityId = check(request.getChannelCode(), request.getActivityCode(), request.getCountryId(), request.getDeductRatioPoints());

        final PointsSpendRule spendRule = spendRuleDao.findOne(MybatisQuery.where()
                .addCriterion(Criterion.where(PointsSpendRule::getActivityId).is(activityId))
                .addCriterion(Criterion.where(PointsSpendRule::getChannelCode).is(request.getChannelCode()))
                .addCriterion(Criterion.where(PointsSpendRule::getRuleType).is(ActivitySpendCode.CURRENCY_DEDUCTION.getRuleType()))
                .addCriterion(Criterion.where(PointsSpendRule::getEnable).is(true)));

        if (spendRule == null) throw new ApplicationRuntimeException(BucksCenterError.SPEND_RULE_NOT_EXISTS);

        final BigDecimal points = new BigDecimal(request.getDeductRatioPoints());

        final BigDecimal bigDecimal = points.subtract(spendRule.getExchangeRatio()).setScale(2, BigDecimal.ROUND_HALF_UP);

        return PointsSpendResponse.builder().ratioValue(bigDecimal).build();
    }


    /**
     * 积分余额变动
     */
    private void balanceChange(PointsSpendRequest request, Integer gentType) {

        if (GenType.SPEND.getStatusCode() == gentType) {
            checkPointsBalance(request);
        }

        String uid = StringUtils.isNotEmpty(request.getUid()) ? request.getUid() : ContextUtils.get().getUID();

        if (GenType.EARN.getStatusCode() == gentType && GenSubType.BACK_GOODS_EXCHANGE.getStatusCode().equals(request.getResourceType())) {
            pointsBalanceDao.changeBalance(request.getPoints(), uid, request.getCountryId());
        } else {
            pointsBalanceDao.changeBalance(-request.getPoints(), uid, request.getCountryId());
        }
    }

    /**
     * 增加流水
     */
    private String addPointsJournal(PointsSpendRequest request, Integer gentType, String activityId) {

        String uid = StringUtils.isNotEmpty(request.getUid()) ? request.getUid() : ContextUtils.get().getUID();
        PointsJournal pointsJournal = new PointsJournal();
        BeanUtils.copyProperties(request, pointsJournal);
        pointsJournal.setActivityId(activityId);
        pointsJournal.setUid(uid);
        pointsJournal.setGenTime(new Date());
        pointsJournal.setSubType(request.getResourceType());
        pointsJournal.setSubOriType(request.getResourceType());
        pointsJournal.setGenType(gentType);
        pointsJournal.create();
        //兼顾历史默认扣减积分就扣减金额：金额新增字段
        pointsJournal.setAmount(request.getAmount());
        pointsJournal.setAmountSymbol(request.getAmountSymbol());
        pointsJournal.setOrderNo(request.getOrderNo());
        pointsJournalDao.insert(pointsJournal);
        return pointsJournal.getId();
    }

    public String checkChannelAndActivity(String channelCode, String activityCode, Long countryId) {

        //渠道
        final Channel channel = channelDao.findOne(MybatisQuery.where()
                .addCriterion(Criterion.where(Channel::getEnable).is(true))
                .addCriterion(Criterion.where(Channel::getCode).is(channelCode)));
        if (channel == null) throw new ApplicationRuntimeException(BucksCenterError.CHANNEL_NOT_EXISTS);

        //渠道活动
        final Activity activity = activityDao.findOne(MybatisQuery.where()
                .addCriterion(Criterion.where(Activity::getCode).is(activityCode))
                .addCriterion(Criterion.where(Activity::getChannelCode).is(channelCode))
                .addCriterion(Criterion.where(Activity::getCountryId).is(countryId))
                .addCriterion(Criterion.where(Activity::getEnable).is(true)));
        if (activity == null) throw new ApplicationRuntimeException(BucksCenterError.ACTIVITY_IS_NOT_EXISTS);

        return activity.getId();
    }

    @Override
    public String checkActivity(String channelCode, String activityCode, Long countryId) {
        return checkChannelAndActivity(channelCode, activityCode, countryId);
    }


    /**
     * 账户校验
     */
    public String check(String channelCode, String activityCode, Long countryId, Integer points) {

        String activityId = checkChannelAndActivity(channelCode, activityCode, countryId);

        if (!efficientPoints(points)) throw new ApplicationRuntimeException(BucksCenterError.POINTS_VALUE_CHECK_ERROR);

        return activityId;
    }

    public boolean efficientPoints(Integer points) {

        return (points != null && points > 0) ? true : false;
    }

    /**
     * 积分登记兑换的积分撤回
     */
    @Transactional
    public void registrationPointsBack(PointsExchangeRegistration exchangeRegistration) {
        // 校验流水
        PointsJournal earnJournal = pointsJournalDao.findOne(MybatisQuery.where()
                .addCriterion(Criterion.where(PointsJournal::getGenType).is(GenType.EARN.getStatusCode()))
                .addCriterion(Criterion.where(PointsJournal::getOrderId).is(exchangeRegistration.getOrderNumber())));
        if (null == earnJournal) {
            log.error("积分回退没有找到发放记录");
            throw new ApplicationRuntimeException(BucksCenterError.POINTS_BACK_ERROR);
        }

        // 校验积分
        if (null == exchangeRegistration.getDistributePoints() || 1 > exchangeRegistration.getDistributePoints()) {
            throw new ApplicationRuntimeException(BucksCenterError.POINTS_VALUE_CHECK_ERROR);
        }

        PointsSpendRequest request = new PointsSpendRequest();
        request.setResourceType(GenSubType.BACK_GOODS_EXCHANGE.getStatusCode());
        request.setUid(exchangeRegistration.getUid());
        request.setPoints(exchangeRegistration.getDistributePoints());
        request.setChannelCode("System_Distribution");
        request.setCountryId(exchangeRegistration.getDistributeCountryId());
        request.setOrderId(exchangeRegistration.getOrderNumber());
        request.setRefundAmount(new BigDecimal(0));
        //积分余额变动
        balanceChange(request, GenType.BACK.getStatusCode());

        //增加流水
        addPointsJournal(request, GenType.BACK.getStatusCode(), null);

    }

    @Override
    public PointsSpendResponse openSpend(PointsSpendRequest request) {
        PointsSpendResponse spend = new PointsSpendResponse();
        String errMsg = null;
        AppMessage appMessage = null;
        try {
            spend = spend(request, GenType.SPEND.getStatusCode());
        } catch (ApplicationRuntimeException ex) {
            appMessage = ex.getExceptionMessage();
            errMsg = ex.getMessage();
        } catch (Exception e){
            e.printStackTrace();
            errMsg = e.getMessage();
        }
        if (StringUtils.isNotEmpty(errMsg)){
            String uid = StringUtils.isEmpty(request.getUid()) ? ContextUtils.get().getUID() : request.getUid();
            // 发送邮件
            addAbnData(uid, request.getChannelCode(), request.getCountryId(), request.getActivityId(), request.getPoints(), errMsg);
            if (appMessage != null) {
                throw new ApplicationRuntimeException(appMessage);
            }
        }
        return spend;
    }

    private void addAbnData(String uid, String channelCode, Long country, String actId, int points, String errMsg) {
        pointsAbnDataDao.earlyWarn(PointsAbnDataVO.builder().channelCode(channelCode)
                .countryId(country).activityId(actId).points(points).abnType(AbnRuleType.RULE_BACK.getCode())
                .uid(uid)
                .abnTime(new Date()).desc(errMsg).build());
        //邮件通知
        pointsEmailWarnDao.sendMail(new EmailWarnTO(uid, errMsg));
    }


}
