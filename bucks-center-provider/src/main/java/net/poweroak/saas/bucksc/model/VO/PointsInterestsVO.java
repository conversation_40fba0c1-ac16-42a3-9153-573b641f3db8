package net.poweroak.saas.bucksc.model.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.sqltag.TableField;
import net.poweroak.saas.bucksc.model.PO.PointsInterests;

import java.util.Date;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2024/5/7
 */
@Data
public class PointsInterestsVO {
    @TableField(table = PointsInterests.class, field = "id")
    private String id;

    @ApiModelProperty(value = "国家Id")
    @TableField(table = PointsInterests.class, field = "countryId")
    private Long countryId;

    @ApiModelProperty(value = "国家名字")
    private String countryName;

    @ApiModelProperty(value = "权益名称")
    @TableField(table = PointsInterests.class, field = "name")
    private String name;

    @ApiModelProperty(value = "权益图标链接")
    @TableField(table = PointsInterests.class, field = "iconUrl")
    private String iconUrl;

    @ApiModelProperty(value = "权益介绍")
    @TableField(table = PointsInterests.class, field = "instructions")
    private String instructions;

    @ApiModelProperty(value = "起始有效期")
    @TableField(table = PointsInterests.class, field = "effectiveStart")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date effectiveStart;

    @ApiModelProperty(value = "结束有效期")
    @TableField(table = PointsInterests.class, field = "effectiveEnd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date effectiveEnd;
}
