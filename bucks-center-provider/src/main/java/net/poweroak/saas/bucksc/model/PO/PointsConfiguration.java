package net.poweroak.saas.bucksc.model.PO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.DomainEntity;
import net.poweroak.framework.sqltag.Table;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/7/28 9:31
 * @description
 */
@Data
@ApiModel("积分配置")
@Table(name = "points_configuration")
public class PointsConfiguration extends DomainEntity<String> {

    @ApiModelProperty(value = "积分清零周期")
    private Integer zeroingCycle;
}
