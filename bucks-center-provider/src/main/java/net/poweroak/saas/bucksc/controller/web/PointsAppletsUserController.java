package net.poweroak.saas.bucksc.controller.web;

import io.swagger.annotations.Api;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.saas.bucksc.client.PointsAppletsUserClient;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.request.PointsChangeRequest;
import net.poweroak.saas.bucksc.request.PointsRecordPageRequest;
import net.poweroak.saas.bucksc.request.TotalPointsRequest;
import net.poweroak.saas.bucksc.response.ActivityPointsRecordResponse;
import net.poweroak.saas.bucksc.response.PointsRecordResponse;
import net.poweroak.saas.bucksc.service.IPointsAppletsUserService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by zx on 2022/8/8 16:54 用户小程序相关积分
 */

@RestController
@Api(tags = "用户小程序积分")
@RequestMapping("/" + RouteEndpoint.API + "/pointsApplets")
public class PointsAppletsUserController implements PointsAppletsUserClient {

    @Resource
    private IPointsAppletsUserService appletsUserService;

    @Override
    public UnifyResponse<Boolean> pointsChange(List<PointsChangeRequest> changeTOS) {

        appletsUserService.pointsChange(changeTOS);

        return new UnifyResponse(true);
    }

    @Override
    public UnifyResponse<Long> totalPointsByType(TotalPointsRequest totalPointsRequest) {
        return new UnifyResponse(appletsUserService.totalPointsByType(totalPointsRequest));
    }

    @Override
    public UnifyResponse<PointsRecordResponse> pointsRecord(PointsRecordPageRequest recordPageTO) {
        return new UnifyResponse(appletsUserService.pointsRecord(recordPageTO));
    }

    @Override
    public UnifyResponse<ActivityPointsRecordResponse> activityPointsRecord(PointsRecordPageRequest recordPageTO) {
        return new UnifyResponse(appletsUserService.activityPointsRecord(recordPageTO));
    }
}
