package net.poweroak.saas.bucksc.service;

import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.saas.bucksc.model.TO.PointsExchangeByOrderTO;
import net.poweroak.saas.bucksc.model.TO.PointsExchangeRegistrationIssueTO;
import net.poweroak.saas.bucksc.model.TO.PointsExchangeRegistrationPageTO;
import net.poweroak.saas.bucksc.model.TO.PointsExchangeRegistrationRefuseTO;
import net.poweroak.saas.bucksc.model.VO.PointsExchangeRegistrationVO;
import net.poweroak.saas.bucksc.model.VO.UserDefaultVO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2023/11/6
 */
public interface IPointsExchangeRegistrationService {

    void add(PointsExchangeByOrderTO params);

    /**
     * 获取初始化信息(默认邮箱、币种)
     *
     * @return
     */
    UserDefaultVO initInfo();

    Pagination<PointsExchangeRegistrationVO> page(PointsExchangeRegistrationPageTO params);

    /**
     * 不发放积分
     *
     * @param params
     */
    void refuse(PointsExchangeRegistrationRefuseTO params);

    /**
     * 发放积分
     *
     * @param params
     */
    void issue(PointsExchangeRegistrationIssueTO params);

    /**
     * 撤回已发放的积分
     *
     * @param id
     */
    void retract(String id);

    /**
     * 导出表格
     *
     * @param response
     */
    void export(@RequestBody PointsExchangeRegistrationPageTO params, HttpServletResponse response);

    /**
     * 导入表格
     *
     * @param file
     */
    void importExcel(MultipartFile file);
}
