package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/31 10:24
 * @description
 */
@Data
public class PointsInterveneAddTO {
    @ApiModelProperty(value = "活动编码")
    private String code;
    @ApiModelProperty(value = "渠道编码")
    private String channelCode;
    @ApiModelProperty(value = "国家编码")
    private Long countryId;
    @ApiModelProperty(value = "类型 1-增加；2-消耗")
    private Integer genType;
    @ApiModelProperty(value = "积分")
    private Integer points;
    @ApiModelProperty(value = "用户id")
    private String uid;
    @ApiModelProperty(value = "原因")
    private String reason;
}
