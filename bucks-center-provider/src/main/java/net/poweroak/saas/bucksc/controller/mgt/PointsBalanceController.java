package net.poweroak.saas.bucksc.controller.mgt;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.BluBucksCenterApplication;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.data.perms.norm.RouteMenu;
import net.poweroak.framework.pedestal.annotate.RequirePermission;
import net.poweroak.framework.pedestal.annotate.RouteAction;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.dao.PointsBalanceDao;
import net.poweroak.saas.bucksc.dao.PointsJournalDao;
import net.poweroak.saas.bucksc.model.PO.PointsBalance;
import net.poweroak.saas.bucksc.model.TO.PointsBalanceAddTO;
import net.poweroak.saas.bucksc.model.TO.PointsBalanceEditTO;
import net.poweroak.saas.bucksc.model.TO.PointsBalanceSearchTO;
import net.poweroak.saas.bucksc.model.TO.PointsJournalTO;
import net.poweroak.saas.bucksc.model.VO.PointsBalanceUservo;
import net.poweroak.saas.bucksc.model.VO.PointsJournalVO;
import net.poweroak.saas.bucksc.service.IPointsBalanceService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/11 10:40
 * @description
 */

//@ApiIgnore
@Slf4j
@RequestMapping("/" + RouteEndpoint.MGT + "/pointsBalance")
@Api(tags = "用户积分=====================")
@RestController
@RouteMenu(module = "pointsBalanceModule", label = "用户积分", parent = BluBucksCenterApplication.class,order = 1)
public class PointsBalanceController {

    @Resource
    PointsBalanceDao pointsBalanceDao;


    @Resource
    PointsJournalDao pointsJournalDao;


    @Resource
    IPointsBalanceService pointsBalanceService;



    /**
     * 分页查询用户
     * @param request
     * @return
     */

    @PostMapping("/pageList")
    @ApiOperation(value = "分页查询用户")
    @RouteAction(id = "180b1cf673ff791157c8280ba90", action = "page", label = "用户积分查询", view = "/BluBucksC/balance/list.vue", linkToModule = true)
    @RequirePermission
    public UnifyResponse<Pagination<PointsBalanceUservo>> pageList(@RequestBody PointsBalanceSearchTO request) {
        return this.pointsBalanceDao.pageList(request);
    }

    /**
     * 删除用户
     * @param id
     * @return
     */
    @GetMapping("/delete/{id}")
    @ApiOperation(value = "删除用户")
    public UnifyResponse<Boolean> delete(@PathVariable("id") String id) {
        return this.pointsBalanceDao.deleteRequest(id);
    }

    /**
     * 更新用户
     * @param request
     * @return
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新用户")
    public UnifyResponse<Boolean> update(@RequestBody PointsBalanceEditTO request) {
        return this.pointsBalanceDao.updatePointsBalance(request);
    }

    /**
     * 根据id获取单个用户
     * @param id
     * @return
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "用户单个查询")
    public UnifyResponse<PointsBalance> get(@PathVariable("id") String id) {
        return this.pointsBalanceDao.getOne(id);
    }


    /**
     * 不分页查询用户
     * @return
     */
    @GetMapping("/list")
    @ApiOperation(value = "不分页查询用户")
    public UnifyResponse<List<PointsBalance>> list() {
        return this.pointsBalanceDao.page();
    }


    /**
     * 新增用户
     * @param request
     * @return
     * @throws Exception
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增用户")
    public UnifyResponse<Boolean> add(@RequestBody PointsBalanceAddTO request) throws Exception {
        return this.pointsBalanceDao.add(request);

    }

    /**
     * 根据uid查明细
     * @param request
     * @return
     */
    @PostMapping("/getDetailByUid")
    @ApiOperation(value = "根据uid获取明细")
    @RouteAction(action = "getDetail",id = "182053178c0f79115229d154a61", label = "用户积分明细")
    @RequirePermission
    public UnifyResponse<Pagination<PointsJournalVO>> getDetail(@RequestBody PointsJournalTO request){
        return pointsJournalDao.findDetail(request);
    }


    /**
     * 用户积分导出
     * @param response
     */
    @GetMapping("/export")
    @ApiOperation(value = "用户积分导出")
    public void export(PointsBalanceSearchTO request, HttpServletResponse response) {
        pointsBalanceDao.export(request, response);
    }

    @PostMapping("/exportDetail")
    @ApiOperation(value = "用户积分详情导出")
    public void exportDetail(@RequestBody PointsJournalTO params, HttpServletResponse response) {
        pointsBalanceService.exportDetail(params, response);
    }
}
