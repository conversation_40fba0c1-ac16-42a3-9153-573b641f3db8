package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.page.PageInfo;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/30 9:54
 * @description
 */
@Data
public class PointsInterveneTO extends PageInfo {
    @ApiModelProperty(value = "活动id")
    private String activityId;
    @ApiModelProperty(value = "国家编码")
    private Long countryId;
    @ApiModelProperty(value = "渠道编码")
    private String channelCode;
    @ApiModelProperty(value = "类型 1-增加；2-消耗")
    private Integer genType;
    @ApiModelProperty(value = "用户id")
    private String uid;
}
