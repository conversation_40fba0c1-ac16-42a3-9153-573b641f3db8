package net.poweroak.saas.bucksc.dao;

import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.PageInfo;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.data.user.ContextUtils;
import net.poweroak.framework.api.exception.ApplicationRuntimeException;
import net.poweroak.framework.data.mybatis.GenericRepository;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.data.mybatis.query.join.JoinType;
import net.poweroak.framework.enums.OrderBy;
import net.poweroak.framework.user.UserInfoForServer;
import net.poweroak.framework.util.StringUtils;
import net.poweroak.saas.bucksc.BucksCenterError;
import net.poweroak.saas.bucksc.enums.GenType;
import net.poweroak.saas.bucksc.mapper.PointsInterveneMapper;
import net.poweroak.saas.bucksc.model.PO.*;
import net.poweroak.saas.bucksc.model.TO.PointsEarnSendTO;
import net.poweroak.saas.bucksc.model.TO.PointsInterveneAddTO;
import net.poweroak.saas.bucksc.model.TO.PointsInterveneTO;
import net.poweroak.saas.bucksc.model.VO.PointsEarnCommonVO;
import net.poweroak.saas.bucksc.model.VO.PointsInterveneVO;
import net.poweroak.saas.bucksc.request.PointsSpendRequest;
import net.poweroak.saas.bucksc.response.PointsSpendResponse;
import net.poweroak.saas.bucksc.service.IPointsChange;
import net.poweroak.saas.bucksc.service.IPointsEarnService;
import net.poweroak.saas.uc.client.UserClient;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/30 9:41
 * @description
 */
@Slf4j
@Repository
public class PointsInterveneDao extends GenericRepository<PointsIntervene, PointsInterveneMapper, String> {
    private final UserClient userClient;

    @Resource
    private IPointsEarnService pointsEarnService;


    @Resource
    private PointsJournalDao pointsJournalDao;

    @Resource
    private IPointsChange pointsChange;
    public PointsInterveneDao(PointsInterveneMapper genericMapper, UserClient userClient) {
        super(genericMapper);
        this.userClient = userClient;
    }

    /**
     * 查询人工干预列表
     * @param request
     * @return
     */

    public Pagination<PointsInterveneVO> getPage(PointsInterveneTO request) {


        MybatisQuery mybatisQuery = new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(PointsIntervene::getEnable).is(1));
        if(StringUtils.isNotEmpty(request.getActivityId())){
            mybatisQuery.addCriterion(Criterion.where(PointsJournal::getActivityId).is(request.getActivityId()));
        }

        if(StringUtils.isNotEmpty(request.getChannelCode())){
            mybatisQuery.addCriterion(Criterion.where(PointsJournal::getChannelCode).is(request.getChannelCode()));
        }

        if(request.getCountryId() != null){
            mybatisQuery.addCriterion(Criterion.where(PointsJournal::getCountryId).is(request.getCountryId()));

        }
//        if(request.getGenType()!=null){
//            mybatisQuery.addCriterion(Criterion.where(PointsJournal::getGenType).is(request.getGenType()));
//        }

        if(StringUtils.isNotEmpty(request.getUid())){
            mybatisQuery.addCriterion(Criterion.where(PointsJournal::getUid).is(request.getUid()));
        }

        mybatisQuery.join(PointsInterveneVO.class)
                .joinTable(JoinType.INNER,PointsJournal.class)
                    .on(PointsIntervene::getJournalId,PointsJournal::getId).complete()
                .build()
                .orderBy(PointsIntervene::getTime, OrderBy.DESC);

       PageInfo pageInfo=new PageInfo(request.getPageNumber(),request.getPageSize());
       Pagination<PointsInterveneVO> results=this.findByJoin(mybatisQuery,pageInfo);


       results.getContent().stream().forEach(a->{

           UnifyResponse<List<UserInfoForServer>> unifyResponse1 = userClient.list(Arrays.asList(a.getUid()));
           if (unifyResponse1.isOK()) {
               List<UserInfoForServer> user = unifyResponse1.getData();
               a.setUname(user.get(0).getNickname());
           }


           Optional<PointsJournal> byId = pointsJournalDao.findById(a.getJournalId());
           if(byId.isPresent()){
               UnifyResponse<List<UserInfoForServer>> unifyResponse = userClient.list(Arrays.asList(byId.get().getUid()));
               if (unifyResponse.isOK()) {
                   List<UserInfoForServer> user = unifyResponse.getData();
                   a.setJournalUname(user.get(0).getNickname());
               }
           }

       });


       return results;



    }

    /**
     * 新增人工干预
     * @param request
     * @return
     */
    public UnifyResponse<Boolean> add( PointsInterveneAddTO request) {

        if(StringUtils.isEmpty(request.getChannelCode()))
        {
            throw new ApplicationRuntimeException(BucksCenterError.CHANNEL_CODE_CANNOT_BE_EMPTY);
        }

        if(request.getCountryId() == null){
            throw new ApplicationRuntimeException(BucksCenterError.COUNTRY_CODE_CANNOT_BE_EMPTY);
        }

        if(StringUtils.isEmpty(request.getCode())){
            throw new ApplicationRuntimeException(BucksCenterError.ACTIVITY_CODE_CANNOT_BE_EMPTY);
        }




        //发放
        if(request.getGenType()== GenType.EARN.getStatusCode()){
            PointsEarnSendTO to=new PointsEarnSendTO();
            to.setPoints(request.getPoints());
            to.setChannelCode(request.getChannelCode());
            to.setUid(request.getUid());
            to.setCountryId(request.getCountryId());
            to.setActivityCode(request.getCode());
            PointsEarnCommonVO send = pointsEarnService.intervene(to);
            PointsIntervene intervene=new PointsIntervene();
            intervene.setTime(new Date());
            intervene.setJournalId(send.getJournalId());
            intervene.setUid(ContextUtils.get().getUID());
            intervene.setReason(request.getReason());
            intervene.create();
            this.insert(intervene);
        }
        //消耗
        if(request.getGenType()==GenType.SPEND.getStatusCode()){
            PointsSpendRequest spend=new PointsSpendRequest();
            spend.setPoints(request.getPoints());
            spend.setChannelCode(request.getChannelCode());
            spend.setActivityCode(request.getCode());
            spend.setCountryId(request.getCountryId());
            spend.setUid(request.getUid());
            PointsSpendResponse response = pointsChange.other(spend);
            PointsIntervene intervene=new PointsIntervene();
            intervene.setTime(new Date());
            intervene.setJournalId(response.getJournalId());
            intervene.setUid(ContextUtils.get().getUID());
            intervene.setReason(request.getReason());
            intervene.create();
            this.insert(intervene);
        }
        return new UnifyResponse<>(Boolean.TRUE);
    }
}
