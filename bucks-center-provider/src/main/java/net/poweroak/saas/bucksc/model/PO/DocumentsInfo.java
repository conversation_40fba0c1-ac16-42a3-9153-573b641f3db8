package net.poweroak.saas.bucksc.model.PO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.DomainEntity;
import net.poweroak.framework.sqltag.Table;
import org.springframework.web.multipart.MultipartFile;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/7/21 11:16
 * @description
 */
@Data
@ApiModel("文案类型表")
@Table(name = "documents_info")
public class DocumentsInfo extends DomainEntity<String> {

    @ApiModelProperty(value="活动编码")
    private String activityId;

    @ApiModelProperty(value="文案类型id")
    private String type;

    @ApiModelProperty(value="内容")
    private String content;

    @ApiModelProperty(value="渠道code")
    private String channelCode;

//    @ApiModelProperty(value="国家code")
//    private String countryCode;

    @ApiModelProperty(value="国家Id")
    private Long countryId;

    @ApiModelProperty(value="语种code")
    private String localeCode;
}
