package net.poweroak.saas.bucksc.dao;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.data.mybatis.GenericRepository;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.saas.bucksc.mapper.PointsLevelMapper;
import net.poweroak.saas.bucksc.model.PO.PointsLevel;
import net.poweroak.saas.bucksc.model.TO.PointsLevelPageTO;
import net.poweroak.saas.bucksc.model.TO.PointsLevelTO;
import net.poweroak.saas.bucksc.model.VO.PointsLevelVO;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2024/4/30
 */
@Slf4j
@Repository
public class PointsLevelDao extends GenericRepository<PointsLevel, PointsLevelMapper, String> {
    public PointsLevelDao(PointsLevelMapper genericMapper) {
        super(genericMapper);
    }

    public Pagination<PointsLevelVO> page(PointsLevelPageTO params) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsLevel::getEnable).is(1))
                .addCriterion(Criterion.where(PointsLevel::getCountryId).is(params.getCountryId()), () -> null != params.getCountryId())
                .join(PointsLevelVO.class);
        return this.findByJoin(query, params);
    }

    public PointsLevel byId(String id) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsLevel::getEnable).is(1))
                .addCriterion(Criterion.where(PointsLevel::getId).is(id));
        return this.findOne(query);
    }

    /**
     * 检查国家是否重复
     */
    public boolean checkCountry(PointsLevelTO params) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsLevel::getEnable).is(1))
                .addCriterion(Criterion.where(PointsLevel::getCountryId).is(params.getCountryId()))
                .addCriterion(Criterion.where(PointsLevel::getId).ne(params.getId()), () -> StrUtil.isNotBlank(params.getId()));
        return this.exists(query);
    }

    public PointsLevel byCountryId(Long countryId) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsLevel::getEnable).is(1))
                .addCriterion(Criterion.where(PointsLevel::getCountryId).is(countryId));
        return this.findOne(query);
    }
}
