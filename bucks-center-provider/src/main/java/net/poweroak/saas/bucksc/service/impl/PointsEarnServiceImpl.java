package net.poweroak.saas.bucksc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.AppMessage;
import net.poweroak.framework.api.data.user.ContextUtils;
import net.poweroak.framework.api.exception.ApplicationRuntimeException;
import net.poweroak.framework.cache.lock.DistributedLock;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.enums.OrderBy;
import net.poweroak.iso.CountryCode;
import net.poweroak.iso.vo.country.CountryRich;
import net.poweroak.saas.bucksc.BucksCenterError;
import net.poweroak.saas.bucksc.common.ActivityEarnCode;
import net.poweroak.saas.bucksc.common.GenSubType;
import net.poweroak.saas.bucksc.constants.RedisConstant;
import net.poweroak.saas.bucksc.dao.*;
import net.poweroak.saas.bucksc.enums.*;
import net.poweroak.saas.bucksc.model.PO.Activity;
import net.poweroak.saas.bucksc.model.PO.Channel;
import net.poweroak.saas.bucksc.model.PO.PointsBalance;
import net.poweroak.saas.bucksc.model.TO.*;
import net.poweroak.saas.bucksc.model.VO.PointsAbnDataVO;
import net.poweroak.saas.bucksc.model.VO.PointsEarnCommonVO;
import net.poweroak.saas.bucksc.response.PointsEarnResponse;
import net.poweroak.saas.bucksc.service.IPointsEarnService;
import net.poweroak.saas.bucksc.service.IPointsLevelUpgradeService;
import net.poweroak.saas.bucksc.service.factory.PointsEarnFactory;
import net.poweroak.saas.bucksc.utils.LocalDateUtil;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/7/4 14:20
 */
@Slf4j
@Service
public class PointsEarnServiceImpl implements IPointsEarnService {

    @Resource
    private PointsEarnRuleDao pointsEarnRuleDao;
    @Resource
    private PointsJournalDao pointsJournalDao;
    @Resource
    private PointsBalanceDao pointsBalanceDao;
    @Resource
    private ActivityDao activityDao;
    @Resource
    private ChannelDao channelDao;
    @Resource
    private RequestLogDao requestLogDao;
    @Resource
    private PointsAbnDataDao pointsAbnDataDao;
    @Resource
    private PointsEmailWarnDao pointsEmailWarnDao;

    @Resource
    private BlackListDao blackListDao;

    @Resource
    @Lazy
    private IPointsLevelUpgradeService levelUpgradeService;

    @Override
    public PointsEarnResponse send(PointsEranCommTO pointsEranCommTO, ActivityEarnCode activityEarnCode, GenSubType genSubType) {
        AppMessage appMessage = null;
        String errMsg = null;
        PointsEarnResponse pointsEarnResponse = new PointsEarnResponse();
        PointsEarnCommonVO pointsEarnCommonVO = new PointsEarnCommonVO();
        String lockId = "";
        if (!Strings.isEmpty(pointsEranCommTO.getOrderId())) {
            lockId = pointsEranCommTO.getOrderId() + "_";
        } else if (!Strings.isEmpty(pointsEranCommTO.getSn())) {
            lockId = pointsEranCommTO.getSn() + "_";
        }
        String lockKey = RedisConstant.REDIS_EARN_LOCK + lockId + pointsEranCommTO.getCountryId() + "_" + pointsEranCommTO.getActivityCode() + "_" + pointsEranCommTO.getUid();
        DistributedLock distributedLock = DistributedLock.getInstance();
        try {
            boolean locked = distributedLock.tryLock(lockKey, 500,2000);
            log.info("tryLock:{},key:{},time:{}", locked, lockKey, DateUtil.format(new Date(), DatePattern.NORM_DATETIME_MS_FORMAT));
            if (!locked) {
                throw new ApplicationRuntimeException(BucksCenterError.REQUEST_SO_FAST);
            }
            //检查
            checkPrecondition(pointsEranCommTO);
            //获取计算结果
            pointsEarnCommonVO = PointsEarnFactory.getService(activityEarnCode).calcPoints(pointsEranCommTO);

            if (pointsEranCommTO.isSend()) {
                //赠送积分
                pointsEarnCommonVO.setGenSubType(genSubType);
                pointsEarnCommonVO.setPointsEranCommTO(pointsEranCommTO);
                pointsEarnCommonVO.setRuleType(activityEarnCode.getRuleType());
                //校验积分异常值
                if (pointsEarnCommonVO.getPoints() < 0) {
                    log.error("积分值为:{},处理参数:{}", pointsEarnCommonVO.getPoints(), JSONUtil.toJsonStr(pointsEarnCommonVO));
                    throw new ApplicationRuntimeException(BucksCenterError.POINTS_VALUE_CHECK_ERROR);
                }
                //积分为零且无规则就报异常
                if (pointsEarnCommonVO.getPoints() <= 0 && !pointsEarnCommonVO.getRuleExist()) {
                    log.error("积分值为:0,处理参数:{}", JSONUtil.toJsonStr(pointsEarnCommonVO));
                    throw new ApplicationRuntimeException(BucksCenterError.EARN_RULE_NOT_EXISTS);
                }
                doSend(pointsEarnCommonVO);
            }

            //用户余额
            PointsBalance pointsBalance = pointsBalanceDao.findByUidAndCountry(pointsEranCommTO.getUid(), pointsEranCommTO.getCountryId());
            pointsEarnCommonVO.setBalance(pointsBalance.getBalance());
            BeanUtils.copyProperties(pointsEarnCommonVO, pointsEarnResponse);
        } catch (ApplicationRuntimeException ex) {
            appMessage = ex.getExceptionMessage();
            errMsg = ex.getMessage();
        } catch (Exception e) {
            e.printStackTrace();
            errMsg = e.getMessage();
        }finally {
            distributedLock.unlock(lockKey);
        }

        //异常记录日志
        if (errMsg != null) {
            log.error(errMsg);
            requestLogDao.add(JSONUtil.toJsonStr(pointsEranCommTO), errMsg, activityEarnCode.getRuleType(), genSubType.getStatusCode());
            int points = pointsEarnCommonVO.getPoints() == null ? 0 : pointsEarnCommonVO.getPoints();
            if (points == 0 && pointsEranCommTO.getPoints() != null && pointsEranCommTO.getPoints() > 0) {
                points = pointsEranCommTO.getPoints();
            }
            addAbnData(pointsEranCommTO.getUid(), pointsEranCommTO.getChannelCode(), pointsEranCommTO.getCountryId(), pointsEranCommTO.getActivityId(), points, errMsg, appMessage);
            if (appMessage != null) {
                throw new ApplicationRuntimeException(appMessage);
            }

        }
        return pointsEarnResponse;
    }

    @Override
    public PointsEarnCommonVO intervene(PointsEarnSendTO pointsEarnSendTO) {
        return sendPointsByTypeAndSubType(pointsEarnSendTO, ActivityEarnCode.COMMON.getRuleType(), GenSubType.OTHER);
    }

    /**
     * 登记发放积分
     *
     * @param pointsEarnSendTO
     * @return
     */
    @Override
    public PointsEarnCommonVO registration(PointsEarnSendTO pointsEarnSendTO, String orderId) {
//        return sendPointsByTypeAndSubType(pointsEarnSendTO, ActivityEarnCode.REGISTRATION.getRuleType(), GenSubType.EXCHANGE_ORDER_REGISTRATION);
        PointsEranCommTO pointsEranCommTO = new PointsEranCommTO();
        BeanUtils.copyProperties(pointsEarnSendTO, pointsEranCommTO);
        pointsEranCommTO.setChannelCode("System_Distribution");
        pointsEranCommTO.setOrderId(orderId);
        // 检查
        this.registrationCheckPrecondition(pointsEranCommTO);
        PointsEarnCommonVO pointsEarnCommonVO = new PointsEarnCommonVO();
        pointsEarnCommonVO.setPoints(pointsEranCommTO.getPoints());
        pointsEarnCommonVO.setPointsEranCommTO(pointsEranCommTO);
        pointsEarnCommonVO.setRuleType(ActivityEarnCode.REGISTRATION.getRuleType());
        pointsEarnCommonVO.setGenSubType(GenSubType.EXCHANGE_ORDER_REGISTRATION);
        doSend(pointsEarnCommonVO);
        return pointsEarnCommonVO;
    }

    private PointsEarnCommonVO sendPointsByTypeAndSubType(PointsEarnSendTO pointsEarnSendTO, Integer ruleType, GenSubType subType) {
        PointsEranCommTO pointsEranCommTO = new PointsEranCommTO();
        BeanUtils.copyProperties(pointsEarnSendTO, pointsEranCommTO);
        //检查
        checkPrecondition(pointsEranCommTO);
        PointsEarnCommonVO pointsEarnCommonVO = new PointsEarnCommonVO();
        pointsEarnCommonVO.setPoints(pointsEranCommTO.getPoints());
        pointsEarnCommonVO.setPointsEranCommTO(pointsEranCommTO);
        pointsEarnCommonVO.setRuleType(ruleType);
        pointsEarnCommonVO.setGenSubType(subType);
        doSend(pointsEarnCommonVO);
        return pointsEarnCommonVO;
    }

    /**
     * 前置检查
     *
     * @param request
     */
    private void checkPrecondition(PointsEranCommTO request) {
        //用户ID获取
        if (Strings.isEmpty(request.getUid())) {
            if (ContextUtils.get().getUID() == null) {
                throw new ApplicationRuntimeException(BucksCenterError.USER_IS_NOT_EXIST);
            }
            request.setUid(ContextUtils.get().getUID());
        }

        if (request.getCountryId() == null) {
            String countryCode = ContextUtils.get().getUserInfo().getCountry();
            if (Strings.isEmpty(countryCode)) {
                throw new ApplicationRuntimeException(BucksCenterError.COUNTRY_CODE_CANNOT_BE_EMPTY);
            }
            CountryRich countryRich = CountryCode.getByCountryCode(countryCode);
            request.setCountryId(countryRich.getGeoNameId());
        }


        //判断用户是否黑名单
        boolean isBlack = blackListDao.verifyBlack(request.getUid(), request.getCountryId());
        if (isBlack) {
            throw new ApplicationRuntimeException(BucksCenterError.POINTS_BALANCE_HAS_BAN);
        }

        //渠道是否存在
        MybatisQuery query = MybatisQuery.where()
                .addCriterion(Criterion.where(Channel::getCode).is(request.getChannelCode()))
                .addCriterion(Criterion.where(Channel::getEnable).is(1)).orderBy(Channel::getCreTime, OrderBy.DESC);
        Channel channel = channelDao.findOne(query);
        if (channel == null) {
            throw new ApplicationRuntimeException(BucksCenterError.CHANNEL_NOT_EXISTS);
        }

        //获取活动
        Activity activity = activityDao.getActivity(request.getActivityCode(), request.getCountryId(), request.getChannelCode());
        if (activity == null) {
            throw new ApplicationRuntimeException(BucksCenterError.ACTIVITY_IS_NOT_EXISTS);
        }

        long currentTimeMillis = System.currentTimeMillis();

        if (activity.getBeginTime() != null && activity.getEndTime() != null) {
            if (activity.getBeginTime().getTime() > currentTimeMillis) {
                throw new ApplicationRuntimeException(BucksCenterError.ACTIVITY_NOT_START);
            } else if (activity.getEndTime().getTime() < currentTimeMillis) {
                throw new ApplicationRuntimeException(BucksCenterError.ACTIVITY_IS_EXPIRE);
            }
        }

        PointsBalance pointsBalance = pointsBalanceDao.findByUidAndCountry(request.getUid(), request.getCountryId());
//        if (pointsBalance != null && pointsBalance.getEnable() == 0) {
//            throw new ApplicationRuntimeException(BucksCenterError.POINTS_BALANCE_HAS_BAN);
//        }

        //日期格式
        if (!Strings.isEmpty(request.getBeginTime()) && !Strings.isEmpty(request.getEndTime())) {
            boolean checkDate = true;
            try {
                DateUtil.parse(request.getBeginTime(), DatePattern.NORM_DATETIME_FORMAT);
                DateUtil.parse(request.getEndTime(), DatePattern.NORM_DATETIME_FORMAT);
            } catch (Exception e) {
                checkDate = false;
                e.printStackTrace();
            }
            if (!checkDate) {
                throw new ApplicationRuntimeException(BucksCenterError.DATE_FORMAT_ERROR);
            }
        }
        //PointsEranCommTO pointsEranCommTO = new PointsEranCommTO();
        //BeanUtils.copyProperties(request, pointsEranCommTO);
        request.setActivityId(activity.getId());
        request.setActivityCode(activity.getCode());
        request.setPointsBalance(pointsBalance);
        request.setBalance(pointsBalance == null ? 0 : pointsBalance.getBalance());
    }

    /**
     * 登记前置检查（移除了活动、渠道判断）
     *
     * @param request
     */
    private void registrationCheckPrecondition(PointsEranCommTO request) {
        //用户ID获取
        if (Strings.isEmpty(request.getUid())) {
            if (ContextUtils.get().getUID() == null) {
                throw new ApplicationRuntimeException(BucksCenterError.USER_IS_NOT_EXIST);
            }
            request.setUid(ContextUtils.get().getUID());
        }

        if (request.getCountryId() == null) {
            String countryCode = ContextUtils.get().getUserInfo().getCountry();
            if (Strings.isEmpty(countryCode)) {
                throw new ApplicationRuntimeException(BucksCenterError.COUNTRY_CODE_CANNOT_BE_EMPTY);
            }
            CountryRich countryRich = CountryCode.getByCountryCode(countryCode);
            request.setCountryId(countryRich.getGeoNameId());
        }

        //判断用户是否黑名单
        boolean isBlack = blackListDao.verifyBlack(request.getUid(), request.getCountryId());
        if (isBlack) {
            throw new ApplicationRuntimeException(BucksCenterError.POINTS_BALANCE_HAS_BAN);
        }

        //渠道是否存在
        MybatisQuery query = MybatisQuery.where()
                .addCriterion(Criterion.where(Channel::getCode).is(request.getChannelCode()))
                .addCriterion(Criterion.where(Channel::getEnable).is(1)).orderBy(Channel::getCreTime, OrderBy.DESC);
        Channel channel = channelDao.findOne(query);
        if (channel == null) {
            // 写死一个新的渠道
            ChannelAddTO channelAddTO = new ChannelAddTO();
            channelAddTO.setCode("System_Distribution");
            channelAddTO.setName("系统发放");
            channelDao.add(channelAddTO);
        }

        PointsBalance pointsBalance = pointsBalanceDao.findByUidAndCountry(request.getUid(), request.getCountryId());

        //日期格式
        if (!Strings.isEmpty(request.getBeginTime()) && !Strings.isEmpty(request.getEndTime())) {
            boolean checkDate = true;
            try {
                DateUtil.parse(request.getBeginTime(), DatePattern.NORM_DATETIME_FORMAT);
                DateUtil.parse(request.getEndTime(), DatePattern.NORM_DATETIME_FORMAT);
            } catch (Exception e) {
                checkDate = false;
                e.printStackTrace();
            }
            if (!checkDate) {
                throw new ApplicationRuntimeException(BucksCenterError.DATE_FORMAT_ERROR);
            }
        }
        request.setPointsBalance(pointsBalance);
        request.setBalance(pointsBalance == null ? 0 : pointsBalance.getBalance());
    }

    /**
     * 赠送积分，增加流水等
     *
     * @param pointsEarnCommonVO
     */
    private void doSend(PointsEarnCommonVO pointsEarnCommonVO) {
        PointsEranCommTO request = pointsEarnCommonVO.getPointsEranCommTO();

        Date currentDate = new Date();
        //流水
        JournalAddTO journalAddTO = JournalAddTO.builder()
                .uid(request.getUid())
                .channelCode(request.getChannelCode())
                .activityId(request.getActivityId())
                .points(pointsEarnCommonVO.getPoints())
                .ratio(pointsEarnCommonVO.getRatio())
                .orderId(request.getOrderId())
                .orderNo(request.getOrderNo())
                // 金额
                .amount(request.getAmount())
                .amountSymbol(request.getAmountSymbol())
                .desc(request.getDesc())
                .pvSn(request.getSn())
                .countryId(request.getCountryId())
                .genTime(currentDate)
                .localGenTime(LocalDateUtil.getLocalDate(request.getCountryId(), currentDate))
                .subOriType(pointsEarnCommonVO.getGenSubType().getStatusCode())
                .subType(pointsEarnCommonVO.getGenSubType().getStatusCode())
                .genType(GenType.EARN.getStatusCode())
                .pvBeginTime(Strings.isEmpty(request.getBeginTime()) ? null : DateUtil.parse(request.getBeginTime(), DatePattern.NORM_DATETIME_FORMAT))
                .pvEndTime(Strings.isEmpty(request.getEndTime()) ? null : DateUtil.parse(request.getEndTime(), DatePattern.NORM_DATETIME_FORMAT))
                .build();

        String journalId = pointsJournalDao.add(journalAddTO);
        pointsEarnCommonVO.setJournalId(journalId);
        int extraTotal = 0;
        if (pointsEarnCommonVO.getPointsEarnRuleId() != null) {
            //加入redis
            pointsEarnRuleDao.setEarnTimes(pointsEarnCommonVO.getPointsEarnRuleId(), request.getCountryId(), request.getUid(), EarnTimesType.ONE.getCode().equals(pointsEarnCommonVO.getEarnType()));
            long time = currentDate.getTime();
            //额外积分
            if (!CollectionUtil.isEmpty(pointsEarnCommonVO.getExtraRewordMap())) {
                for (Map.Entry<String, Integer> item : pointsEarnCommonVO.getExtraRewordMap().entrySet()) {
                    if (item.getValue().equals(0)) {
                        continue;
                    }
                    extraTotal += item.getValue();
                    time += 1000;
                    //额外积分记录
                    JournalAddTO extraReword = JournalAddTO.builder()
                            .uid(request.getUid())
                            .channelCode(request.getChannelCode())
                            .activityId(request.getActivityId())
                            .points(item.getValue())
                            .ratio(BigDecimal.ZERO)
                            .countryId(request.getCountryId())
                            .sourceId(item.getKey())
                            .genTime(new Date(time))
                            .localGenTime(LocalDateUtil.getLocalDate(request.getCountryId(), new Date(time)))
                            .subOriType(pointsEarnCommonVO.getGenSubType().getStatusCode())
                            .subType(GenSubType.EXTRA_REWARD.getStatusCode())
                            .genType(GenType.EARN.getStatusCode())
                            .build();
                    pointsJournalDao.add(extraReword);
//                    pointsEarnRuleDao.setExtraRewardTimes(pointsEarnCommonVO.getPointsEarnRuleId(), request.getCountryCode(), request.getUid(), item.getValue());
                }
            }
        }

        //增加积分余额
        pointsBalanceDao.changeBalance(pointsEarnCommonVO.getPoints() + extraTotal, request.getUid(), request.getCountryId());

        //签到记录加入redis
        if (ActivityEarnCode.SIGN.getRuleType().equals(pointsEarnCommonVO.getRuleType())) {
            pointsEarnRuleDao.setSignToRedis(request.getCountryId(), request.getChannelCode(), request.getUid(), LocalDateUtil.getLocalDateTime(request.getCountryId(), DateTime.now()))
            ;
        }

        // 有消费金额时，尝试升级等级并发放积分
        if (!ActivityEarnCode.POINTS_LEVEL_GIFT.getRuleType().equals(pointsEarnCommonVO.getRuleType())
                && journalAddTO.getAmount() != null
                && journalAddTO.getAmount().compareTo(BigDecimal.ZERO) > 0) {
            levelUpgradeService.levelUpgrade(journalAddTO.getUid(), journalAddTO.getCountryId());
        }
    }


    private void addAbnData(String uid, String channelCode, Long country, String actId, int points, String errMsg, AppMessage appMessage) {

        pointsAbnDataDao.earlyWarn(PointsAbnDataVO.builder()
                .channelCode(channelCode)
                .countryId(country)
                .activityId(actId)
                .points(points)
                .abnType(AbnRuleType.CUSTOMIZE_EARN.getCode())
                .uid(uid)
                .abnTime(new Date())
                .desc(errMsg)
                .build());

        //频繁和签到不提醒
        List<String> notNotifyList = ListUtil.toList(BucksCenterError.REQUEST_SO_FAST.getMsgId(), BucksCenterError.EARN_SIGN_HAS_EXISTS.getMsgId());
        if (appMessage == null || !notNotifyList.contains(appMessage.getMsgId())) {
            //邮件通知
            pointsEmailWarnDao.sendMail(new EmailWarnTO(uid, errMsg));
        }
    }
}
