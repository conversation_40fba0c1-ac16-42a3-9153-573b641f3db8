package net.poweroak.saas.bucksc.model.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Created by zx on 2022/5/31 17:33
 */
@Data
public class PointsAbnDataPageVO {

    @ApiModelProperty(value = "适用的积分消耗规则：1-积分发放；2-积分回退")
    private Integer abnType;

    @ApiModelProperty(value = "单位时间次数")
    private Integer abnCount;

    @ApiModelProperty("订单创建时间")
    @JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd")
    private Date creTime;


}
