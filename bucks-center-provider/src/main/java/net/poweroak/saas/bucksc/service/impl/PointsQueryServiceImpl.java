package net.poweroak.saas.bucksc.service.impl;


import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.data.user.ContextUtils;
import net.poweroak.framework.api.exception.ApplicationRuntimeException;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.framework.data.mybatis.query.join.JoinType;
import net.poweroak.framework.enums.OrderBy;
import net.poweroak.framework.i18n.I18nBuilder;
import net.poweroak.framework.i18n.LocaleUtils;
import net.poweroak.framework.util.StringUtils;
import net.poweroak.saas.bucksc.BucksCenterError;
import net.poweroak.saas.bucksc.common.ActivityEarnCode;
import net.poweroak.saas.bucksc.common.GenSubType;
import net.poweroak.saas.bucksc.dao.*;
import net.poweroak.saas.bucksc.enums.ChannelType;
import net.poweroak.saas.bucksc.enums.GenType;
import net.poweroak.saas.bucksc.enums.WeekCode;
import net.poweroak.saas.bucksc.model.PO.*;
import net.poweroak.saas.bucksc.model.VO.ActivityCommonVO;
import net.poweroak.saas.bucksc.model.VO.PointsBalanceTotalVO;
import net.poweroak.saas.bucksc.request.PointsEarnRuleQueryRequest;
import net.poweroak.saas.bucksc.request.PointsQueryJournalRequest;
import net.poweroak.saas.bucksc.request.PointsQueryRequest;
import net.poweroak.saas.bucksc.response.*;
import net.poweroak.saas.bucksc.service.IPointsQueryService;
import net.poweroak.saas.bucksc.utils.LocalDateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2022/5/31
 */
@Service
@Slf4j
public class PointsQueryServiceImpl implements IPointsQueryService {

    @Resource
    private PointsJournalDao pointsJournalDao;

    @Resource
    private PointsEarnRuleDao pointsEarnRuleDao;

    @Resource
    private ActivityDao activityDao;

    @Resource
    private PointsBalanceDao pointsBalanceDao;

    @Resource
    private DocumentsInfoDao documentsInfoDao;

    @Resource
    private ChannelDao channelDao;

    @Override
    public Integer getInvalidPoints(String uid, Long countryId, int days) {
        String date = DateUtil.format(DateUtil.offsetMonth(DateUtil.offsetDay(new Date(), days), -12), DatePattern.NORM_DATETIME_FORMAT);
        int dayPoints = pointsJournalDao.getMapper().sumPoints(date, uid, countryId, null);
        PointsBalance pointsBalance = pointsBalanceDao.findByUidAndCountry(uid, countryId);
        if (pointsBalance.getBalance() <= dayPoints) {
            return 0;
        }
        return pointsBalance.getBalance() - dayPoints;
    }

    /**
     * 积分总额查询
     *
     * @param request
     * @return
     */
    @Override
    public PointsBalanceTotalResponse querySummary(PointsQueryRequest request) {
        PointsBalanceTotalResponse pointsBalanceTotalResponse = new PointsBalanceTotalResponse();

        PointsBalanceTotalVO pointsBalanceTotalVO = pointsBalanceDao.queryPointsTotal(request.getUid(), request.getCountryId());

        if (pointsBalanceTotalVO == null) {
            return pointsBalanceTotalResponse;
        }
        //消耗积分
        int spendTotal = pointsBalanceTotalVO.getSpendTotal();
        //余额
        int balanceTotal = pointsBalanceTotalVO.getBalanceTotal();
        //总发放积分
        int earnTotal = pointsBalanceTotalVO.getEarnTotal();


        if (!Strings.isEmpty(request.getChannelCode())) {
            int channelTotal = pointsJournalDao.getMapper().sumPoints(null, request.getUid(), request.getCountryId(), request.getChannelCode());
            //渠道总发放积分
            pointsBalanceTotalResponse.setChannelTotal(channelTotal);
            //其他渠道总发放积分
            pointsBalanceTotalResponse.setOtherChannelTotal(Math.max(earnTotal - channelTotal, 0));
            Channel channel = channelDao.findByCode(request.getChannelCode());
            //同个国家的其他店铺不计算
            if (request.getExcludeOtherShops() == 1 && channel.getType() != null && channel.getType() == ChannelType.shop.getCode()) {
                List<ActivityCommonVO> activityCommonVOList = getOtherShop(request.getCountryId(), request.getChannelCode());
                int otherShopTotal = 0;
                for (ActivityCommonVO activityCommonVO : activityCommonVOList) {
                    otherShopTotal += pointsJournalDao.getMapper().sumPoints(null, request.getUid(), request.getCountryId(), activityCommonVO.getChannelCode());
                }
                //earnTotal = Math.max(earnTotal - otherShopTotal, 0);
                // otherTotal = Math.max(otherTotal - otherShopTotal, 0);
                int shopTotal = Math.max(balanceTotal - otherShopTotal, 0);
                pointsBalanceTotalResponse.setChannelAndCommonTotal(shopTotal);
            } else {
                int shopTotal = Math.max(balanceTotal, 0);
                pointsBalanceTotalResponse.setChannelAndCommonTotal(shopTotal);
            }

        }


        //余额
        pointsBalanceTotalResponse.setBalanceTotal(balanceTotal);
        //消耗积分
        pointsBalanceTotalResponse.setSpendTotal(spendTotal);
        //总发放积分
        pointsBalanceTotalResponse.setEarnTotal(earnTotal);
        //过期积分
        pointsBalanceTotalResponse.setValidTotal(getInvalidPoints(request.getUid(), request.getCountryId(), 7));
        return pointsBalanceTotalResponse;
    }

    @Override
    public SignInfoResponse getSignInfo(String uid, Long countryId, String channelCode) {
        SignInfoResponse signInfoResponse = new SignInfoResponse();
        DateTime dateTime = LocalDateUtil.getLocalDateTime(countryId);
        Map<String, String> map = pointsEarnRuleDao.getSignDate(countryId, channelCode, uid, dateTime);
        List<Activity> list = activityDao.findByEarnType(countryId, channelCode, ActivityEarnCode.SIGN.getRuleType());
        if (list.size() == 0) {
            return signInfoResponse;
        }
        Activity activity = list.get(0);

        int dayOfWeek = dateTime.dayOfWeek();
        signInfoResponse.setTodaySignStatus(map.containsKey(WeekCode.getCodeByOriCode(dayOfWeek) + "") ? 1 : 0);
        PointsEarnRule pointsEarnRule = null;
        //签到展示
        List<PointsEarnRule> ruleList = pointsEarnRuleDao.findSignRuleList(activity.getId(), channelCode);
        List<SignInfoResponse.Day> daysList = new ArrayList<>();
        Map<Integer, Integer> extraRewardMap = new HashMap<>();
        if (ruleList.size() > 0) {
            for (PointsEarnRule item : ruleList) {
                int day = item.getEarnLimit().intValue();
                SignInfoResponse.Day dayNote = new SignInfoResponse.Day();
                dayNote.setNo(WeekCode.getOriCodeByCode(day));
                dayNote.setName(WeekCode.getNameByCode(day));
                dayNote.setPoints(item.getPoints().intValue());
                dayNote.setSignStatus(map.containsKey(day + "") ? 1 : 0);
                int extraStatus = 0;
                daysList.add(dayNote);
                if (item.getExtraReward() != null && item.getExtraReward().intValue() > 0) {
                    extraRewardMap.put(day, item.getExtraReward().intValue());
                    extraStatus = 1;
                }
                dayNote.setExtraStatus(extraStatus);
                if (WeekCode.getOriCodeByCode(day) == dayOfWeek) {
                    pointsEarnRule = item;
                }
            }
            daysList = daysList.stream().sorted(Comparator.comparing(SignInfoResponse.Day::getNo)).collect(Collectors.toList());
        }
        //提示状态 1=展示今天获得的额外积分 2=展示下次获得积分 3=什么都不展示
        int tipStatus = 0;
        //额外获得积分
        int extraRewardPoints = 0;

        int dayOfWeekCode = WeekCode.getCodeByOriCode(dayOfWeek);

        //已连续签到天数
        int continueDays = this.getContinueDays(map, signInfoResponse.getTodaySignStatus() == 0 ? dayOfWeekCode - 1 : dayOfWeekCode);
        if (signInfoResponse.getTodaySignStatus() == 1 && extraRewardMap.getOrDefault(continueDays, 0) > 0) {
            //展示今天获得的额外积分
            tipStatus = 1;
            extraRewardPoints = extraRewardMap.get(continueDays);
        } else {
            /*
            //剩余可签到天数
            int totalSignDay = signInfoResponse.getTodaySignStatus() == 1 ? 7 : 8;
            int surplusDay = totalSignDay - WeekCode.getCodeByOriCode(dayOfWeek) + continueDays;
            //用剩余可签到天数判断是否满足下一个赠送额外积分条件
            Optional<Integer> optionalInteger = extraRewardMap.keySet().stream().sorted().filter(integer -> surplusDay >= integer).findFirst();
            */
            // 取消剩余天数判断，展示下一个连续签到奖励
            int finalContinueDays = continueDays;
            Optional<Integer> optionalInteger = extraRewardMap.keySet().stream().sorted().filter(integer -> integer >= finalContinueDays).findFirst();
            if (optionalInteger.isPresent()) {
                tipStatus = 2;
                extraRewardPoints = extraRewardMap.get(optionalInteger.get());
                continueDays = optionalInteger.get();
            } else {
                continueDays = 0;
                tipStatus = 3;
            }
        }

        int todaySignPoints = pointsEarnRule == null ? 0 : pointsEarnRule.getPoints().intValue();
        signInfoResponse.setSignPoints(todaySignPoints);
        signInfoResponse.setContinueDays(continueDays);
        signInfoResponse.setExtraPoints(extraRewardPoints);
        signInfoResponse.setDays(daysList);
        signInfoResponse.setCurrentDay(dayOfWeek);
        signInfoResponse.setTipStatus(tipStatus);
        return signInfoResponse;
    }

    /**
     * 获得签到天数
     *
     * @param map
     * @param today
     * @return
     */
    private int getContinueDays(Map<String, String> map, int today) {
        int days = 0;
        for (int i = today; i > 0; i--) {
            String signDay = map.get(i + "");
            if (Strings.isEmpty(signDay)) {
                break;
            }
            days += 1;
        }
        return days;
    }

    @Override
    public DocumentResponse findDocument(Long countryId, String channelCode, String documentCode, String activityCode, String language) {

        String activityId = null;
        if (!Strings.isEmpty(activityCode)) {
            MybatisQuery mybatisQuery = new MybatisQuery();
            mybatisQuery.addCriterion(Criterion.where(Activity::getCode).is(activityCode));
            mybatisQuery.addCriterion(Criterion.where(Activity::getChannelCode).is(channelCode));
            mybatisQuery.addCriterion(Criterion.where(Activity::getCountryId).is(countryId));
            Activity activity = activityDao.findOne(mybatisQuery);
            activityId = activity == null ? null : activity.getId();
        }

        if (Strings.isEmpty(documentCode) && Strings.isEmpty(activityId)) {
            throw new ApplicationRuntimeException(BucksCenterError.DOCUMENT_CODE_ACT_NOT_EMPTY);
        }

        MybatisQuery query = new MybatisQuery();
        if (!Strings.isEmpty(documentCode)) {
            query.addCriterion(Criterion.where(DocumentsInfo::getType).is(documentCode));
        }
        if (!Strings.isEmpty(activityId)) {
            query.addCriterion(Criterion.where(DocumentsInfo::getActivityId).is(activityId));
        }
        if (StringUtils.isNotEmpty(language)) {
            query.addCriterion(Criterion.where(DocumentsInfo::getLocaleCode).is(language));
        }
        query.addCriterion(Criterion.where(DocumentsInfo::getChannelCode).is(channelCode));
        query.addCriterion(Criterion.where(DocumentsInfo::getCountryId).is(countryId));
        List<DocumentsInfo> documentsInfoList = documentsInfoDao.find(query);
        if (CollectionUtils.isEmpty(documentsInfoList)) {
            return null;
        }
        String content = documentsInfoList.get(0).getContent();
        DocumentResponse documentResponse = new DocumentResponse();
        documentResponse.setContent(content);
        return documentResponse;
    }

    @Override
    public DocumentResponse findOneDocument(Long countryId, String channelCode, String documentCode, String activityCode, String language) {
        DocumentResponse document = this.findDocument(countryId, channelCode, documentCode, activityCode, language);
        if (ObjectUtil.isEmpty(document)) {
            document = this.findDocument(countryId, channelCode, documentCode, activityCode, "en-US");
        }
        if (ObjectUtil.isEmpty(document)) {
            document = this.findDocument(countryId, channelCode, documentCode, activityCode, "zh-CN");
        }
        if (ObjectUtil.isEmpty(document)) {
            throw new ApplicationRuntimeException(BucksCenterError.DOCUMENT_NOT_EXISTS);
        }
        return document;
    }

    @Override
    public List<GenTypeResponse> getGenTypeList(String lang) {
        List<GenTypeResponse> list = new ArrayList<>(GenType.values().length);
        Locale locale = getLocale(lang);
        for (GenType genType : GenType.values()) {
            GenTypeResponse genTypeResponse = new GenTypeResponse();
            genTypeResponse.setGenType(genType.getStatusCode());
            genTypeResponse.setGenTypeName(LocaleUtils.translate(locale, I18nBuilder.build(genType)));
            list.add(genTypeResponse);
        }
        return list;
    }


    /**
     * 积分明细查询接口
     *
     * @param request
     * @return
     */
    @Override
    public Pagination<PointsBalanceJournalResponse> getJournalPageRes(PointsQueryJournalRequest request) {
        if (log.isInfoEnabled()) {
            log.debug("积分明细查询：{}", JSONUtil.toJsonStr(request));
        }
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsJournal::getUid).is(request.getUid()));
        query.addCriterion(Criterion.where(PointsJournal::getCountryId).is(request.getCountryId()));
        if (request.getGenType() != null && request.getGenType() != 0) {
            query.addCriterion(Criterion.where(PointsJournal::getGenType).is(request.getGenType()));
        }
        if (!Strings.isEmpty(request.getActivityId())) {
            query.addCriterion(Criterion.where(PointsJournal::getActivityId).is(request.getActivityId()));
        }
        if (request.getSubType() != null) {
            query.addCriterion(Criterion.where(PointsJournal::getSubOriType).is(request.getSubType()));
        }
        if (!Strings.isEmpty(request.getChannelCode())) {
            if (request.getIsOnlySelfChannel() == 1) {
                query.addCriterion(Criterion.where(PointsJournal::getChannelCode).is(request.getChannelCode()));
            } else if (request.getIsOnlySelfChannel() == 2) {
                Channel channel = channelDao.findByCode(request.getChannelCode());
                if (channel.getType() != null && channel.getType() == ChannelType.shop.getCode()) {
                    List<ActivityCommonVO> activityCommonVOList = getOtherShop(request.getCountryId(), request.getChannelCode());
                    List<String> channelCodeList = activityCommonVOList.stream().map(ActivityCommonVO::getChannelCode).collect(Collectors.toList());
                    query.addCriterion(Criterion.where(PointsJournal::getChannelCode).nin(channelCodeList));
                }
            }
        }

        if (!Strings.isEmpty(request.getId())) {
            query.addCriterion(Criterion.where(PointsJournal::getId).is(request.getId()));
        }
        if (!Strings.isEmpty(request.getOrderId())) {
            query.addCriterion(Criterion.where(PointsJournal::getOrderId).is(request.getOrderId()));
        }
        if (!Strings.isEmpty(request.getOrderNo())) {
            query.addCriterion(Criterion.where(PointsJournal::getOrderNo).regex(request.getOrderNo()));
        }
        if (!Strings.isEmpty(request.getEndTime()) && !Strings.isEmpty(request.getBeginTime())) {
            query.addCriterion(Criterion.and(Criterion.where(PointsJournal::getCreTime).gte(request.getBeginTime()), Criterion.where(PointsJournal::getCreTime).lt(request.getEndTime())));
        }
        query.orderBy(PointsJournal::getCreTime, OrderBy.DESC);
        Pagination<PointsJournal> pagination = pointsJournalDao.find(query, request.getPageNumber(), request.getPageSize());
        List<PointsBalanceJournalResponse> journalResponseList = new ArrayList<>(pagination.getNumber());
        Map<String, String> channelMap = channelDao.findAll().stream().collect(Collectors.toMap(Channel::getCode, Channel::getName, (key1, key2) -> key1));
        Map<String, String> actMap = activityDao.findByCountry(request.getCountryId()).stream().collect(Collectors.toMap(Activity::getId, Activity::getName));
        Locale finalLocale = getLocale(request.getLang());
        pagination.getContent().forEach(item -> {
            PointsBalanceJournalResponse pointsBalanceJournalResponse = new PointsBalanceJournalResponse();
            pointsBalanceJournalResponse.setGenTime(DateUtil.format(LocalDateUtil.getLocalDate(request.getCountryId(), item.getGenTime()), DatePattern.NORM_DATETIME_FORMAT.getPattern()));
            pointsBalanceJournalResponse.setBalance(item.getPoints());
            pointsBalanceJournalResponse.setOrderId(item.getOrderId() == null ? "" : item.getOrderId());
            pointsBalanceJournalResponse.setChannelCode(item.getChannelCode());
            pointsBalanceJournalResponse.setRatio(item.getRatio() == null ? "" : item.getRatio().toString());
            if (GenType.EARN.getStatusCode() == item.getGenType()) {
                pointsBalanceJournalResponse.setInvalidTime(DateUtil.format(DateUtil.offsetMonth(LocalDateUtil.getLocalDate(request.getCountryId(), item.getGenTime()), 12).toLocalDateTime(), DatePattern.NORM_DATETIME_FORMAT.getPattern()));
            } else {
                pointsBalanceJournalResponse.setInvalidTime("");
            }
            pointsBalanceJournalResponse.setSymbol(item.getGenType() == GenType.EARN.getStatusCode() ? 1 : 2);
            pointsBalanceJournalResponse.setId(item.getId());
            pointsBalanceJournalResponse.setGenType(item.getGenType());
            GenType genType = GenType.match(item.getGenType());
            String typeName = genType != null ? LocaleUtils.translate(finalLocale, I18nBuilder.build(genType)) : "";
            pointsBalanceJournalResponse.setGenTypeName(typeName);

            //app端展示图标，如果是额外积分，需要关联原来的类型（签到额外还是订单额外）
            pointsBalanceJournalResponse.setSubType(item.getSubOriType() == null ? item.getSubType() : item.getSubOriType());
            GenSubType genSubType = GenSubType.match(item.getSubType());
            String subTypeName = genSubType != null ? LocaleUtils.translate(finalLocale, I18nBuilder.build(genSubType)) : "";
            pointsBalanceJournalResponse.setSubTypeName(subTypeName);
            String actName = actMap.get(item.getActivityId());
            if (!Strings.isEmpty(actName)) {
                pointsBalanceJournalResponse.setActivityName(actName);
            }
            String channelName = channelMap.get(item.getChannelCode());
            if (!Strings.isEmpty(channelName)) {
                pointsBalanceJournalResponse.setChannelName(channelName);
            }
            pointsBalanceJournalResponse.setOrderNo(item.getOrderNo());
            journalResponseList.add(pointsBalanceJournalResponse);
        });
        return new Pagination<>(journalResponseList, pagination.getPageable(), pagination.getTotalElements());
    }

    @Override
    public PointsEarnRuleResponse getEarnRule(PointsEarnRuleQueryRequest request) {
        log.info("根据金额查相应等级：{}", JSONUtil.toJsonStr(request));
        Activity activity = activityDao.getActivity(request.getActivityCode(), request.getCountryId(), request.getChannelCode());
        if (activity == null) {
            throw new ApplicationRuntimeException(BucksCenterError.ACTIVITY_IS_NOT_EXISTS);
        }
        List<PointsEarnRule> list = pointsEarnRuleDao.findListByActivityId(activity.getId());
        BigDecimal ratio = BigDecimal.ONE;
        BigDecimal extraReward = BigDecimal.ZERO;
        PointsEarnRuleResponse pointsEarnRuleResponse = new PointsEarnRuleResponse();
        if (list.size() > 0) {
            List<PointsEarnRule> pointsEarnRuleList = list.stream().sorted(Comparator.comparing(PointsEarnRule::getEarnLimit).reversed()).collect(Collectors.toList());
            for (PointsEarnRule pointsEarnRule : pointsEarnRuleList) {
                ratio = pointsEarnRule.getPoints();
                extraReward = pointsEarnRule.getExtraReward();
                if (pointsEarnRule.getEarnLimit().compareTo(request.getAmount()) <= 0) {
                    break;
                }
            }
        }
        pointsEarnRuleResponse.setRatio(ratio);
        pointsEarnRuleResponse.setExtraReward(extraReward);
        return pointsEarnRuleResponse;
    }

    private Locale getLocale(String lang) {
        Locale locale = ContextUtils.get().getLocale();
        if (locale != null) {
            return locale;
        }
        if (!Strings.isEmpty(lang)) {
            locale = Locale.forLanguageTag(lang);
            if (locale != null) {
                return locale;
            }
        }
        return Locale.ENGLISH;
    }

    private List<ActivityCommonVO> getOtherShop(long countryId, String channelCode) {
        MybatisQuery mybatisQuery = new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(Activity::getEnable).is(1))
                .addCriterion(Criterion.where(Activity::getCountryId).is(countryId))
                .addCriterion(Criterion.where(Activity::getChannelCode).ne(channelCode))
                .addCriterion(Criterion.where(Channel::getType).is(ChannelType.shop.getCode()))
                .join(ActivityCommonVO.class)
                .joinTable(JoinType.INNER, Channel.class)
                .on(Activity::getChannelCode, Channel::getCode).complete()
                .build();
        return activityDao.findByJoin(mybatisQuery);
    }

}
