package net.poweroak.saas.bucksc.model.PO;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.DomainEntity;
import net.poweroak.framework.sqltag.Table;

import java.util.Date;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/30 9:31
 * @description
 */



@Data
@ApiModel("人工后台干预")
@Table(name = "points_intervene")
public class PointsIntervene extends DomainEntity<String> {
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    @ApiModelProperty(value = "日期")
    private Date time;
    @ApiModelProperty(value = "用户id")
    private String uid;
    @ApiModelProperty(value = "流水id")
    private String journalId;

    @ApiModelProperty(value = "原因")
    private String reason;




}
