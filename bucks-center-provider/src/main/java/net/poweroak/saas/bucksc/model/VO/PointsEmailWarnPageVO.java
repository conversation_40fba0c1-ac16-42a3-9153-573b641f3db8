package net.poweroak.saas.bucksc.model.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * Created by zx on 2022/7/6 08:46
 */
@Data
public class PointsEmailWarnPageVO {

    private String id;

    @ApiModelProperty(value = "邮件")
    private String email;

    @ApiModelProperty(value = "用户名称")
    private String name;

    @ApiModelProperty(value = "部门")
    private String department;

    @ApiModelProperty(value = "是否发送")
    private Boolean isSend;

    @ApiModelProperty("创建时间")
    @JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
    private Date creTime;
}
