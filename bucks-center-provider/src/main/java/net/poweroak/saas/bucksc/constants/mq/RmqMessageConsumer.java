package net.poweroak.saas.bucksc.constants.mq;

import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.saas.bucksc.service.IPointsMqService;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * <AUTHOR>
 * @Mailto <EMAIL>
 * @Create 2024/5/9 11:00
 **/
@Slf4j
@Component
public class RmqMessageConsumer {

    @Resource
    private IPointsMqService iPointsMqServiceImpl;

    @Async
    @RabbitHandler
    @RabbitListener(queues = RmqConfig.BUCKS_QUEUE)
    public void onMessageBUS(Message message, Channel channel) throws IOException {
        String mes = new String(message.getBody());
        log.info("使用业务队列，收到消息:{}",mes);
        try {
            iPointsMqServiceImpl.processingMessages(mes);
            // 手动ACK
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), Boolean.TRUE);
        } catch (Exception e) {
            e.printStackTrace();
            // channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);
        }
    }

}
