package net.poweroak.saas.bucksc.model.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.sqltag.TableField;
import net.poweroak.saas.bucksc.model.PO.PointsLevelRule;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2024/6/11
 */
@Data
public class PointsLevelUpgradeVO {
    @ApiModelProperty("升级到的等级下标")
    @TableField(table = PointsLevelRule.class, field = "index")
    private Integer index;
}
