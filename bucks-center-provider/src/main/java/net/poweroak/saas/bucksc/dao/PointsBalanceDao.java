package net.poweroak.saas.bucksc.dao;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.exception.ApplicationRuntimeException;
import net.poweroak.framework.data.mybatis.GenericRepository;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.framework.enums.OrderBy;
import net.poweroak.framework.user.UserInfoForServer;
import net.poweroak.framework.util.DateTimeUtils;
import net.poweroak.framework.util.StringUtils;
import net.poweroak.iso.CountryCode;
import net.poweroak.iso.vo.country.CountryRich;
import net.poweroak.saas.bucksc.BucksCenterError;
import net.poweroak.saas.bucksc.enums.GenType;
import net.poweroak.saas.bucksc.mapper.PointsBalanceMapper;
import net.poweroak.saas.bucksc.model.PO.BlackList;
import net.poweroak.saas.bucksc.model.PO.PointsBalance;
import net.poweroak.saas.bucksc.model.TO.PointsBalanceAddTO;
import net.poweroak.saas.bucksc.model.TO.PointsBalanceEditTO;
import net.poweroak.saas.bucksc.model.TO.PointsBalanceSearchTO;
import net.poweroak.saas.bucksc.model.TO.PointsJournalTO;
import net.poweroak.saas.bucksc.model.VO.*;
import net.poweroak.saas.uc.client.UserClient;
import net.poweroak.saas.uc.request.UcMemberPageRequest;
import net.poweroak.saas.uc.request.UserTO;
import net.poweroak.saas.uc.response.UserVO;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;

/**
 * Created by zx on 2022/5/10 17:00
 */

@Repository
@Slf4j
public class PointsBalanceDao extends GenericRepository<PointsBalance, PointsBalanceMapper, String> {

    private final UserClient userClient;

    @Resource
    private BlackListDao blackListDao;

    @Resource
    @Lazy
    private PointsJournalDao journalDao;

    public PointsBalanceDao(PointsBalanceMapper genericMapper, UserClient userClient) {
        super(genericMapper);
        this.userClient = userClient;
    }

    /**
     * 分页查询积分列表
     *
     * @param request
     * @return
     */
    public UnifyResponse<Pagination<PointsBalanceUservo>> pageList(PointsBalanceSearchTO request) {
        MybatisQuery mybatisQuery = new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(PointsBalance::getEnable).is(true));
//        if (StringUtils.isNotEmpty(request.getUid())) {
//            mybatisQuery.addCriterion(Criterion.where(PointsBalance::getUid).regex(request.getUid()));
//        }
        if (request.getCountryId() != null) {
            mybatisQuery.addCriterion(Criterion.where(PointsBalance::getCountryId).is(request.getCountryId()));
        }

        if (StringUtils.isNotEmpty(request.getEmail())) {
            UserTO userTO = new UserTO();
            userTO.setEmail(request.getEmail());
            UcMemberPageRequest ucMemberPageRequest = new UcMemberPageRequest();
            ucMemberPageRequest.setMember(userTO);
            ucMemberPageRequest.setPage(0);
            ucMemberPageRequest.setSize(100);
            UnifyResponse<Pagination<UserInfoForServer>> response = userClient.list(ucMemberPageRequest);
            if (response.isOK()) {
                List<String> uids = new ArrayList<>();
                response.getData().forEach(a -> {
                    uids.add(a.getUid());
                });
                mybatisQuery.addCriterion(Criterion.where(PointsBalance::getUid).in(uids));
            }
        }


        /*if (StringUtils.isNotEmpty(request.getUname())) {
            UserTO userTO = new UserTO();
            userTO.setNickName(request.getUname());
            List<UserVO> listUnifyResponse = userClient.listAllUser(userTO).getData();
            List<String> uids = new ArrayList<>();
            listUnifyResponse.forEach(a -> {
                uids.add(a.getUid());
            });
            mybatisQuery.addCriterion(Criterion.where(PointsBalance::getUid).in(uids));
        }*/

        // 新增筛选条件
        // 账户注册时间区间筛选
        if (request.getCreTimeStart() != null) {
            mybatisQuery.addCriterion(Criterion.where(PointsBalance::getCreTime).gte(request.getCreTimeStart()));
        }
        if (request.getCreTimeEnd() != null) {
            mybatisQuery.addCriterion(Criterion.where(PointsBalance::getCreTime).lte(request.getCreTimeEnd()));
        }

        // 累计发放区间筛选
        if (request.getEarnMin() != null) {
            mybatisQuery.addCriterion(Criterion.where(PointsBalance::getEarn).gte(request.getEarnMin()));
        }
        if (request.getEarnMax() != null) {
            mybatisQuery.addCriterion(Criterion.where(PointsBalance::getEarn).lte(request.getEarnMax()));
        }

        // 累计消耗区间筛选
        if (request.getSpendMin() != null) {
            mybatisQuery.addCriterion(Criterion.where(PointsBalance::getSpend).gte(request.getSpendMin()));
        }
        if (request.getSpendMax() != null) {
            mybatisQuery.addCriterion(Criterion.where(PointsBalance::getSpend).lte(request.getSpendMax()));
        }

        // 剩余积分区间筛选
        if (request.getBalanceMin() != null) {
            mybatisQuery.addCriterion(Criterion.where(PointsBalance::getBalance).gte(request.getBalanceMin()));
        }
        if (request.getBalanceMax() != null) {
            mybatisQuery.addCriterion(Criterion.where(PointsBalance::getBalance).lte(request.getBalanceMax()));
        }

        mybatisQuery.addCriterion(Criterion.where(PointsBalance::getUid).ne(null));
        mybatisQuery.orderBy(PointsBalance::getModTime, OrderBy.DESC);
        List<String> ids = new ArrayList<>();
        List<PointsBalanceUservo> result = new ArrayList<>();
        // 找uid 终于看懂了一点；
        Pagination<PointsBalance> page = this.find(mybatisQuery, request.getPageNumber(), request.getPageSize());
        if (CollectionUtil.isNotEmpty(page.getContent())) {
            page.getContent().forEach(a -> {
                ids.add(a.getUid());
            });

            UnifyResponse<List<UserInfoForServer>> unifyResponse = userClient.list(ids);
            if (unifyResponse.isOK()) {
                List<UserInfoForServer> list = unifyResponse.getData();
                page.getContent().forEach(c -> {
                    PointsBalanceUservo tem = new PointsBalanceUservo();
                    BeanUtils.copyProperties(c, tem);
                    list.forEach(b -> {
                        if (Objects.equals(b.getUid(), c.getUid())) {
                            tem.setEmail(b.getEmail());
                            tem.setUsername(b.getNickname());
                        }


                    });

                    MybatisQuery mybatisQuery1 = new MybatisQuery();
                    mybatisQuery1.addCriterion(Criterion.where(BlackList::getUid).is(c.getUid()));
                    mybatisQuery1.addCriterion(Criterion.where(BlackList::getEnable).is(1));
                    mybatisQuery1.addCriterion(Criterion.where(BlackList::getCountryId).is(c.getCountryId()));
                    List<BlackList> blackLists = this.blackListDao.find(mybatisQuery1);
                    if (blackLists.size() > 0) {
                        tem.setIsInBlackList(true);
                    } else {
                        tem.setIsInBlackList(false);
                    }
                    this.getAmountByUidAndCountryId(c.getUid(), c.getCountryId(), tem);
                    result.add(tem);
                });

            }
        }

        return new UnifyResponse((new Pagination<>(result, page.getPageable(), page.getTotalElements())));
    }

    /**
     * 根据uid和国家id获取当前用户所花费金额
     *
     * @param tem PointsBalanceUservo 对象，pageList方法专用，用来赋值金额与币种
     */
    public String getAmountByUidAndCountryId(String uid, Long countryId, PointsBalanceUservo tem) {
        PointsJournalTO pointsJournalTO = new PointsJournalTO();
        pointsJournalTO.setUid(uid);
        pointsJournalTO.setCountryId(countryId);
        List<PointsJournalVO> detailList = journalDao.findDetailList(pointsJournalTO);
        if (CollectionUtil.isNotEmpty(detailList)) {
            tem.setAmount(String.valueOf(detailList.stream().map(PointsJournalVO::getAmount).filter(Objects::nonNull).map(BigDecimal::new).reduce(new BigDecimal("0.00"), BigDecimal::add)));
            // 同一个国家下使用的币种相同
            detailList.stream().map(PointsJournalVO::getCurrency).filter(StrUtil::isNotBlank).findFirst().ifPresent(tem::setCurrency);
        } else {
            tem.setAmount("0.00");
        }
        return tem.getAmount();
    }


    /**
     * 用户积分导出
     * @param response
     */
    public void export(PointsBalanceSearchTO request, HttpServletResponse response) {

        // 存储调用分页查询的数据；
        List<PointsBalanceUservo> getAllPointsBalanceList = getAllPointsBalance(request);

        // 存储输出到excel中的数据；
        List<UserPointsExportListVO> userPointsExportListVOList = new ArrayList<>();

        // 将pageList查询出来的每一个对象，转换为excel的vo对象；
        for (PointsBalanceUservo item : getAllPointsBalanceList) {

            // 可以赋值给对象，也可以赋值给class最后返回一个对象；
            UserPointsExportListVO userPointsExportListVO = BeanUtil.copyProperties(item, UserPointsExportListVO.class);

            // 要把这种 注册时间  Tue Nov 21 09:43:42 CST 2023  改为正常的的时间；
            userPointsExportListVO.setCreTime(DateTimeUtils.dateToStr(item.getCreTime(), DatePattern.NORM_DATETIME_PATTERN));
            log.info("看看vo转换后的时间：{}",userPointsExportListVO.getCreTime());

            // 根据国家id查询出国家name；
            String countryNameZn = "";
            boolean flag = true;
            try {
                CountryRich countryRich = CountryCode.getByGeoId(userPointsExportListVO.getCountryId());
                log.info("看看countryRich：{}",countryRich);

//                String countryCode = countryRich.getCountryCode();
//                log.info("看看countryCode：{}",countryCode);
//
//                CountryCode byAlpha2Code = CountryCode.getByAlpha2Code(countryCode);
//                log.info("看看byAlpha2Code：{}",byAlpha2Code);
//
//                countryNameZn = CountryCode.getByAlpha2Code(countryCode).getNameZh();
//                log.info("看看countryNameZn：{}",countryNameZn);

                countryNameZn = CountryCode.getByAlpha2Code(countryRich.getCountryCode()).getNameZh();

            } catch (Exception e) {
                log.error("无效的国家编码ID：{}", userPointsExportListVO.getCountryId());
                flag = false;
            }
            if (flag) {
                userPointsExportListVO.setCountryName(countryNameZn);
            } else {
                userPointsExportListVO.setCountryName("无效的国家编码ID：" + userPointsExportListVO.getCountryId());
            }

            log.info("是否在黑名单：{}", item.getIsInBlackList());
            // 是否在黑名单
            if ("false".equals(userPointsExportListVO.getIsInBlackList())) {
                userPointsExportListVO.setIsInBlackList("否");
            }
            if ("true".equals(userPointsExportListVO.getIsInBlackList())) {
                userPointsExportListVO.setIsInBlackList("是");
            }


            PointsJournalTO pointsJournalTO = new PointsJournalTO();
            pointsJournalTO.setUid(item.getUid());
            pointsJournalTO.setCountryId(item.getCountryId());
            List<PointsJournalVO> detailList = journalDao.findDetailList(pointsJournalTO);
            if (CollectionUtil.isNotEmpty(detailList)) {
                userPointsExportListVO.setAmount(String.valueOf(detailList.stream().map(PointsJournalVO::getAmount).filter(Objects::nonNull).map(BigDecimal::new).reduce(new BigDecimal("0.00"), BigDecimal::add)));
                // 同一个国家下使用的币种相同
                detailList.stream().map(PointsJournalVO::getCurrency).filter(StrUtil::isNotBlank).findFirst().ifPresent(userPointsExportListVO::setCurrency);
                // 统计过期积分
                userPointsExportListVO.setExpired(
                        String.valueOf(detailList.stream()
                                .filter(detail -> StrUtil.isNotBlank(detail.getPoints()) && Objects.equals(detail.getGenType(), GenType.CLEAR.getCode()))
                                .map(PointsJournalVO::getPoints)
                                .mapToInt(Integer::valueOf) // 将String转换为int
                                .sum()));
            } else {
                userPointsExportListVO.setAmount("0.00");
                userPointsExportListVO.setExpired("0");
            }

            // 将对象插入到excel的vo中；
            userPointsExportListVOList.add(userPointsExportListVO);
        }

        // 导出；
        try {
            excelWriteResponse("User-Points-Export", response);
            EasyExcel.write(response.getOutputStream(), UserPointsExportListVO.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("sheet1").doWrite(userPointsExportListVOList);
        } catch (IOException e) {
            log.error("用户积分导出失败", e);
        }

    }


    public List<PointsBalanceUservo> getAllPointsBalance(PointsBalanceSearchTO request) {
        List<PointsBalanceUservo> allPointsBalance = new ArrayList<>();
        int pageNo = 1; // 起始页码
        int pageSize = 50; // 每页大小

        // 设置 起始页码和每页大小 的值；=====分页查询的数据查询了2遍，重复了；
        request.setPage(pageNo);
        request.setSize(pageSize);

        // PointsBalanceSearchTO request 我这个DTO是带有查询条件的，然后带着这些条件去做分页查询；我也可以在这里把DTO中的查询条件设置为空为”“等，让它没有查询条件；
//        request.setCountryId(null);
//        request.setUname("");

        boolean hasMoreData = true;
        while (hasMoreData) {

            UnifyResponse<Pagination<PointsBalanceUservo>> page1 = pageList(request); // 调用分页查询方法
            Pagination<PointsBalanceUservo> page = page1.getData();

            if (page != null && page.getContent() != null && !page.getContent().isEmpty()) {
                allPointsBalance.addAll(page.getContent());
                // pageNo++ 获取下一页数据；pageSize可以不设置的；
                pageNo = pageNo + 1;
                request.setPage(pageNo);
                request.setSize(pageSize);
            } else {
                hasMoreData = false; // 没有更多数据
            }
        }

        return allPointsBalance;
    }


//    private Pagination<PointsBalanceUservo> pageList(int pageNumber, int pageSize) {
//        // 这里放置调用原来分页查询的逻辑，根据 pageNumber 和 pageSize 查询相应的分页数据
//        // 返回一个 Pagination<PointsBalanceUservo> 对象
//        // 注意：此处需要根据实际情况调用原来的分页查询方法，这里仅为示例，实际逻辑需要根据具体情况来实现
//        return null; // 替换成你的实际查询逻辑
//    }


    /**
     * 修改积分
     *
     * @param points
     * @param uid
     * @param countryId
     * @return
     */
    public boolean changeBalance(Integer points, String uid, Long countryId) {

        if (blackListDao.verifyBlack(uid, countryId)) {
            log.error("积分账户被禁止，UID:{}", uid);
            throw new ApplicationRuntimeException(BucksCenterError.POINTS_BALANCE_HAS_BAN);
        }

        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsBalance::getUid).is(uid));
        query.addCriterion(Criterion.where(PointsBalance::getCountryId).is(countryId));
        PointsBalance pointsBalance = this.findOne(query);
        if (pointsBalance == null) {
            PointsBalanceAddTO pointsBalanceAddTO = new PointsBalanceAddTO();
            pointsBalanceAddTO.setCountryId(countryId);
            pointsBalanceAddTO.setUid(uid);
            pointsBalanceAddTO.setBalance(0);
            pointsBalanceAddTO.setEarn(0);
            pointsBalanceAddTO.setSpend(0);
            pointsBalanceAddTO.setPvAmount(BigDecimal.ZERO);
            pointsBalanceAddTO.setShopAmount(BigDecimal.ZERO);
            this.add(pointsBalanceAddTO);
        } else {
            pointsBalance.modify();
            this.updateById(pointsBalance);
        }
        int earnPoints = 0;
        int spendPoints = 0;
        if (points < 0) {
            //负数则为消耗

            spendPoints = -points;
        } else {
            //正数为累计发放

            earnPoints = points;
        }
        return this.getMapper().changeBalance(points, earnPoints, spendPoints, uid, countryId) > 0;
    }


    /**
     * 删除用户
     *
     * @param id
     * @return
     */

    public UnifyResponse<Boolean> deleteRequest(String id) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsBalance::getId).is(id));
        query.addUpdateItem(PointsBalance::getEnable, 0);
        this.update(query);
        return new UnifyResponse<>(Boolean.TRUE);
    }

    /**
     * 积分总额查询
     *
     * @param uid
     * @param countryId
     * @return
     */
    public PointsBalanceTotalVO queryPointsTotal(String uid, Long countryId) {
        return this.getMapper().queryPointsTotal(uid, countryId);
    }


    /**
     * 根据id获取单个用户
     *
     * @param id
     * @return
     */
    public UnifyResponse<PointsBalance> getOne(String id) {

        Optional<PointsBalance> pointsBalance = this.findById(id);
        if (pointsBalance.isPresent()) {
            return new UnifyResponse(pointsBalance.get());
        } else {
            log.error("未查询到来源渠道详情数据，id={}", id);
            throw new ApplicationRuntimeException(BucksCenterError.BALANCE_IS_NOT_EXISTS);
        }

    }

    /**
     * 不分页查询用户
     *
     * @return
     */
    public UnifyResponse<List<PointsBalance>> page() {
        MybatisQuery mybatisQuery = new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(PointsBalance::getEnable).is(true));
        return new UnifyResponse(this.find(mybatisQuery));
    }

    /**
     * 新增用户
     *
     * @param request
     * @return
     */
    public UnifyResponse<Boolean> add(PointsBalanceAddTO request) {
        if (StringUtils.isEmpty(request.getUid())) {
            log.error("用户id为空");
            throw new ApplicationRuntimeException(BucksCenterError.UID_CANNOT_BE_EMPTY);
        }
        if (request.getCountryId() == null) {
            log.error("国家编码为空");
            throw new ApplicationRuntimeException(BucksCenterError.COUNTRY_CANNOT_BE_EMPTY);
        }
        if (StringUtils.isEmpty(request.getBalance())) {
            log.error("余额为空");
            throw new ApplicationRuntimeException(BucksCenterError.BALANCE_CANNOT_BE_EMPTY);
        }

        if (StringUtils.isEmpty(request.getSpend())) {
            log.error("消耗为空");
            throw new ApplicationRuntimeException(BucksCenterError.SPEND_CANNOT_BE_EMPTY);
        }

        if (StringUtils.isEmpty(request.getEarn())) {
            log.error("发放为空");
            throw new ApplicationRuntimeException(BucksCenterError.EARN_CANNOT_BE_EMPTY);
        }

        MybatisQuery mybatisQuery = new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(PointsBalance::getUid).is(request.getUid()));
        mybatisQuery.addCriterion(Criterion.where(PointsBalance::getCountryId).is(request.getCountryId()));
        PointsBalance one = this.findOne(mybatisQuery);
        if (one != null) {
            log.error("相同国家uid已经存在");
            throw new ApplicationRuntimeException(BucksCenterError.UID_ALREADY_EXISTS_IN_SAME_COUNTRY);
        }


        PointsBalance balance = new PointsBalance();
        BeanUtils.copyProperties(request, balance);
        balance.create();
        this.insert(balance);
        return new UnifyResponse<>(Boolean.TRUE);
    }


    /**
     * 根据用户和国家获取用户
     *
     * @param uid
     * @param country
     * @return
     */
    public PointsBalance findByUidAndCountry(String uid, Long country) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsBalance::getEnable).is(1));
        query.addCriterion(Criterion.where(PointsBalance::getUid).is(uid));
        query.addCriterion(Criterion.where(PointsBalance::getCountryId).is(country));
        return this.findOne(query);
    }


    /**
     * 检查用户是否注册了账户
     *
     * @param uid
     * @param country
     * @return
     */
    public boolean checkUserExists(String uid, Long country) {
        PointsBalance pointsBalance = this.findByUidAndCountry(uid, country);
        if (pointsBalance != null) {
            return true;
        }
        if (this.findRegUserByUid(uid)) {
            //新增用户
            PointsBalanceAddTO pointsBalanceAddTO = new PointsBalanceAddTO();
            pointsBalanceAddTO.setUid(uid);
            pointsBalanceAddTO.setSpend(0);
            pointsBalanceAddTO.setEarn(0);
            pointsBalanceAddTO.setBalance(0);
            pointsBalanceAddTO.setCountryId(country);
            this.add(pointsBalanceAddTO);
            return true;
        }
        log.error("根据UID无法获取用户信息,UID:{}", uid);
        throw new ApplicationRuntimeException(BucksCenterError.BALANCE_IS_NOT_EXISTS);
    }

    /**
     * 获取注册用户信息
     *
     * @param uid
     * @return
     */
    public boolean findRegUserByUid(String uid) {
        UnifyResponse<List<UserInfoForServer>> unifyResponse = userClient.list(Arrays.asList(uid));
        if (unifyResponse.isOK()) {
            List<UserInfoForServer> list = unifyResponse.getData();
            if (!CollectionUtil.isEmpty(list)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 更新用户
     *
     * @param request
     * @return
     */
    public UnifyResponse<Boolean> updatePointsBalance(PointsBalanceEditTO request) {
        PointsBalance balance = new PointsBalance();
        BeanUtils.copyProperties(request, balance);
        balance.setBalance(Integer.parseInt(request.getBalance().toString()));
        balance.setSpend(Integer.parseInt(request.getSpend().toString()));
        balance.setEarn(Integer.parseInt(request.getEarn().toString()));

        this.updateById(balance);
        return new UnifyResponse<>(Boolean.TRUE);

    }


    protected void excelWriteResponse(String fileName, HttpServletResponse response) throws UnsupportedEncodingException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", StrUtil.concat(Boolean.TRUE, "attachment;filename=", fileName, ExcelTypeEnum.XLSX.getValue()));
    }


    // 查询积分余额表
    public List<PointsExportVO> UserPointsExport() {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsBalance::getEnable).is(1))
                .join(PointsExportVO.class);
        return this.findByJoin(query);
    }


    // 查询在黑名单表中的uid
    public List<BlackListUidVO> BlackListUid() {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(BlackList::getEnable).is(1))
                .join(BlackListUidVO.class);
        return blackListDao.findByJoin(query);
    }


    public long getUserNumByCountry(Long countryId) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsBalance::getEnable).is(1))
                .addCriterion(Criterion.where(PointsBalance::getCountryId).is(countryId));
        return this.count(query);
    }



}
