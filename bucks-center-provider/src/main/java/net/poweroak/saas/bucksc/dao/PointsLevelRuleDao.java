package net.poweroak.saas.bucksc.dao;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.data.mybatis.GenericRepository;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.saas.bucksc.mapper.PointsLevelRuleMapper;
import net.poweroak.saas.bucksc.model.PO.PointsLevelRule;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2024/4/30
 */
@Slf4j
@Repository
public class PointsLevelRuleDao extends GenericRepository<PointsLevelRule, PointsLevelRuleMapper, String> {
    public PointsLevelRuleDao(PointsLevelRuleMapper genericMapper) {
        super(genericMapper);
    }

    public List<PointsLevelRule> byLevelId(String levelId) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsLevelRule::getEnable).is(1))
                .addCriterion(Criterion.where(PointsLevelRule::getPointsLevelId).is(levelId));
        return this.find(query);
    }

    public void deleteByLevelId(String levelId) {
        if (StrUtil.isBlank(levelId)) {
            return;
        }
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsLevelRule::getPointsLevelId).is(levelId));
        this.delete(query);
    }

    /**
     *
     */
    public List<PointsLevelRule> byAmount(BigDecimal amount, String levelId) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsLevelRule::getEnable).is(1))
                .addCriterion(Criterion.where(PointsLevelRule::getConsumptionThreshold).lte(amount))
                .addCriterion(Criterion.where(PointsLevelRule::getPointsLevelId).is(levelId));
        return this.find(query);
    }
}
