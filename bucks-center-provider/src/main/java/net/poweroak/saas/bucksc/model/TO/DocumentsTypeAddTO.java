package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/7/21 10:22
 * @description
 */
@Data
@ApiModel("新增文案类型")
public class DocumentsTypeAddTO {
    /**
     * 文案名称
     */
    @ApiModelProperty(value = "文案名称")
    private String name;

    /**
     * 文案代码
     */
    @ApiModelProperty(value = "文案代码")
    private String code;
}
