package net.poweroak.saas.bucksc.dao;


import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.data.page.PageInfo;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.exception.ApplicationRuntimeException;
import net.poweroak.framework.cache.CacheUtils;
import net.poweroak.framework.data.core.idgen.CharID;
import net.poweroak.framework.data.mybatis.GenericRepository;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.framework.data.mybatis.query.join.JoinType;
import net.poweroak.framework.enums.OrderBy;
import net.poweroak.saas.bucksc.BucksCenterError;
import net.poweroak.saas.bucksc.common.ActivityEarnCode;
import net.poweroak.saas.bucksc.constants.RedisConstant;
import net.poweroak.saas.bucksc.enums.EarnTimesType;
import net.poweroak.saas.bucksc.enums.WeekCode;
import net.poweroak.saas.bucksc.mapper.PointsEarnRuleMapper;
import net.poweroak.saas.bucksc.model.PO.Activity;
import net.poweroak.saas.bucksc.model.PO.Channel;
import net.poweroak.saas.bucksc.model.PO.PointsEarnRule;
import net.poweroak.saas.bucksc.model.PO.PointsJournal;
import net.poweroak.saas.bucksc.model.TO.PointsEarnRuleListTO;
import net.poweroak.saas.bucksc.model.TO.PointsEarnRuleSaveTO;
import net.poweroak.saas.bucksc.model.TO.PointsEarnRuleTO;
import net.poweroak.saas.bucksc.model.VO.PointsEarnRulePageVO;
import net.poweroak.saas.bucksc.model.VO.PointsEarnRuleVO;
import net.poweroak.saas.bucksc.response.SignDaysResponse;
import net.poweroak.saas.bucksc.utils.LocalDateUtil;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2022/5/7 9:15
 */
@Slf4j
@Repository
public class PointsEarnRuleDao extends GenericRepository<PointsEarnRule, PointsEarnRuleMapper, String> {

    private final ActivityDao activityDao;
    private final ChannelDao channelDao;
    private final RequestLogDao requestLogDao;
    private final PointsJournalDao pointsJournalDao;
    private final PointsBalanceDao pointsBalanceDao;

    public PointsEarnRuleDao(@Autowired PointsEarnRuleMapper pointsEarnRuleMapper, ActivityDao activityDao, ChannelDao channelDao, PointsJournalDao pointsJournalDao, PointsBalanceDao pointsBalanceDao, RequestLogDao requestLogDao) {
        super(pointsEarnRuleMapper);
        this.activityDao = activityDao;
        this.channelDao = channelDao;
        this.pointsJournalDao = pointsJournalDao;
        this.pointsBalanceDao = pointsBalanceDao;
        this.requestLogDao = requestLogDao;
    }


    /**
     * 签到详情列表
     *
     * @return
     */
    public List<PointsEarnRulePageVO> getSignList(String id) {
        Optional<PointsEarnRule> pointsEarnRuleOptional = this.findById(id);
        List<PointsEarnRulePageVO> listVOList = new ArrayList<>();

        if (pointsEarnRuleOptional.isPresent()) {
            PointsEarnRule pointsEarnRule = pointsEarnRuleOptional.get();
            MybatisQuery query = new MybatisQuery();
            query.addCriterion(Criterion.where(PointsEarnRule::getRuleCode).is(pointsEarnRule.getRuleCode()));
//            query.addCriterion(Criterion.where(PointsEarnRule::getRuleType).is(EarnRuleType.SIGN.getCode()));
//            query.addCriterion(Criterion.where(PointsEarnRule::getChannelCode).is(pointsEarnRule.getChannelCode()));
//            query.addCriterion(Criterion.where(PointsEarnRule::getActivityId).is(pointsEarnRule.getActivityId()));
            query.addCriterion(Criterion.where(PointsEarnRule::getEnable).is(1));
            query.orderBy(PointsEarnRule::getMemberLevel, OrderBy.ASC);
            List<PointsEarnRule> list = this.find(query);
            list.forEach(item -> {
                PointsEarnRulePageVO pointsEarnRuleListVO = new PointsEarnRulePageVO();
                BeanUtils.copyProperties(item, pointsEarnRuleListVO);
                pointsEarnRuleListVO.setId(item.getId());
                listVOList.add(pointsEarnRuleListVO);
            });
        }
        return listVOList;
    }


    public List<PointsEarnRulePageVO> getListByActivityId(PointsEarnRuleListTO pointsEarnRuleListTO) {
        List<PointsEarnRulePageVO> listVOList = new ArrayList<>();
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsEarnRule::getActivityId).is(pointsEarnRuleListTO.getActivityId()));
        query.addCriterion(Criterion.where(PointsEarnRule::getEnable).is(1));
        query.orderBy(PointsEarnRule::getEarnLimit, OrderBy.ASC);

        List<PointsEarnRule> list = this.find(query);
        list.forEach(item -> {
            PointsEarnRulePageVO pointsEarnRuleListVO = new PointsEarnRulePageVO();
            BeanUtils.copyProperties(item, pointsEarnRuleListVO);
            pointsEarnRuleListVO.setId(item.getId());
            listVOList.add(pointsEarnRuleListVO);
        });
        return listVOList;
    }

    /**
     * 签到规则列表
     *
     * @param pointsEarnRuleTO
     * @return
     */
    public Pagination<PointsEarnRulePageVO> getSignPage(PointsEarnRuleTO pointsEarnRuleTO) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsEarnRule::getRuleType).is(pointsEarnRuleTO.getRuleType()));
        query.addCriterion(Criterion.where(PointsEarnRule::getEnable).is(1));
        if (!Strings.isEmpty(pointsEarnRuleTO.getActivityId())) {
            query.addCriterion(Criterion.where(PointsEarnRule::getActivityId).is(pointsEarnRuleTO.getActivityId()));
        }
        if (!Strings.isEmpty(pointsEarnRuleTO.getChannelCode())) {
            query.addCriterion(Criterion.where(PointsEarnRule::getChannelCode).is(pointsEarnRuleTO.getChannelCode()));
        }
        List<PointsEarnRule> list = this.find(query);

        List<String> idList = new ArrayList<>();
        List<String> ruleCodeList = new ArrayList<>();
        list.forEach(item -> {
            if (!ruleCodeList.contains(item.getRuleCode())) {
                idList.add(item.getId());
                ruleCodeList.add(item.getRuleCode());
            }
        });

        query.reset();
        query.addCriterion(Criterion.where(PointsEarnRule::getId).in(idList));
        if (pointsEarnRuleTO.getCountryId() != null) {
            query.addCriterion(Criterion.where(Activity::getCountryId).is(pointsEarnRuleTO.getCountryId()));
        }
        query.join(PointsEarnRuleVO.class)
                .joinTable(JoinType.INNER, Activity.class)
                .on(PointsEarnRule::getActivityId, Activity::getId)
                .complete().build();
        PageInfo pageInfo = new PageInfo(pointsEarnRuleTO.getPageNumber(), pointsEarnRuleTO.getPageSize());
        //Pagination<PointsEarnRuleVO> pagination = this.find(query, pointsEarnRuleTO.getPageNumber(), pointsEarnRuleTO.getPageSize());
        Pagination<PointsEarnRuleVO> pagination = this.findByJoin(query, pageInfo);
        return new Pagination<>(getRulePageVo(pagination.getContent()), pagination.getPageable(), pagination.getTotalElements());
    }

    /**
     * 列表
     *
     * @return
     */
    public Pagination<PointsEarnRulePageVO> getPage(PointsEarnRuleTO pointsEarnRuleTO) {
        MybatisQuery mybatisQuery = new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(PointsEarnRule::getRuleType).is(pointsEarnRuleTO.getRuleType()));
        if (!Strings.isEmpty(pointsEarnRuleTO.getActivityId())) {
            mybatisQuery.addCriterion(Criterion.where(PointsEarnRule::getActivityId).is(pointsEarnRuleTO.getActivityId()));
        }
        if (!Strings.isEmpty(pointsEarnRuleTO.getChannelCode())) {
            mybatisQuery.addCriterion(Criterion.where(PointsEarnRule::getChannelCode).is(pointsEarnRuleTO.getChannelCode()));
        }
        if (pointsEarnRuleTO.getCountryId() != null) {
            mybatisQuery.addCriterion(Criterion.where(Activity::getCountryId).is(pointsEarnRuleTO.getCountryId()));
        }
        mybatisQuery.addCriterion(Criterion.where(PointsEarnRule::getEnable).is(1));
        mybatisQuery.join(PointsEarnRuleVO.class)
                .joinTable(JoinType.INNER, Activity.class)
                .on(PointsEarnRule::getActivityId, Activity::getId)
                .complete().build();
        PageInfo pageInfo = new PageInfo(pointsEarnRuleTO.getPageNumber(), pointsEarnRuleTO.getPageSize());
        //pointsEarnRuleTO.getPageNumber(), pointsEarnRuleTO.getPageSize()
        Pagination<PointsEarnRuleVO> pagination = this.findByJoin(mybatisQuery, pageInfo);
        return new Pagination<>(getRulePageVo(pagination.getContent()), pagination.getPageable(), pagination.getTotalElements());
    }


    /**
     * 规则列表
     *
     * @param list
     * @return
     */
    private List<PointsEarnRulePageVO> getRulePageVo(List<PointsEarnRuleVO> list) {
        List<PointsEarnRulePageVO> listVOList = new ArrayList<>(list.size());
        list.forEach(item -> {
            PointsEarnRulePageVO pointsEarnRuleListVO = new PointsEarnRulePageVO();
            BeanUtils.copyProperties(item, pointsEarnRuleListVO);
            pointsEarnRuleListVO.setId(item.getId());
            if (!Strings.isEmpty(item.getActivityId())) {
                Optional<Activity> optionalActivity = activityDao.findById(item.getActivityId());
                if (optionalActivity.isPresent()) {
                    pointsEarnRuleListVO.setActivityName(optionalActivity.get().getName());
                    pointsEarnRuleListVO.setCountry(optionalActivity.get().getCountryId());
                }
            }
            if (!Strings.isEmpty(item.getChannelCode())) {
                MybatisQuery mybatisQuery = new MybatisQuery();
                mybatisQuery.addCriterion(Criterion.where(Channel::getCode).is(item.getChannelCode()));
                Channel channel = channelDao.findOne(mybatisQuery);
                if (channel != null) {
                    pointsEarnRuleListVO.setChannelName(channel.getName());
                }
            }
            if (item.getRuleType() != null) {
                pointsEarnRuleListVO.setRuleTypeName(ActivityEarnCode.getNameByRule(item.getRuleType()));
            }
            listVOList.add(pointsEarnRuleListVO);
        });
        return listVOList;
    }

    /**
     * 批量保存
     *
     * @param list
     * @return
     */
    public boolean saveList(List<PointsEarnRuleSaveTO> list, Integer ruleType) {
        if (list.size() == 0) {
            throw new ApplicationRuntimeException(BucksCenterError.DATA_NOT_EXISTS);
        }

        this.checkData(list, ruleType);
        list.forEach(this::save);
//        String ruleCode = new CharID().genId();
//        list.forEach(item -> {
//            item.setRuleCode(ruleCode);
//            this.save(item);
//        });
        return true;
    }

    /**
     * 保存
     *
     * @param pointsEarnRuleSaveTO
     * @return
     */
    public boolean save(PointsEarnRuleSaveTO pointsEarnRuleSaveTO) {
        Optional<Activity> optionalActivity = activityDao.findById(pointsEarnRuleSaveTO.getActivityId());
        optionalActivity.ifPresent(activity -> pointsEarnRuleSaveTO.setChannelCode(activity.getChannelCode()));
//        MybatisQuery mybatisQuery = new MybatisQuery();
//        mybatisQuery.addCriterion(Criterion.where(PointsEarnRule::getActivityId).is(pointsEarnRuleSaveTO.getActivityId()));
//        mybatisQuery.addCriterion(Criterion.where(PointsEarnRule::getEnable).is(1));
//        //签到和货币兑换，允许同活动不同数值的数据
//        if (ActivityEarnCode.CURRENCY_EXCHANGE.getRuleType().equals(pointsEarnRuleSaveTO.getRuleType()) || ActivityEarnCode.SIGN.getRuleType().equals(pointsEarnRuleSaveTO.getRuleType())) {
//            mybatisQuery.addCriterion(Criterion.where(PointsEarnRule::getEarnLimit).is(pointsEarnRuleSaveTO.getEarnLimit()));
//        }
//
//        //光伏同一活动 兑换门槛和兑换比例不能相同
//        if (ActivityEarnCode.PV_POWER.getRuleType().equals(pointsEarnRuleSaveTO.getRuleType())) {
//            mybatisQuery.addCriterion(Criterion.where(PointsEarnRule::getEarnLimit).is(pointsEarnRuleSaveTO.getEarnLimit()));
//            mybatisQuery.addCriterion(Criterion.where(PointsEarnRule::getPoints).is(pointsEarnRuleSaveTO.getPoints()));
//        } else {
//            mybatisQuery.addCriterion(Criterion.where(PointsEarnRule::getEarnLimit).is(pointsEarnRuleSaveTO.getEarnLimit()));
//        }
//
//
//        if (!Strings.isEmpty(pointsEarnRuleSaveTO.getId())) {
//            mybatisQuery.addCriterion(Criterion.where(PointsEarnRule::getId).ne(pointsEarnRuleSaveTO.getId()));
//        }
//        PointsEarnRule pointsEarnRuleExists = this.findOne(mybatisQuery);
//        if (pointsEarnRuleExists != null) {
//            throw new ApplicationRuntimeException(BucksCenterError.EARN_RULE_ACTIVITY_HAS_EXISTS);
//        }
        if (pointsEarnRuleSaveTO.getMemberLevel() == null) {
            pointsEarnRuleSaveTO.setMemberLevel(0);
        }
        if (Strings.isEmpty(pointsEarnRuleSaveTO.getId())) {
            PointsEarnRule pointsEarnRule = new PointsEarnRule();
            BeanUtils.copyProperties(pointsEarnRuleSaveTO, pointsEarnRule);
            pointsEarnRule.create();
            pointsEarnRule.setId(new CharID().genId());
            this.insert(pointsEarnRule);
            return true;
        } else {
            Optional<PointsEarnRule> pointsEarnRuleOptional = this.findById(pointsEarnRuleSaveTO.getId());
            if (pointsEarnRuleOptional.isPresent()) {
                PointsEarnRule pointsEarnRuleUpdate = new PointsEarnRule();
                pointsEarnRuleUpdate.modify();
                BeanUtils.copyProperties(pointsEarnRuleSaveTO, pointsEarnRuleUpdate);
                this.updateById(pointsEarnRuleUpdate);
                return true;
            }
        }
        return false;
    }


    /**
     * 数据检查
     *
     * @param list
     * @param ruleType
     */
    private void checkData(List<PointsEarnRuleSaveTO> list, Integer ruleType) {
        if (ActivityEarnCode.CURRENCY_EXCHANGE.getRuleType().equals(ruleType)) {
            List<PointsEarnRuleSaveTO> saveTOList = list.stream().sorted(Comparator.comparing(PointsEarnRuleSaveTO::getEarnLimit)).collect(Collectors.toList());
            List<BigDecimal> earnLimitList = new ArrayList<>();
            for (PointsEarnRuleSaveTO pointsEarnRuleSaveTO : saveTOList) {
                if (earnLimitList.contains(pointsEarnRuleSaveTO.getEarnLimit())) {
                    throw new ApplicationRuntimeException(BucksCenterError.AMOUNT_AND_LEVEL_SORT_ERR);
                }
//                if (pointsEarnRuleSaveTO.getMemberLevel() <= memberLevel) {
//                    throw new ApplicationRuntimeException(BucksCenterError.LEVEL_CANT_NOT_SAME);
//                }
                earnLimitList.add(pointsEarnRuleSaveTO.getEarnLimit());
            }
        } else if (ActivityEarnCode.PV_POWER.getRuleType().equals(ruleType)) {
            List<BigDecimal> rateList = new ArrayList<>();
            List<BigDecimal> earnLimitList = new ArrayList<>();
            for (PointsEarnRuleSaveTO pointsEarnRuleSaveTO : list) {
                if (earnLimitList.contains(pointsEarnRuleSaveTO.getEarnLimit())) {
                    throw new ApplicationRuntimeException(BucksCenterError.EARN_LIMIT_ERR);
                }
                if (rateList.contains(pointsEarnRuleSaveTO.getPoints())) {
                    throw new ApplicationRuntimeException(BucksCenterError.RATE_LIMIT_ERR);
                }
                rateList.add(pointsEarnRuleSaveTO.getPoints());
                earnLimitList.add(pointsEarnRuleSaveTO.getEarnLimit());
            }
        } else if (ActivityEarnCode.COMMON.getRuleType().equals(ruleType)) {
            List<BigDecimal> earnLimitList = new ArrayList<>();
            for (PointsEarnRuleSaveTO pointsEarnRuleSaveTO : list) {
                if (earnLimitList.contains(pointsEarnRuleSaveTO.getEarnLimit())) {
                    throw new ApplicationRuntimeException(BucksCenterError.EARN_LIMIT_ERR);
                }
                earnLimitList.add(pointsEarnRuleSaveTO.getEarnLimit());
            }
        }
    }


    /**
     * 删除
     *
     * @param id
     */
    public void delete(String id) {
        Optional<PointsEarnRule> pointsEarnRuleOptional = this.findById(id);
        if (pointsEarnRuleOptional.isPresent()) {
            PointsEarnRule pointsEarnRuleNew = new PointsEarnRule();
            pointsEarnRuleNew.setEnable(0);
            pointsEarnRuleNew.setId(id);
            this.updateById(pointsEarnRuleNew);
//            List<String> list = new ArrayList<>();
//            MybatisQuery query = new MybatisQuery();
//            query.addCriterion(Criterion.where(PointsEarnRule::getActivityId).is(pointsEarnRule.getActivityId()));
//            query.addCriterion(Criterion.where(PointsEarnRule::getChannelCode).is(pointsEarnRule.getChannelCode()));
//            List<PointsEarnRule> pointsEarnRuleList = this.find(query);
//            if (!CollectionUtil.isEmpty(pointsEarnRuleList)) {
//                list = pointsEarnRuleList.stream().map(PointsEarnRule::getId).collect(Collectors.toList());
//            } else {
//                list.add(id);
//            }
//            list.forEach(item -> {
//                PointsEarnRule pointsEarnRuleNew = new PointsEarnRule();
//                pointsEarnRuleNew.setEnable(0);
//                pointsEarnRuleNew.setId(item);
//                this.updateById(pointsEarnRuleNew);
//            });
        }
    }


    /**
     * 检查规则是否满足
     *
     * @param uid
     * @param countryId
     * @param pointsEarnRule
     * @return
     */
    public boolean checkEarnRuleCondition(String uid, Long countryId, PointsEarnRule pointsEarnRule) {

        int times = this.getEarnTimes(pointsEarnRule.getId(), countryId, uid);
        //一次性
        if (EarnTimesType.ONE.getCode().equals(pointsEarnRule.getEarnType())) {
            // long times = pointsJournalDao.getEarnTimes(uid, pointsEarnRule.getActivityId(), null, null);
            return times < 1;
        }

        if (EarnTimesType.INFINITE.getCode().equals(pointsEarnRule.getEarnType())) {
            //是否达到每天限制次数
            //DateTime endTime = DateTime.now();
            //long times = pointsJournalDao.getEarnTimes(uid, pointsEarnRule.getActivityId(), DateUtil.beginOfDay(endTime), endTime);
            return pointsEarnRule.getDailyNum() > times;
        }
        return false;
    }


    /**
     * 重置
     *
     * @param countryId
     * @param channelCode
     * @param uid
     * @param date
     * @return
     */
    public void resetSignDataToRedis(Long countryId, String channelCode, String uid, DateTime date) {
        List<Activity> activityList = activityDao.findByEarnType(countryId, channelCode, ActivityEarnCode.SIGN.getRuleType());
        if (activityList.size() == 0) {
            return;
        }
        List<String> actIdList = activityList.stream().map(Activity::getId).collect(Collectors.toList());
        String month = DateUtil.format(date, DatePattern.NORM_MONTH_FORMAT);
        List<PointsJournal> journalList = pointsJournalDao.getMonthSignList(uid, countryId, month, actIdList);
        List<Integer> daysList = new ArrayList<>(journalList.size());
        journalList.forEach(item -> {
            DateTime dateTime = LocalDateUtil.getLocalDateTime(countryId, item.getGenTime());
            int days = dateTime.dayOfMonth();
            if (!daysList.contains(days)) {
                SignDaysResponse signDaysResponse = new SignDaysResponse();
                signDaysResponse.setDay(days);
                signDaysResponse.setDate(DateUtil.format(item.getGenTime(), DatePattern.NORM_DATE_FORMATTER));
                daysList.add(days);
                setSignToRedis(countryId, channelCode, uid, dateTime);
            }
        });
    }


    /**
     * 从redis取发放次数
     *
     * @param pointsRulesId
     * @param uid
     * @return
     */
    public int getEarnTimes(String pointsRulesId, Long countryId, String uid) {
        String redisKey = RedisConstant.ERAN_TIMES + pointsRulesId + "_" + countryId + "_" + uid;
        Object timesObj = CacheUtils.get(redisKey);
        if (timesObj == null) {
            return 0;
        }
        return Integer.parseInt(timesObj.toString());
    }

    /**
     * redis存入发放次数
     *
     * @param pointsRulesId
     * @param uid
     */
    public void setEarnTimes(String pointsRulesId, Long countryId, String uid, boolean isForever) {
        try {
            String redisKey = RedisConstant.ERAN_TIMES + pointsRulesId + "_" + countryId + "_" + uid;
            if (isForever) {
                CacheUtils.set(redisKey, 1);
            } else {
                int times = this.getEarnTimes(pointsRulesId, countryId, uid);
                long time = DateUtil.endOfDay(new Date()).getTime() - System.currentTimeMillis();
                CacheUtils.set(redisKey, times + 1, time, TimeUnit.MILLISECONDS);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 额外奖励
     *
     * @param pointsRulesId
     * @param uid
     */
    public void setExtraRewardTimes(String pointsRulesId, Long countryId, String uid, int points) {
        try {
            String redisKey = RedisConstant.ERAN_EXTRA + pointsRulesId + "_" + countryId + "_" + uid;
            CacheUtils.set(redisKey, points);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 额外奖励
     *
     * @param pointsRulesId
     * @param uid
     */
    public boolean hasExtraReward(String pointsRulesId, String countryId, String uid) {
        String redisKey = RedisConstant.ERAN_EXTRA + pointsRulesId + "_" + countryId + "_" + uid;
        Object obj = CacheUtils.get(redisKey);
        return obj == null;
    }

    /**
     * 签到信息存入redis
     */
    public void setSignToRedis(Long countryId, String channelCode, String uid, DateTime date) {
        try {
            String redisKey = getSignKey(countryId, channelCode, uid, date);
            log.debug("date,{}", date);
            log.debug("setSignToRedis dayOfWeek,{}", date.dayOfWeek());
            String dateKey = WeekCode.getCodeByOriCode(date.dayOfWeek()) + "";
            CacheUtils.setHash(redisKey, dateKey + "", DateUtil.format(date, DatePattern.NORM_DATETIME_PATTERN));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 获取签到的KEY
     *
     * @param countryId
     * @param channelCode
     * @param uid
     * @param date
     * @return
     */
    public String getSignKey(Long countryId, String channelCode, String uid, DateTime date) {
        return RedisConstant.ERAN_SIGN + countryId + "_" + channelCode + ":" + uid + ":" + DateUtil.format(date.toLocalDateTime(), DatePattern.NORM_YEAR_PATTERN) + ":" + date.weekOfYear();
    }

    /**
     * 签到信息存入redis
     */
    public Map<String, String> getSignDate(Long countryId, String channelCode, String uid, DateTime date) {
        String redisKey = getSignKey(countryId, channelCode, uid, date);
        Map<String, Integer> map = CacheUtils.getHash(redisKey);
        if (map == null || map.size() == 0) {
            this.resetSignDataToRedis(countryId, channelCode, uid, date);
        }
        return CacheUtils.getHash(redisKey);
    }


    /**
     * 获取发放类型
     *
     * @param activityId
     * @return
     */
    public PointsEarnRule findSignRule(String activityId, String channelCode, Integer days) {

        MybatisQuery query = MybatisQuery.where()
                .addCriterion(Criterion.where(PointsEarnRule::getActivityId).is(activityId))
                .addCriterion(Criterion.where(PointsEarnRule::getChannelCode).is(channelCode))
                .addCriterion(Criterion.where(PointsEarnRule::getRuleType).is(ActivityEarnCode.SIGN.getRuleType()))
                .addCriterion(Criterion.where(PointsEarnRule::getEnable).is(1));
        if (days != null && days > 0) {
            query.addCriterion(Criterion.where(PointsEarnRule::getEarnLimit).lte(days));
            query.orderBy(PointsEarnRule::getEarnLimit, OrderBy.DESC);
        } else {
            query.orderBy(PointsEarnRule::getEarnLimit, OrderBy.ASC);
        }
        return this.findOne(query);
    }


    /**
     * 获取发放类型
     *
     * @param activityId
     * @return
     */
    public List<PointsEarnRule> findSignRuleList(String activityId, String channelCode) {
        return this.find(MybatisQuery.where()
                .addCriterion(Criterion.where(PointsEarnRule::getActivityId).is(activityId))
                .addCriterion(Criterion.where(PointsEarnRule::getChannelCode).is(channelCode))
                .addCriterion(Criterion.where(PointsEarnRule::getRuleType).is(ActivityEarnCode.SIGN.getRuleType()))
                .addCriterion(Criterion.where(PointsEarnRule::getEnable).is(1)));
    }

    /**
     * 获取发放类型
     *
     * @param activityId
     * @return
     */
    public List<PointsEarnRule> findListByActivityId(String activityId) {
        return this.find(MybatisQuery.where()
                .addCriterion(Criterion.where(PointsEarnRule::getActivityId).is(activityId))
                .addCriterion(Criterion.where(PointsEarnRule::getEnable).is(1))
                .orderBy(PointsEarnRule::getMemberLevel, OrderBy.DESC)
        );
    }
}
