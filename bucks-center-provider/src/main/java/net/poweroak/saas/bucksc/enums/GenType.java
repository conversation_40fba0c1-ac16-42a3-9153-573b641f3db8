package net.poweroak.saas.bucksc.enums;

import lombok.Getter;
import net.poweroak.framework.api.AppMessage;
import net.poweroak.framework.api.util.ApplicationUtil;
import net.poweroak.framework.app.MicroServiceApplication;
import net.poweroak.framework.i18n.Internationalization;
import net.poweroak.framework.interfaces.MessageID;
import net.poweroak.saas.bucksc.BucksCenterApp;

/**
 * Created by zx on 2022/5/12 16:51 积分变动类型
 */
@Internationalization(app = MicroServiceApplication.bluBucksCenter)
@Getter
public enum GenType implements AppMessage, MessageID {

    EARN(1, "增加", BucksCenterApp.I18N_CODE_PREFIX + "gentype.add_bucks", "181ae78487af79115229d15490b"),
    SPEND(2, "消耗", BucksCenterApp.I18N_CODE_PREFIX + "gentype.spend_bucks", "181ae5571e9f79115229d1548f8"),
    BACK(3, "回退", BucksCenterApp.I18N_CODE_PREFIX + "gentype.back_bucks", "181ae78144df79115229d154906"),
    CLEAR(4, "回收清零", BucksCenterApp.I18N_CODE_PREFIX + "gentype.clear_bucks", "181ae785f58f79115229d154911");

    private int statusCode;
    private int code;
    private String messageZhCn;
    private String i18nCode;
    private String messageId;


    GenType(int statusCode, String messageZhCn, String i18nCode, String messageId) {
        this.statusCode = statusCode;
        this.messageZhCn = messageZhCn;
        this.messageId = messageId;
        this.i18nCode = i18nCode;
    }

    private static final int START_ERROR_CODE = 20112110;

    public static GenType match(Integer status) {
        for (GenType genType : GenType.values()) {
            if (genType.statusCode == status) {
                return genType;
            }
        }
        return null;
    }

    /**
     * 获取消息错误码
     *
     * @return
     */
    public int getCode() {
        this.code = this.ordinal() + START_ERROR_CODE;
        return this.code;
    }

    /**
     * 获取消息的国际化Code
     *
     * @return
     */
    @Override
    public String getI18nCode() {
        return this.i18nCode;
    }

    /**
     * 获取消息的默认本地化文本消息
     *
     * @return
     */
    @Override
    public String getMessage() {
        return this.messageZhCn;
    }

    /**
     * 获取应用服务标识
     *
     * @return
     */
    @Override
    public String getAppId() {
        return ApplicationUtil.getApplicationNameReal();
    }

    @Override
    public String getMsgId() {
        return this.getMessageId();
    }

    @Override
    public int getMsgCode() {
        return this.getCode();
    }

    /**
     * 获取消息定义的全局唯一ID
     *
     * @return
     */
    public String getMessageId() {
        return this.messageId;
    }
}
