package net.poweroak.saas.bucksc.model.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Created by zx on 2022/5/31 11:46
 */
@Data
public class PointsAbnDataRulePageVO {

    private String id;

    @ApiModelProperty(value = "规则类型(1-自定义 2-积分基础规则)")
    private Integer ruleType;

    @ApiModelProperty(value = "适用的积分消耗规则：1-积分发放；2-积分回退")
    private Integer abnType;

    @ApiModelProperty(value = "时间单位 1-分钟 2-小时  3-天 ")
    private Integer timeUnit;

    @ApiModelProperty(value = "单位时间次数")
    private Integer unitTimeCount;

    @ApiModelProperty("订单创建时间")
    @JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
    private Date creTime;

    @ApiModelProperty("状态")
    private Integer enable;
}
