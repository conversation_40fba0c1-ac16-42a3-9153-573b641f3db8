package net.poweroak.saas.bucksc.model.VO;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/17 10:00
 * @description
 */
@Data
public class journalDayVO {

    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    @ApiModelProperty(value = "日期")
    private Date days;
    @ApiModelProperty(value = "渠道名称")
    private String channelName;
    @ApiModelProperty(value = "活动名称")
    private String activityName;
    @ApiModelProperty(value = "发放积分")
    private BigDecimal type1Points;
    @ApiModelProperty(value = "发放积分人数")
    private Integer type1Num;
    @ApiModelProperty(value = "消耗积分")
    private BigDecimal type2Points;
    @ApiModelProperty(value = "消耗积分人数")
    private Integer type2Num;
    @ApiModelProperty(value = "回退积分")
    private BigDecimal type3Points;
    @ApiModelProperty(value = "回退积分人数")
    private Integer type3Num;
    @ApiModelProperty(value = "消耗率")
    private BigDecimal ratio;


}
