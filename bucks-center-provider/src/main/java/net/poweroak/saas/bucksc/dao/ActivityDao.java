package net.poweroak.saas.bucksc.dao;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.exception.ApplicationRuntimeException;
import net.poweroak.framework.data.mybatis.GenericRepository;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.framework.data.mybatis.query.join.JoinType;
import net.poweroak.framework.enums.OrderBy;
import net.poweroak.framework.util.StringUtils;
import net.poweroak.iso.CountryCode;
import net.poweroak.iso.vo.country.CountryRich;
import net.poweroak.saas.bucksc.BucksCenterError;
import net.poweroak.saas.bucksc.mapper.ActivityMapper;
import net.poweroak.saas.bucksc.model.PO.Activity;
import net.poweroak.saas.bucksc.model.PO.Channel;
import net.poweroak.saas.bucksc.model.PO.PointsSpendRule;
import net.poweroak.saas.bucksc.model.TO.ActivityAddTO;
import net.poweroak.saas.bucksc.model.TO.ActivityGetTO;
import net.poweroak.saas.bucksc.model.TO.ActivitySearchTO;
import net.poweroak.saas.bucksc.model.TO.ActivityUpdateTO;
import net.poweroak.saas.bucksc.model.VO.ActivityCommonVO;
import net.poweroak.saas.bucksc.model.VO.ActivityVO;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/5/9 10:32
 */
@Repository
@Slf4j
public class ActivityDao extends GenericRepository<Activity, ActivityMapper, String> {
    @Resource
    private ChannelDao channelDao;

    @Resource
    @Lazy
    private PointsSpendRuleDao pointsSpendRuleDao;

    @Resource
    @Lazy
    private PointsEarnRuleDao pointsEarnRuleDao;

    public ActivityDao(ActivityMapper activityMapper) {
        super(activityMapper);
    }


    /**
     * 删除活动
     *
     * @param id
     * @return
     */
    public UnifyResponse<Boolean> deleteRequest(String id) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(Activity::getId).is(id));
        query.addUpdateItem(Channel::getEnable, 0);
        this.update(query);
        return new UnifyResponse<>(Boolean.TRUE);
    }

    /**
     * 更新活动
     *
     * @param request
     * @return
     */
    public UnifyResponse<Boolean> updateActivity(ActivityUpdateTO request) {
        MybatisQuery mybatisQuery = new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(Activity::getEnable).is(1));
        if (StringUtils.isNotEmpty(request.getChannelCode())) {
            mybatisQuery.addCriterion(Criterion.where(Activity::getChannelCode).is(request.getChannelCode()));
        }
        if (request.getCountryId() != null) {
            mybatisQuery.addCriterion(Criterion.where(Activity::getCountryId).is(request.getCountryId()));
        }

        if (StringUtils.isEmpty(request.getEarnRule()) && (StringUtils.isEmpty(request.getSpendRule()))) {
            log.error("发放规则和消耗规则不能同时为空}");
            throw new ApplicationRuntimeException(BucksCenterError.SPEND_AND_EARN_RULES_CANNOT_BE_EMPTY_AT_THE_SAME_TIME);
        }


        if (StringUtils.isNotEmpty(request.getCode())) {
            MybatisQuery mybatisQuery1 = new MybatisQuery();
            mybatisQuery1.addCriterion(Criterion.where(Activity::getEnable).is(true));
            mybatisQuery1.addCriterion(Criterion.where(Activity::getCountryId).is(request.getCountryId()));
            mybatisQuery1.addCriterion(Criterion.where(Activity::getChannelCode).is(request.getChannelCode()));
            List<Activity> activities = this.find(mybatisQuery1);

            activities.forEach(a -> {
                if (Objects.equals(a.getCode(), request.getCode()) && !Objects.equals(a.getId(), request.getId())) {
                    log.error("活动标识已经存在,activityCode:{}", request.getCode());
                    throw new ApplicationRuntimeException(BucksCenterError.ACTIVITY_CODE_IS_ALREADY_EXISTS);
                }
            });


        }


        List<Activity> list = this.find(mybatisQuery);
        list.forEach(a -> {
            if (!Objects.equals(a.getId(), request.getId())) {
                if (Objects.equals(a.getName(), request.getName())) {
                    log.error("活动名称已存在.activityName:{}", request.getName());
                    throw new ApplicationRuntimeException(BucksCenterError.ACTIVITY_NAME_ALREADY_EXISTS);
                }
//                if (Objects.equals(a.getChannelCode(), request.getChannelCode())) {
//                    throw new ApplicationRuntimeException(BucksCenterError.ACTIVITY_CHANNEL_ALREADY_EXISTS);
//                }
            }

        });
        Activity activity = new Activity();
        BeanUtils.copyProperties(request, activity);
        activity.modify();

        if (CollectionUtil.isNotEmpty(request.getDate())) {
            Date beginTime = DateUtil.parse(request.getDate().get(0), DatePattern.NORM_DATETIME_FORMAT);
            Date endTime = DateUtil.parse(request.getDate().get(1), DatePattern.NORM_DATETIME_FORMAT);
            activity.setBeginTime(beginTime);
            activity.setEndTime(endTime);
        }

        this.updateById(activity);
        return new UnifyResponse<>(Boolean.TRUE);
    }

    /**
     * 根据id获取活动
     *
     * @param id
     * @return
     */
    public ActivityVO getOne(String id) {
        Optional<Activity> activityOptional = this.findById(id);
        if (activityOptional.isPresent()) {
            ActivityVO activityVO = new ActivityVO();
            BeanUtils.copyProperties(activityOptional.get(), activityVO);
            activityVO.setId(id);
            return activityVO;
        } else {
            log.error("未查询到积分活动详情数据，id={}", id);
            throw new ApplicationRuntimeException(BucksCenterError.POINTS_ACTIVITY_DETAILS_ARE_NOT_QUERIED);
        }
    }

    /**
     * 不分页查询全部活动
     *
     * @return
     */
    public UnifyResponse<List<Activity>> page() {
        MybatisQuery mybatisQuery = new MybatisQuery();
//        mybatisQuery.addCriterion(Criterion.where(Activity::getEnable).is(true));
        List<Activity> all = this.find(mybatisQuery);
        return new UnifyResponse(all);
    }


    public UnifyResponse<Pagination<ActivityCommonVO>> commonSpend() {
        MybatisQuery mybatisQuery = new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(Activity::getSpendRule).ne(null));
        mybatisQuery.addCriterion(Criterion.where(Activity::getSpendRule).ne(""));
        mybatisQuery.orderBy(Activity::getModTime, OrderBy.DESC);

        mybatisQuery.addCriterion(Criterion.where(Activity::getEnable).is(1))
                .join(ActivityCommonVO.class)
                .joinTable(JoinType.INNER, Channel.class)
                .on(Activity::getChannelCode, Channel::getCode)
                .complete().build();
        List<ActivityCommonVO> results = this.findByJoin(mybatisQuery);
        results.forEach(a -> {
            try {
                CountryRich countryRich = CountryCode.getByGeoId(a.getCountryId());
                if (countryRich != null) {
                    CountryCode countryCode = CountryCode.getByAlpha2Code(countryRich.getCountryCode());
                    a.setCountryName(countryCode.getNameZh());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return new UnifyResponse(results);
    }


    public UnifyResponse<Pagination<ActivityCommonVO>> common() {
        MybatisQuery mybatisQuery = new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(Activity::getEnable).is(1))
                .addCriterion(Criterion.where(Channel::getEnable).is(1))
                .join(ActivityCommonVO.class)
                .joinTable(JoinType.INNER, Channel.class)
                .on(Activity::getChannelCode, Channel::getCode)
                .complete().build();
        List<ActivityCommonVO> results = this.findByJoin(mybatisQuery);
        results.forEach(a -> {
            try {
                if (a.getCountryId() != null) {
                    CountryRich countryRich = CountryCode.getByGeoId(a.getCountryId());
                    if (countryRich != null) {
                        CountryCode countryCode = CountryCode.getByAlpha2Code(countryRich.getCountryCode());
                        a.setCountryName(countryCode.getNameZh());
                    }
                }


            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return new UnifyResponse(results);
    }

    /**
     * 分页查询全部活动
     *
     * @param request
     * @return
     */

    public UnifyResponse<Pagination<ActivityVO>> pageList(ActivitySearchTO request) {
        MybatisQuery mybatisQuery = new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(Activity::getEnable).is(true));

        if (StringUtils.isNotEmpty(request.getActiveType())) {
            if (Objects.equals(request.getActiveType(), "1")) {
                mybatisQuery.addCriterion(Criterion.where(Activity::getEarnRule).ne(null));
                mybatisQuery.addCriterion(Criterion.where(Activity::getEarnRule).ne(""));
            }
            if (Objects.equals(request.getActiveType(), "2")) {
                mybatisQuery.addCriterion(Criterion.where(Activity::getSpendRule).ne(null));
                mybatisQuery.addCriterion(Criterion.where(Activity::getSpendRule).ne(""));
            }
        }

        if (StrUtil.isNotEmpty(request.getActiveCode())) {
            mybatisQuery.addCriterion(Criterion.where(Activity::getCode).is(request.getActiveCode()));
        }
        if (StringUtils.isNotEmpty(request.getName())) {
            mybatisQuery.addCriterion(Criterion.where(Activity::getName).regex(request.getName()));
        }
        if (request.getCountryId() != null) {
            mybatisQuery.addCriterion(Criterion.where(Activity::getCountryId).is(request.getCountryId()));
        }
        if (StringUtils.isNotEmpty(request.getChannelCode())) {

            mybatisQuery.addCriterion(Criterion.where(Activity::getChannelCode).is(request.getChannelCode()));
//            List<Channel> channels = this.channelDao.getByName(request.getName());
//            List<String> ids = new ArrayList<>();
//            channels.forEach(a -> {
//                ids.add(a.getCode());
//            });
//            mybatisQuery.addCriterion(Criterion.where(Activity::getChannelCode).in(ids));
        }

//        mybatisQuery.getOrderByMap().put("name", OrderBy.DESC);
//        mybatisQuery.getOrderByMap().put("country", OrderBy.DESC);
//        mybatisQuery.getOrderByMap().put("channelCode", OrderBy.DESC);

        if (request.getCountryId() != null) {
            mybatisQuery.addCriterion(Criterion.where(Activity::getCountryId).is(request.getCountryId()));
        }

        mybatisQuery.orderBy(Activity::getChannelCode, OrderBy.DESC);
        mybatisQuery.orderBy(Activity::getCountryId, OrderBy.DESC);
        mybatisQuery.orderBy(Activity::getName, OrderBy.DESC);
        Pagination<Activity> activities = this.find(mybatisQuery, request.getPageNumber(), request.getPageSize());
        List<ActivityVO> result = new ArrayList<>();
        activities.getContent().forEach(activity -> {
            ActivityVO activityVO = new ActivityVO();
            BeanUtils.copyProperties(activity, activityVO);
            activityVO.setId(activity.getId());
            if (StringUtils.isNotEmpty(activity.getChannelCode())) {
                MybatisQuery query = new MybatisQuery();
                query.addCriterion(Criterion.where(Channel::getEnable).is(true));
                query.addCriterion(Criterion.where(Channel::getCode).is(activity.getChannelCode()));
                List<Channel> channels = this.channelDao.find(query);
                if (channels.size() != 0) {
                    activityVO.setChannelName(channels.get(0).getName());
                }
            }
            result.add(activityVO);
        });
        return new UnifyResponse(new Pagination(result, activities.getPageable(), activities.getTotalElements()));
    }

    /**
     * 新增活动
     *
     * @param request
     * @return
     */
    public UnifyResponse<Boolean> add(ActivityAddTO request) {

        if (StringUtils.isEmpty(request.getChannelCode())) {
            log.error("渠道标识为空");
            throw new ApplicationRuntimeException(BucksCenterError.CHANNEL_CODE_CANNOT_BE_EMPTY);
        }

        if (request.getCountryId() == null) {
            log.error("国家/地区为空");
            throw new ApplicationRuntimeException(BucksCenterError.COUNTRY_REGION_CANNOT_BE_EMPTY);
        }

        if (StringUtils.isEmpty(request.getEarnRule()) && (StringUtils.isEmpty(request.getSpendRule()))) {
            log.error("发放规则和消耗规则不能同时为空");
            throw new ApplicationRuntimeException(BucksCenterError.SPEND_AND_EARN_RULES_CANNOT_BE_EMPTY_AT_THE_SAME_TIME);
        }

        if (StringUtils.isNotEmpty(request.getCode())) {
            MybatisQuery mybatisQuery = new MybatisQuery();
            mybatisQuery.addCriterion(Criterion.where(Activity::getEnable).is(true));
            mybatisQuery.addCriterion(Criterion.where(Activity::getCountryId).is(request.getCountryId()));
            mybatisQuery.addCriterion(Criterion.where(Activity::getChannelCode).is(request.getChannelCode()));
            List<Activity> activities = this.find(mybatisQuery);

            activities.forEach(a -> {
                if (Objects.equals(a.getCode(), request.getCode())) {
                    log.error("活动标识为空,activityCode:{}", request.getCode());
                    throw new ApplicationRuntimeException(BucksCenterError.ACTIVITY_CODE_IS_ALREADY_EXISTS);
                }
            });


        }


        if (StringUtils.isEmpty(request.getName())) {
            log.error("活动名称为空");
            throw new ApplicationRuntimeException(BucksCenterError.ACTIVITY_NAME_CANNOT_BE_EMPTY);
        } else {
            MybatisQuery mybatisQuery = new MybatisQuery();
            mybatisQuery.addCriterion(Criterion.where(Activity::getEnable).is(true));
            if (StringUtils.isNotEmpty(request.getChannelCode())) {
                mybatisQuery.addCriterion(Criterion.where(Activity::getChannelCode).is(request.getChannelCode()));
            }
            if (request.getCountryId() != null) {
                mybatisQuery.addCriterion(Criterion.where(Activity::getCountryId).is(request.getCountryId()));
            }

            List<Activity> activities = this.find(mybatisQuery);
            activities.forEach(a -> {
                if (Objects.equals(a.getName(), request.getName())) {
                    log.error("同一国家、渠道已存在相同活动名称,activityName:{}", request.getName());
                    throw new ApplicationRuntimeException(BucksCenterError.ACTIVITY_NAME_ALREADY_EXISTS_IN_SAME_COUNTRY_AND_SAME_CHANNEL);
                }


            });
        }
        Activity activity = new Activity();
        BeanUtils.copyProperties(request, activity);
        activity.create();
        if (CollectionUtil.isNotEmpty(request.getDate())) {
            Date beginTime = DateUtil.parse(request.getDate().get(0), DatePattern.NORM_DATETIME_FORMAT);
            Date endTime = DateUtil.parse(request.getDate().get(1), DatePattern.NORM_DATETIME_FORMAT);
            activity.setBeginTime(beginTime);
            activity.setEndTime(endTime);
        }

        Activity insert = this.insert(activity);

        if (Strings.isNotBlank(insert.getSpendRule())) {

            PointsSpendRule rule = new PointsSpendRule();
            rule.setChannelCode(insert.getChannelCode());
            rule.setRuleType(Integer.valueOf(insert.getSpendRule()));
            rule.setActivityId(insert.getId());
            rule.setCountryId(insert.getCountryId());
            rule.setPoints(0);
            rule.create();
            this.pointsSpendRuleDao.insert(rule);

        } else if (Strings.isNotBlank(insert.getEarnRule())) {
//            PointsEarnRule rule = new PointsEarnRule();
//            rule.setChannelCode(insert.getChannelCode());
//            rule.setRuleType(Integer.valueOf(insert.getEarnRule()));
//            rule.setActivityId(insert.getId());
//            rule.setPoints(BigDecimal.valueOf(0));
//            rule.create();
//            this.pointsEarnRuleDao.insert(rule);
        }

        return new UnifyResponse<>(Boolean.TRUE);
    }

    /**
     * 根据countryCode获得活动列表
     *
     * @param countryId
     * @return
     */
    public UnifyResponse<List<Activity>> listByCountryCode(Long countryId) {

        if (countryId != null) {
            MybatisQuery mybatisQuery = new MybatisQuery();
            mybatisQuery.addCriterion(Criterion.where(Activity::getCountryId).is(countryId));
            mybatisQuery.addCriterion(Criterion.where(Activity::getEnable).is(1));
            List<Activity> activities = this.find(mybatisQuery);
            return new UnifyResponse<>(activities);


        } else {
            return null;
        }

    }

    /**
     * 根据发放规则获取活动
     *
     * @param countryId
     * @param earnRule
     * @return
     */
    public List<Activity> findByEarnType(Long countryId, String channelCode, Integer earnRule) {
        MybatisQuery mybatisQuery = new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(Activity::getCountryId).is(countryId));
        mybatisQuery.addCriterion(Criterion.where(Activity::getChannelCode).is(channelCode));
        mybatisQuery.addCriterion(Criterion.where(Activity::getEarnRule).is(earnRule));
        mybatisQuery.addCriterion(Criterion.where(Activity::getEnable).is(1));
        return this.find(mybatisQuery);
    }


    /**
     * 获取活动
     *
     * @param activityCode
     * @param countryId
     * @param channelCode
     * @return
     */
    public Activity getActivity(String activityCode, Long countryId, String channelCode) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(Activity::getCountryId).is(countryId));
        query.addCriterion(Criterion.where(Activity::getChannelCode).is(channelCode));
        query.addCriterion(Criterion.where(Activity::getCode).is(activityCode));
        query.addCriterion(Criterion.where(Activity::getEnable).is(1));
        return this.findOne(query);
    }

    /**
     * 获取活动
     *
     * @param countryId
     * @return
     */
    public List<Activity> findByCountry(Long countryId) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(Activity::getCountryId).is(countryId));
        return this.find(query);
    }

    public UnifyResponse<List<Activity>> getList(ActivityGetTO request) {
        MybatisQuery mybatisQuery = new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(Activity::getEnable).is(1));
        if (StringUtils.isNotEmpty(request.getChannelCode())) {
            mybatisQuery.addCriterion(Criterion.where(Activity::getChannelCode).is(request.getChannelCode()));

        }
        if (request.getCountryId() != null) {
            mybatisQuery.addCriterion(Criterion.where(Activity::getCountryId).is(request.getCountryId()));
        }
        return new UnifyResponse<>(this.find(mybatisQuery));
    }

    public UnifyResponse<List<Activity>> existsList() {
        MybatisQuery mybatisQuery = new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(Activity::getEnable).is(true));
        List<Activity> all = this.find(mybatisQuery);
        return new UnifyResponse(all);
    }


    public Activity findByActivityName(Long countryId,String activityName) {
        MybatisQuery query = new MybatisQuery()
                .addCriterion(Criterion.where(Activity::getCountryId).is(countryId))
                .addCriterion(Criterion.where(Activity::getName).is(activityName))
                .addCriterion(Criterion.where(Activity::getEnable).is(true));
        return this.findOne(query);
    }

    public Activity byCodeAndCountryAndChannel(String code, Long countryId, String channelCode) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(Activity::getEnable).is(1))
                .addCriterion(Criterion.where(Activity::getCode).is(code))
                .addCriterion(Criterion.where(Activity::getCountryId).is(countryId))
                .addCriterion(Criterion.where(Activity::getChannelCode).is(channelCode));
        return this.findOne(query);
    }

}
