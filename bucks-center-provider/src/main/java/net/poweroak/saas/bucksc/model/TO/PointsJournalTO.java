package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.page.PageInfo;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/13 17:42
 * @description
 */
@Data
public class PointsJournalTO  extends PageInfo {
    @ApiModelProperty(value = "用户id")
    private String uid;
    @ApiModelProperty(value = "国家编码")
    private Long countryId;
    @ApiModelProperty(value = "来源渠道")
    private String channelCode;
    @ApiModelProperty(value = "活动id")
    private String activityId;

}
