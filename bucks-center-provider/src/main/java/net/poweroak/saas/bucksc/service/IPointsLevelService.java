package net.poweroak.saas.bucksc.service;

import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.saas.bucksc.model.TO.PointsLevelPageTO;
import net.poweroak.saas.bucksc.model.TO.PointsLevelTO;
import net.poweroak.saas.bucksc.model.VO.PointsLevelDetailVO;
import net.poweroak.saas.bucksc.model.VO.PointsLevelUserDetailVO;
import net.poweroak.saas.bucksc.model.VO.PointsLevelUserGeneralVO;
import net.poweroak.saas.bucksc.model.VO.PointsLevelVO;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2024/4/30
 */
public interface IPointsLevelService {

    void add(PointsLevelTO params);

    Pagination<PointsLevelVO> page(PointsLevelPageTO params);

    PointsLevelDetailVO detail(String id);

    void edit(PointsLevelTO params);

    void delete(String id);

    PointsLevelUserDetailVO userDetail(Long countryId);

    PointsLevelUserGeneralVO userGeneral(Long countryId);
}
