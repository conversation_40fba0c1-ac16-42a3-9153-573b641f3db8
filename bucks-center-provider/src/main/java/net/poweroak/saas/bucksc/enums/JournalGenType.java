package net.poweroak.saas.bucksc.enums;

/**
 * <AUTHOR>
 * @date 2022/5/10 8:46
 */
public enum JournalGenType {
    EARN(1, "增加"),
    SPEND(2, "消耗"),
    RETURN(3, "回退"),
    REC(4, "回收清零"),
    ;


    private Integer code;
    private String name;

    JournalGenType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(Integer code) {
        JournalGenType[] earnRuleTypes = JournalGenType.values();
        for (JournalGenType earnRuleType : earnRuleTypes) {
            if (earnRuleType.code.equals(code)) {
                return earnRuleType.name;
            }
        }
        return "";
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
