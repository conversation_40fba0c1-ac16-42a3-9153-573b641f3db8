package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.page.PageInfo;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/11 13:53
 * @description
 */
@Data
public class PointsBalanceSearchTO extends PageInfo {
//    @ApiModelProperty(value = "uid")
//    private String uid;
    @ApiModelProperty(value = "国家编码")
    private Long countryId;
    @ApiModelProperty(value = "用户昵称")
    private String uname;
    @ApiModelProperty(value = "用户邮箱")
    private String email;

}
