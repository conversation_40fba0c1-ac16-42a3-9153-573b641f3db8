package net.poweroak.saas.bucksc.model.PO;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.DomainEntity;
import net.poweroak.framework.sqltag.Table;

import java.security.cert.CertPathValidatorException;
import java.util.Date;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/26 16:59
 * @description
 */



@Data
@ApiModel("积分活动")
@Table(name = "black_list")
public class BlackList extends DomainEntity<String> {

    @ApiModelProperty(value = "用户id")
    private String uid;

    @ApiModelProperty(value = "加入时间")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date joinTime;

    @ApiModelProperty(value = "加入原因")
    private String reason;

//    @ApiModelProperty(value = "国家编码")
//    private String countryCode;

    @ApiModelProperty(value = "国家编码")
    private Long countryId;
}
