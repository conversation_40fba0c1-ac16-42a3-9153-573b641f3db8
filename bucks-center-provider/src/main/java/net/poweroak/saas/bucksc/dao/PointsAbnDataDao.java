package net.poweroak.saas.bucksc.dao;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.PageInfo;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.data.mybatis.GenericRepository;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.enums.OrderBy;
import net.poweroak.framework.user.UserInfoForServer;
import net.poweroak.saas.bucksc.mapper.PointsAbnDataMapper;
import net.poweroak.saas.bucksc.model.PO.BlackList;
import net.poweroak.saas.bucksc.model.PO.PointsAbnData;
import net.poweroak.saas.bucksc.model.PO.PointsEarnRule;
import net.poweroak.saas.bucksc.model.TO.AbnDataPageTO;
import net.poweroak.saas.bucksc.model.TO.AbnDatePTO;
import net.poweroak.saas.bucksc.model.TO.PointsAbnDataTO;
import net.poweroak.saas.bucksc.model.TO.PointsEarnRuleTO;
import net.poweroak.saas.bucksc.model.VO.PointsAbnDataVO;
import net.poweroak.saas.bucksc.utils.LocalDateUtil;
import net.poweroak.saas.uc.client.UserClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zx on 2022/5/31 11:26
 */
@Repository
@Slf4j
public class PointsAbnDataDao extends GenericRepository<PointsAbnData, PointsAbnDataMapper, String> {
    public PointsAbnDataDao(PointsAbnDataMapper genericMapper) {
        super(genericMapper);
    }

    public Pagination<PointsAbnDataTO> page(AbnDataPageTO pageTO) {

        final List<PointsAbnDataTO> abnDataList = this.getMapper().list((pageTO.getPageNo() - 1) * pageTO.getPageSize(), pageTO.getPageSize());

        final int count = this.getMapper().counter();

        return new Pagination(abnDataList, new PageInfo(pageTO.getPageNo() - 1, pageTO.getPageSize()), count);
    }

    @Resource
    private BlackListDao blackListDao;

    public Pagination<PointsAbnDataVO> detailList(AbnDatePTO request) {


        MybatisQuery mybatisQuery = new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(PointsAbnData::getAbnTime).is(request.getAbnDays()));
        mybatisQuery.addOrderByItem(PointsAbnData::getCreTime, OrderBy.DESC);
        Pagination<PointsAbnData> page = this.find(mybatisQuery, request.getPageNumber(), request.getPageSize());
        List<PointsAbnData> dataList = page.getContent();


        //统计用户信息

        List<String> userIds = dataList.stream().map(PointsAbnData::getUid).distinct().collect(Collectors.toList());
        Pair<Map<String, String>, Map<String, String>> pair = getUserMap(userIds);

        List<PointsAbnDataVO> list = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(dataList)) {
            dataList.forEach(abnData -> {
                PointsAbnDataVO dataVO = new PointsAbnDataVO();
                BeanUtils.copyProperties(abnData, dataVO);
                dataVO.setUserName(pair.getLeft().get(abnData.getUid()));
                dataVO.setUserAccount(pair.getRight().get(abnData.getUid()));
                BlackList blackList = blackListDao.findOne(MybatisQuery.where()
                        .addCriterion(Criterion.where(BlackList::getEnable).is(true))
                        .addCriterion(Criterion.where(BlackList::getCountryId).is(abnData.getCountryId()))
                        .addCriterion(Criterion.where(BlackList::getUid).is(abnData.getUid())));
                dataVO.setIsPullBlack(blackList == null ? false : true);
                list.add(dataVO);
            });
        }
        return new Pagination(list, page.getPageable(), page.getTotalElements());
    }

    @Resource
    private UserClient userClient;


    private Pair<Map<String, String>, Map<String, String>> getUserMap(List<String> userIds) {
        final UnifyResponse<List<UserInfoForServer>> users = userClient.list(userIds);
        Map<String, String> nameMap = (users.isOK() && (users.getData() != null)) ? users.getData().stream().filter(user -> StringUtils.isNotEmpty(user.getNickname())).collect(Collectors.toMap(UserInfoForServer::getUid, UserInfoForServer::getNickname)) : Maps.newHashMap();
        Map<String, String> accountMap = (users.isOK() && (users.getData() != null)) ? users.getData().stream().filter(user -> StringUtils.isNotEmpty(user.getUsername())).collect(Collectors.toMap(UserInfoForServer::getUid, UserInfoForServer::getUsername)) : Maps.newHashMap();
        return Pair.of(nameMap, accountMap);
    }


    @Resource
    private PointsEarnRuleDao pointsEarnRuleDao;

    public PointsEarnRule earnRuleDetail(PointsEarnRuleTO ruleTO) {
        return pointsEarnRuleDao.findOne(MybatisQuery.where()
                .addCriterion(Criterion.where(PointsEarnRule::getEnable).is(true))
                .addCriterion(Criterion.where(PointsEarnRule::getActivityId).is(ruleTO.getActivityId()))
                .addCriterion(Criterion.where(PointsEarnRule::getChannelCode).is(ruleTO.getChannelCode())).limit(1));
    }

    /**
     * 预警
     */
    public void earlyWarn(PointsAbnDataVO pointsAbnDataVO) {
        PointsAbnData pointsAbnData = new PointsAbnData();
        BeanUtils.copyProperties(pointsAbnDataVO, pointsAbnData);
        pointsAbnData.create();
        pointsAbnData.setAbnTime(LocalDateUtil.getLocalDateTime(pointsAbnDataVO.getCountryId()));
        this.insert(pointsAbnData);
    }

}



