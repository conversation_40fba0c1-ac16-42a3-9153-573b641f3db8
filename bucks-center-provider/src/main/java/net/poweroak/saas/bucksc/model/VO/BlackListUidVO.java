package net.poweroak.saas.bucksc.model.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.sqltag.TableField;
import net.poweroak.saas.bucksc.model.PO.BlackList;
import net.poweroak.saas.bucksc.model.PO.PointsBalance;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023/12/7 11:58
 */


@ApiModel(value = "查询在黑名单表中的uid")
@Data
public class BlackListUidVO {

    @ApiModelProperty(value = "用户id")
    @TableField(table = BlackList.class, field = "uid")
    private String uid;

}
