package net.poweroak.saas.bucksc.enums;

import lombok.Getter;
import net.poweroak.framework.api.AppMessage;
import net.poweroak.framework.api.util.ApplicationUtil;
import net.poweroak.framework.app.MicroServiceApplication;
import net.poweroak.framework.i18n.I18nEnMessage;
import net.poweroak.framework.i18n.Internationalization;
import net.poweroak.framework.i18n.LocaleUtils;
import net.poweroak.framework.interfaces.MessageID;
import net.poweroak.saas.bucksc.BucksCenterApp;

/**
 * <AUTHOR>
 * @date 2023/2/23
 */
@Internationalization(app = MicroServiceApplication.bluBucksCenter)
public enum WeekCode implements AppMessage, MessageID, I18nEnMessage {
    SUNDAY(7, 1, "周日", "Sun", "1867d2e4cb0f791150ab90fe322", BucksCenterApp.I18N_CODE_PREFIX + "week_code.sunday"),
    MONDAY(1, 2, "周一", "Mon", "1867d2e5be7f791150ab90fe323", BucksCenterApp.I18N_CODE_PREFIX + "week_code.monday"),
    TUESDAY(2, 3, "周二", "Tue", "1867d2e677ef791150ab90fe324", BucksCenterApp.I18N_CODE_PREFIX + "week_code.tuesday"),
    WEDNESDAY(3, 4, "周三", "Wed", "1867d2e739af791150ab90fe325", BucksCenterApp.I18N_CODE_PREFIX + "week_code.wednesday"),
    THURSDAY(4, 5, "周四", "Thu", "1867d2e7f7ef791150ab90fe326", BucksCenterApp.I18N_CODE_PREFIX + "week_code.thursday"),
    FRIDAY(5, 6, "周五", "Fri", "1867d2e8cbef791150ab90fe327", BucksCenterApp.I18N_CODE_PREFIX + "week_code.friday"),
    SATURDAY(6, 7, "周六", "Sat", "1867d2e9958f791150ab90fe328", BucksCenterApp.I18N_CODE_PREFIX + "week_code.saturday");


    private Integer code;
    private Integer oriCode;
    private String messageZhCn;
    private String i18nCode;
    private String msgId;
    private String messageEn;
    private String source;

    WeekCode(int code, int oriCode, String messageZhCN, String messageEn, String msgId, String i18nCode) {
        this.oriCode = oriCode;
        this.code = code;
        this.msgId = msgId;
        this.i18nCode = i18nCode;
        this.messageZhCn = messageZhCN;
        this.messageEn = messageEn;
    }

    public static String getNameByCode(Integer code) {
        WeekCode[] earnRuleTypes = WeekCode.values();
        for (WeekCode weekCode : earnRuleTypes) {
            if (weekCode.code.equals(code)) {
                return LocaleUtils.translate(weekCode);
            }
        }
        return "";
    }

    public static Integer getCodeByOriCode(Integer oriCode) {
        WeekCode[] earnRuleTypes = WeekCode.values();
        for (WeekCode weekCode : earnRuleTypes) {
            if (weekCode.oriCode.equals(oriCode)) {
                return weekCode.code;
            }
        }
        return -1;
    }

    public static Integer getOriCodeByCode(Integer code) {
        WeekCode[] earnRuleTypes = WeekCode.values();
        for (WeekCode weekCode : earnRuleTypes) {
            if (weekCode.code.equals(code)) {
                return weekCode.oriCode;
            }
        }
        return -1;
    }



    /**
     * 获取消息错误码
     *
     * @return
     */
    public int getCode() {
        return this.code;
    }

    /**
     * 获取消息的国际化Code
     *
     * @return
     */
    @Override
    public String getI18nCode() {
        return this.i18nCode;
    }

    /**
     * 获取消息的默认本地化文本消息
     *
     * @return
     */
    @Override
    public String getMessage() {
        return this.messageZhCn;
    }

    /**
     * 获取应用服务标识
     *
     * @return
     */
    @Override
    public String getAppId() {
        return ApplicationUtil.getApplicationNameReal();
    }

    @Override
    public String getMsgId() {
        return this.getMessageId();
    }

    @Override
    public int getMsgCode() {
        return this.getCode();
    }

    /**
     * 获取消息定义的全局唯一ID
     *
     * @return
     */
    public String getMessageId() {
        return this.msgId;
    }

    public Integer getOriCode() {
        return oriCode;
    }

    public String getMessageZhCn() {
        return messageZhCn;
    }

    @Override
    public String getMessageEn() {
        return messageEn;
    }

    public String getSource() {
        return source;
    }
}
