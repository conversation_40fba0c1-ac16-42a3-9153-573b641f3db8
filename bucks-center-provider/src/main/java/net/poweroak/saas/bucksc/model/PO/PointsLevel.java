package net.poweroak.saas.bucksc.model.PO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.DomainEntity;
import net.poweroak.framework.sqltag.Table;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2024/4/30
 */
@Data
@ApiModel("积分等级")
@Table(name = "points_level")
public class PointsLevel extends DomainEntity<String> {

    @ApiModelProperty(value = "国家ID")
    private Long countryId;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "货币单位")
    private String currencyUnit;

    @ApiModelProperty(value = "等级说明")
    private String instructions;

    @ApiModelProperty(value = "等级说明的html形式")
    private String instructionsHtml;
}
