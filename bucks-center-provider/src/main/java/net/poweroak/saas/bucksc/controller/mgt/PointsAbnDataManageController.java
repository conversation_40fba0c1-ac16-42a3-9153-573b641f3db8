package net.poweroak.saas.bucksc.controller.mgt;

import io.swagger.annotations.Api;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.data.perms.norm.RouteMenu;
import net.poweroak.framework.pedestal.annotate.RequirePermission;
import net.poweroak.framework.pedestal.annotate.RouteAction;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.dao.PointsAbnDataDao;
import net.poweroak.saas.bucksc.model.PO.PointsEarnRule;
import net.poweroak.saas.bucksc.model.TO.AbnDataPageTO;
import net.poweroak.saas.bucksc.model.TO.AbnDatePTO;
import net.poweroak.saas.bucksc.model.TO.PointsEarnRuleTO;
import net.poweroak.saas.bucksc.model.VO.PointsAbnDataVO;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;

/**
 * Created by zx on 2022/5/31 17:28
 */

@ApiIgnore
@RestController
@Api(tags = "异常数据管理")
@RequestMapping("/" + RouteEndpoint.MGT + "/pointsAbnData")
@RouteMenu(module = "pointsAbnData", label = "异常数据管理", parent = PointsRiskMgtController.class)
public class PointsAbnDataManageController {

    @Resource
    private PointsAbnDataDao abnDataDao;


    @PostMapping("/page")
    @RouteAction(action = "pageList",id = "1811d2093fff791157c8280bba7", label = "异常数据统计", view = "/BluBucksC/risk/statistics/list.vue", linkToModule = true)
    @RequirePermission
    public UnifyResponse<Pagination> listByPage(@RequestBody AbnDataPageTO pageTO) {
        return new UnifyResponse(abnDataDao.page(pageTO));
    }

    @PostMapping("/detailList")
    @RouteAction(action = "detailList",id = "182334e6016f79115229d154abe", label = "异常数据统计明细")
    @RequirePermission
    public UnifyResponse<Pagination<PointsAbnDataVO>> detailList(@RequestBody AbnDatePTO pageTO) {
        return new UnifyResponse(abnDataDao.detailList(pageTO));
    }


    @PostMapping("/earnRuleDetail")
    @RouteAction(action = "earnRuleDetail",id = "1826bb7e11cf79115229d154ba6", label = "发放规则明细查询")
    @RequirePermission
    public UnifyResponse<PointsEarnRule> earnRuleDetail(@RequestBody PointsEarnRuleTO pointsEarnRuleTO) {
        return new UnifyResponse(abnDataDao.earnRuleDetail(pointsEarnRuleTO));
    }

}
