package net.poweroak.saas.bucksc.enums;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2023/5/5 16:24
 * @description
 */
public enum DocumentType {
    TURNTABLE_RAFFLE_RULES("LOTTERY_RULE_CODE","转盘抽奖规则"),
    CHECK_IN_RULES("POINTS_SIGN_RULE","签到规则"),
    INTEGRAL_COPY("POINTS_RULE_CODE","积分文案"),
    ;




    private String code;
    private String name;

    DocumentType()
    {

    }

    DocumentType(String code, String name)
    {

        this.code = code;
        this.name = name;
    }



    public String getCode()
    {

        return code;
    }

    public String getName()
    {

        return name;
    }
}
