package net.poweroak.saas.bucksc.dao;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.data.page.PageInfo;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.exception.ApplicationRuntimeException;
import net.poweroak.framework.data.mybatis.GenericRepository;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.saas.bucksc.BucksCenterError;
import net.poweroak.saas.bucksc.mapper.PointsAbnDataRuleMapper;
import net.poweroak.saas.bucksc.model.PO.PointsAbnDataRule;
import net.poweroak.saas.bucksc.model.TO.AbnDataPageTO;
import net.poweroak.saas.bucksc.model.TO.AbnDataRulePageTO;
import net.poweroak.saas.bucksc.model.TO.AbnDataRuleTO;
import net.poweroak.saas.bucksc.model.VO.PointsAbnDataRulePageVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by zx on 2022/5/31 11:26
 */
@Repository
@Slf4j
public class PointsAbnDataRuleDao extends GenericRepository<PointsAbnDataRule, PointsAbnDataRuleMapper, String> {

    public PointsAbnDataRuleDao(PointsAbnDataRuleMapper genericMapper) {
        super(genericMapper);
    }

    public Pagination<PointsAbnDataRulePageVO> page(AbnDataRulePageTO pageTO) {
        final MybatisQuery query = MybatisQuery.where();
        Pagination<PointsAbnDataRule> pagination = this.find(query,pageTO.getPageNumber(), pageTO.getPageSize());
        List<PointsAbnDataRulePageVO> list = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(pagination.getContent())) {
            pagination.getContent().forEach(rule -> {
                PointsAbnDataRulePageVO rulePageVO = new PointsAbnDataRulePageVO();
                BeanUtils.copyProperties(rule,rulePageVO);
                rulePageVO.setId(rule.getId());
                rulePageVO.setEnable(rule.getEnable());
                rulePageVO.setCreTime(rule.getCreTime());
                list.add(rulePageVO);
            });
        }
        return new Pagination(list, new PageInfo(pageTO.getPageNumber() - 1, pageTO.getPageSize()), pagination.getTotalElements());
    }

    public void add(AbnDataRuleTO abnDataRuleTO) {

        final PointsAbnDataRule abnDataRule = this.findOne(MybatisQuery.where()
                .addCriterion(Criterion.where(PointsAbnDataRule::getEnable).is(true))
                .addCriterion(Criterion.where(PointsAbnDataRule::getAbnType).is(abnDataRuleTO.getAbnType()))
                .addCriterion(Criterion.where(PointsAbnDataRule::getRuleType).is(abnDataRuleTO.getRuleType())));

        if(abnDataRule !=null){
            throw new ApplicationRuntimeException(BucksCenterError.ABN_RULE_EXISTED);
        }

        PointsAbnDataRule pointsAbnDataRule = new PointsAbnDataRule();
        BeanUtils.copyProperties(abnDataRuleTO,pointsAbnDataRule);
        pointsAbnDataRule.create();
        this.insert(pointsAbnDataRule);

    }

    public void update(AbnDataRuleTO abnDataRuleTO) {

        final PointsAbnDataRule abnDataRule = this.findOne(MybatisQuery.where()
                .addCriterion(Criterion.where(PointsAbnDataRule::getEnable).is(true))
                .addCriterion(Criterion.where(PointsAbnDataRule::getId).nin(abnDataRuleTO.getId()))
                .addCriterion(Criterion.where(PointsAbnDataRule::getAbnType).is(abnDataRuleTO.getRuleType()))
                .addCriterion(Criterion.where(PointsAbnDataRule::getRuleType).is(abnDataRuleTO.getAbnType())));

        if(abnDataRule !=null){
            throw new ApplicationRuntimeException(BucksCenterError.ABN_RULE_EXISTED);
        }

        PointsAbnDataRule dataRule = new PointsAbnDataRule();
        dataRule.setId(abnDataRuleTO.getId());
        dataRule.setRuleType(abnDataRuleTO.getRuleType());
        dataRule.setAbnType(abnDataRuleTO.getAbnType());
        dataRule.setTimeUnit(abnDataRuleTO.getTimeUnit());
        dataRule.setUnitTimeCount(abnDataRuleTO.getUnitTimeCount());
        dataRule.modify();
        this.updateById(dataRule);

    }
}
