package net.poweroak.saas.bucksc.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2023/11/6
 */
@Getter
public enum PlatformType {
    AMAZON(1, "Amazon"),
    WALMART(2, "Walmart"),
    EBAY(3, "eBay"),
    BLUETTI(4, "BLUETTI"),
    OTHER(0, "Others"),
    ;

    private int code;
    private String platform;

    PlatformType(int code, String platform) {
        this.code = code;
        this.platform = platform;
    }

    public static List<String> toList() {
        List<String> list = new ArrayList<>();
        for (PlatformType item : PlatformType.values()) {
            list.add(item.getPlatform());
        }
        return list;
    }
}
