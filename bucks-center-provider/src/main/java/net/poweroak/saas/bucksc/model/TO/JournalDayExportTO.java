package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023/12/10 9:44
 */

@ApiModel(value = "分日期统计积分导出的输入条件")
@Data
public class JournalDayExportTO {

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "渠道code")
    private String channelCode;

    @ApiModelProperty(value = "活动id")
    private String activityId;

}
