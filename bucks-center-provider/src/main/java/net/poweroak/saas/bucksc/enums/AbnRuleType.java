package net.poweroak.saas.bucksc.enums;

/**
 * Created by zx on 2022/6/1 17:51 异常规则类型
 */

public enum AbnRuleType {

    CUSTOMIZE_EARN(1, "自定义/发放"),
    RULE_BACK(2, "积分基础规则/回退");

    private int code;
    private String name;

    AbnRuleType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
