package net.poweroak.saas.bucksc.controller.mgt;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.BluBucksCenterApplication;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.data.perms.norm.RouteMenu;
import net.poweroak.framework.pedestal.annotate.RouteAction;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.model.TO.PointsInterestsPageTO;
import net.poweroak.saas.bucksc.model.TO.PointsInterestsTO;
import net.poweroak.saas.bucksc.model.VO.PointsInterestsVO;
import net.poweroak.saas.bucksc.service.IPointsInterestsService;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2024/4/29
 */
@Slf4j
@RequestMapping("/" + RouteEndpoint.MGT + "/pointsInterests")
@Api(tags = "积分权益")
@RestController
@RouteMenu(module = "pointsInterestsModule", label = "积分权益", parent = BluBucksCenterApplication.class, order = 9)
@RequiredArgsConstructor
public class PointsInterestsController {

    private final IPointsInterestsService interestsService;

    @PostMapping("/page")
    @RouteAction(id = "18f27cc508df791153a32a140c3", action = "page", label = "积分权益", view = "/BluBucksC/interests/page.vue", linkToModule = true)
    @ApiOperation(value = "积分权益分页", httpMethod = "POST")
    public UnifyResponse<Pagination<PointsInterestsVO>> page(@RequestBody PointsInterestsPageTO params) {
        return new UnifyResponse<>(interestsService.page(params));
    }

    @PostMapping("/add")
    @ApiOperation(value = "积分权益新增", httpMethod = "POST")
    public UnifyResponse<Void> add(@RequestBody @Valid PointsInterestsTO params) {
        interestsService.add(params);
        return new UnifyResponse<>();
    }

    @PutMapping("/edit")
    @ApiOperation(value = "积分权益修改", httpMethod = "PUT")
    public UnifyResponse<Void> edit(@RequestBody @Valid PointsInterestsTO params) {
        interestsService.edit(params);
        return new UnifyResponse<>();
    }

    @DeleteMapping("/delete")
    @ApiOperation(value = "积分权益删除", httpMethod = "DELETE")
    public UnifyResponse<Void> delete(@RequestParam("id") String id) {
        interestsService.delete(id);
        return new UnifyResponse<>();
    }
}
