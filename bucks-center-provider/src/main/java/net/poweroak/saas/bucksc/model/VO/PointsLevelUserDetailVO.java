package net.poweroak.saas.bucksc.model.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2024/5/6
 */
@Data
public class PointsLevelUserDetailVO {

    @ApiModelProperty("当前用户剩余积分")
    private Integer balance;

    @ApiModelProperty(value = "消费金额")
    private String amount;

    @ApiModelProperty(value = "当前用户所处等级下标")
    private Integer levelIndex;

    @ApiModelProperty(value = "用户升级到的积分等级（没有升级时返回0）")
    private Integer upgradeLevel;

    @ApiModelProperty(value = "国家ID")
    private Long countryId;

    @ApiModelProperty(value = "国家名称")
    private String countryName;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "货币单位")
    private String currencyUnit;

    @ApiModelProperty(value = "等级说明")
    private String instructions;

    @ApiModelProperty(value = "等级说明的html形式")
    private String instructionsHtml;

    @ApiModelProperty(value = "规则列表")
    private List<LevelVO> ruleList;

    @Data
    public static class LevelVO {

        @ApiModelProperty(value = "logo的链接")
        private String logoUrl;

        @ApiModelProperty(value = "排序下标")
        private Integer index;

        @ApiModelProperty(value = "该等级的名称")
        private String name;

        @ApiModelProperty(value = "状态（0：未满足，1：到达，2：已获得）")
        private Integer status;

        @ApiModelProperty(value = "赠送积分数")
        private Integer giftPoints;

        @ApiModelProperty(value = "消费门槛")
        private String consumptionThreshold;

        @ApiModelProperty(value = "积分权益列表")
        private List<PointsInterestsVO> interestsList;
    }
}
