package net.poweroak.saas.bucksc.model.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * Created by zx on 2022/5/10 10:36
 */
@Data
public class SpendRuleVO {

    private String id;

    @ApiModelProperty(value = "渠道标识")
    private String channelCode;

    @ApiModelProperty(value = "活动ID")
    @NotBlank(message = "活动不能为空")
    private String activityId;

    @ApiModelProperty(value = "1-券卡兑换；2-物品兑换；3-货币抵扣；4-抽奖；9-通用")
    @NotNull(message = "规则类型不能为空")
    private Integer ruleType;

    @ApiModelProperty(value = "积分值")
    @NotNull(message = "积分值不能为空")
    private Integer points;

    @ApiModelProperty(value = "国家编码")
    private Long countryId;

    @ApiModelProperty(value = "消耗门槛（设置只有达到或超过该门槛值时，才可以使用该规则）")
    private Integer spendLimit;

    @ApiModelProperty(value = "兑换比例")
    private BigDecimal exchangeRatio;

    @ApiModelProperty(value = "会员等级")
    private String memberLevel;
}
