package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/5/7 9:15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class JournalAddTO {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "用户ID")
    private String uid;

    @ApiModelProperty(value = "活动ID")
    private String activityId;

    @ApiModelProperty(value = "渠道标识")
    private String channelCode;

    @ApiModelProperty(value = "国家编码")
    private Long countryId;

    @ApiModelProperty(value = "积分")
    private Integer points;

    @ApiModelProperty(value = "产生时间")
    private Date genTime;

    @ApiModelProperty(value = "当地时区下的产生时间")
    private Date localGenTime;

    @ApiModelProperty(value = "产生类型(1-增加；2-消耗；3-回退；4-回收清零)")
    private Integer genType;

    @ApiModelProperty(value = "0=其他 1=签到 2=订单 3=光伏 4=积分兑换 5=回收清零 6=大转盘")
    private Integer subType;

    @ApiModelProperty(value = "0=其他 1=签到 2=订单 3=光伏 4=积分兑换 5=回收清零 6=大转盘")
    private Integer subOriType;

    @ApiModelProperty(value = "积分类型说明")
    private String typeDesc;

    @ApiModelProperty(value = "积分说明")
    private String desc;

    @ApiModelProperty(value = "订单ID")
    private String orderId;

    @ApiModelProperty(value = "来源ID")
    private String sourceId;

    @ApiModelProperty(value = "比例")
    private BigDecimal ratio;

    @ApiModelProperty(value = "设备SN")
    private String pvSn;

    @ApiModelProperty(value = "数据读取开始时间")
    private Date pvBeginTime;

    @ApiModelProperty(value = "数据读取结束时间")
    private Date pvEndTime;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "消耗金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "费金额增减：默认true加、false减")
    private Boolean amountSymbol;


}
