package net.poweroak.saas.bucksc.dao;

import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.exception.ApplicationRuntimeException;
import net.poweroak.framework.data.mybatis.GenericRepository;

import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.enums.OrderBy;
import net.poweroak.framework.util.StringUtils;
import net.poweroak.saas.bucksc.BucksCenterError;
import net.poweroak.saas.bucksc.mapper.DocumentsTypeMapper;

import net.poweroak.saas.bucksc.model.PO.Channel;
import net.poweroak.saas.bucksc.model.PO.DocumentsInfo;
import net.poweroak.saas.bucksc.model.PO.DocumentsType;
import net.poweroak.saas.bucksc.model.TO.DocumentsTypeAddTO;
import net.poweroak.saas.bucksc.model.TO.DocumentsTypeTO;
import net.poweroak.saas.bucksc.model.TO.DocumentsTypeUpdateTO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/7/21 10:00
 * @description
 */
@Repository
@Slf4j
public class DocumentsTypeDao extends GenericRepository<DocumentsType, DocumentsTypeMapper, String> {

    @Resource
    private DocumentsInfoDao documentsInfoDao;

    public DocumentsTypeDao(DocumentsTypeMapper genericMapper) {
        super(genericMapper);
    }

    public UnifyResponse<Pagination<DocumentsType>> pageList(DocumentsTypeTO request) {
        MybatisQuery mybatisQuery=new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(DocumentsType::getEnable).is(1));
        if(StringUtils.isNotEmpty(request.getCode())){
            mybatisQuery.addCriterion(Criterion.where(DocumentsType::getCode).regex(request.getCode()));
        }
        if(StringUtils.isNotEmpty(request.getName())){
            mybatisQuery.addCriterion(Criterion.where(DocumentsType::getName).regex(request.getName()));
        }
        mybatisQuery.orderBy(DocumentsType::getModTime, OrderBy.DESC);
        return new UnifyResponse(this.find(mybatisQuery, request.getPageNumber(), request.getPageSize()));
    }

    public UnifyResponse<Boolean> add(DocumentsTypeAddTO request) {
        if(StringUtils.isEmpty(request.getName())){
            log.error("文案名称为空");
            throw new ApplicationRuntimeException(BucksCenterError.DOCUMENT_NAME_CANNOT_BE_EMPTY);
        }
        if(StringUtils.isEmpty(request.getCode())){
            log.error("文案代码为空");
            throw new ApplicationRuntimeException(BucksCenterError.DOCUMENT_CODE_CANNOT_BE_EMPTY);
        }

        MybatisQuery mybatisQuery=new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(DocumentsType::getEnable).is(1));
        mybatisQuery.addCriterion(Criterion.where(DocumentsType::getCode).is(request.getCode()));
        DocumentsType one = this.findOne(mybatisQuery);
        if(one!=null){

                log.error("文案代码重复");
                throw new ApplicationRuntimeException(BucksCenterError.DOCUMENT_CODE_CANNOT_BE_REPEATED);

        }

//        List<DocumentsType> documentsTypes = this.find(mybatisQuery);
//        documentsTypes.forEach(documents->{
//            if(Objects.equals(request.getCode(),documents.getCode())){
//                log.error("文案代码重复");
//                throw new ApplicationRuntimeException(BucksCenterError.DOCUMENTS_TYPE_CODE_CANNOT_BE_REPEATED);
//            }
//
//
//        });

        DocumentsType documentsType=new DocumentsType();
        documentsType.setCode(request.getCode());
        documentsType.setName(request.getName());
        documentsType.create();
        this.insert(documentsType);
        return new UnifyResponse<>(Boolean.TRUE);


    }

    public UnifyResponse<DocumentsType> getOne(String id) {
        Optional<DocumentsType> documentsType = this.findById(id);
        if (documentsType.isPresent()) {
            return new UnifyResponse(documentsType.get());
        } else {
            log.error("未查询到文案类型详情数据，id={}", id);
            throw new ApplicationRuntimeException(BucksCenterError.THE_SOURCE_DOCUMENT_DETAILS_DATA_WERE_NOT_QUERIED);
        }


    }

    public UnifyResponse<Boolean> updateDocumentsType(DocumentsTypeUpdateTO request) {

        MybatisQuery query=new MybatisQuery();
        query.addCriterion(Criterion.where(DocumentsType::getEnable).is(1));
        query.addCriterion(Criterion.where(DocumentsType::getCode).is(request.getCode()));
        query.addCriterion(Criterion.where(DocumentsType::getId).ne(request.getId()));
        DocumentsType one = this.findOne(query);
        if(one!=null){

            log.error("文案代码重复");
            throw new ApplicationRuntimeException(BucksCenterError.DOCUMENT_CODE_CANNOT_BE_REPEATED);

        }

        MybatisQuery mybatisQuery=new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(DocumentsType::getId).is(request.getId()));
        mybatisQuery.addUpdateItem(DocumentsType::getName,request.getName());
        this.update(mybatisQuery);
        return new UnifyResponse<>(Boolean.TRUE);
    }

    public UnifyResponse<Boolean> deleteDocumentsType(String id) {
        Optional<DocumentsType> byId = this.findById(id);
        if(byId.isPresent()){
            DocumentsType type=byId.get();
            MybatisQuery query1=new MybatisQuery();
            query1.addCriterion(Criterion.where(DocumentsInfo::getEnable).is(1));
            query1.addCriterion(Criterion.where(DocumentsInfo::getType).is(type.getCode()));
            DocumentsInfo one = this.documentsInfoDao.findOne(query1);
            if(one!=null){
                log.error("存在该文案类型的文案信息,请先删除文案信息");
                throw new ApplicationRuntimeException(BucksCenterError.THERE_IS_A_DOCUMENT_INFO_FOR_THIS_DOCUMENTS_TYPE);
            }
        }





        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(DocumentsType::getId).is(id));
        query.addUpdateItem(DocumentsType::getEnable, 0);
        this.update(query);
        return new UnifyResponse<>(Boolean.TRUE);
    }

    public UnifyResponse<List<DocumentsType>> list() {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(DocumentsType::getEnable).is(1));
        List<DocumentsType> documentsTypes = this.find(query);
        return new UnifyResponse(documentsTypes);

    }
}
