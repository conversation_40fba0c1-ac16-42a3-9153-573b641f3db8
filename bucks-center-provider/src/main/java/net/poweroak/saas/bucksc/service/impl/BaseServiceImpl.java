package net.poweroak.saas.bucksc.service.impl;

import net.poweroak.saas.bucksc.model.VO.RegionCountryVO;
import net.poweroak.saas.bucksc.service.IBaseService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by zx on 2022/5/18 15:23
 */
@Service
public class BaseServiceImpl implements IBaseService {

//    @Resource
//    private RegionClient regionClient;

    @Override
    public List<RegionCountryVO> queryAllCountry() {
//        List<RegionCountryVO> regionCountryVOS = Lists.newArrayList();
//        CountryCode.getCountriesByCodeMap().forEach((key,val)->{
//            regionCountryVOS.add(RegionCountryVO.builder().countryId(val.getAlpha2Code()).nameZh(val.getNameZh()).build());
//        });

//        final UnifyResponse<List<RegionCountryResponse>> unifyResponse = regionClient.queryAllCountry();
//
//        List<RegionCountryVO> regionCountryVOS = Lists.newArrayList();
//
//        if(unifyResponse.isOK()){
//            final List<RegionCountryResponse> data = unifyResponse.getData();
//            data.forEach(regionCountry -> regionCountryVOS.add(RegionCountryVO.builder().countryCode(regionCountry.getCountryCode()).nameZh(regionCountry.getNameZh()).build()));
//        }
        return null;
    }





}
