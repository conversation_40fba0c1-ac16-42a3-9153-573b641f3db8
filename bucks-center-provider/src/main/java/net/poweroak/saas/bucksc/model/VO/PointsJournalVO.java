package net.poweroak.saas.bucksc.model.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.sqltag.TableField;
import net.poweroak.saas.bucksc.model.PO.Activity;
import net.poweroak.saas.bucksc.model.PO.Channel;
import net.poweroak.saas.bucksc.model.PO.PointsJournal;

import java.util.Date;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/13 17:58
 * @description
 */
@Data
public class PointsJournalVO{

    @TableField(table= Activity.class,field = "name")
    @ApiModelProperty(value = "活动名称")
    private String activityName;
    @TableField(table= Activity.class,field = "id")
    @ApiModelProperty(value = "活动id")
    private String activityId;
    @TableField(table= Channel.class,field = "name")
    @ApiModelProperty(value = "渠道名称")
    private String channelName;
    @TableField(table= Channel.class,field = "code")
    @ApiModelProperty(value = "渠道标识")
    private String channelCode;
    @TableField(table= PointsJournal.class,field = "countryId")
    @ApiModelProperty(value = "国家编码")
    private Long countryId;
    @TableField(table= PointsJournal.class,field = "genTime")
    @JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "产生时间")
    private Date genTime;
    @ApiModelProperty(value = "积分数")
    @TableField(table= PointsJournal.class,field = "points")
    private String points;

    @ApiModelProperty(value = "产生类型(1-增加；2-消耗；3-回退；4-回收清零)")
    @TableField(table= PointsJournal.class,field = "genType")
    private Integer genType;

    @ApiModelProperty(value = "业务单号")
    @TableField(table = PointsJournal.class, field = "orderId")
    private String orderId;

    @ApiModelProperty(value = "消费金额")
    @TableField(table = PointsJournal.class, field = "amount")
    private String amount;

    @ApiModelProperty(value = "币种")
    @TableField(table = PointsJournal.class, field = "currency")
    private String currency;

    @ApiModelProperty(value = "积分说明")
    @TableField(table = PointsJournal.class, field = "desc")
    private String desc;
}
