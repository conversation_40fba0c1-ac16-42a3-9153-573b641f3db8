package net.poweroak.saas.bucksc.model.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.page.PageInfo;
import net.poweroak.framework.sqltag.TableField;
import net.poweroak.saas.bucksc.model.PO.Activity;
import net.poweroak.saas.bucksc.model.PO.DocumentsInfo;
import net.poweroak.saas.bucksc.model.PO.DocumentsType;

import java.util.Date;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/7/25 14:22
 * @description
 */


@Data
public class DocumentsInfoVO{


    @ApiModelProperty(value = "id")
    @TableField(table = DocumentsInfo.class, field = "id")
    private String id;

    @ApiModelProperty(value="文案类型名称")
    @TableField(table = DocumentsType.class, field = "name")
    private String typeName;

    @ApiModelProperty(value="文案类型code")
    @TableField(table = DocumentsType.class, field = "code")
    private String type;

    @ApiModelProperty(value="内容")
    @TableField(table = DocumentsInfo.class, field = "content")
    private String content;

    @ApiModelProperty(value="国家code")
    @TableField(table = DocumentsInfo.class, field = "countryId")
    private Long countryId;

    @ApiModelProperty(value="渠道code")
    @TableField(table = DocumentsInfo.class, field = "channelCode")
    private String channelCode;

    @ApiModelProperty(value="活动名称")
    @TableField(table = Activity.class, field = "name")
    private String activityName;


    @ApiModelProperty(value="活动id")
    @TableField(table = Activity.class, field = "id")
    private String activityId;

    @ApiModelProperty(value="修改日期")
    @TableField(table = DocumentsInfo.class, field = "modTime")
    private Date modTime;

    @ApiModelProperty(value="语种")
    @TableField(table = DocumentsInfo.class, field = "localeCode")
    private String localeCode;
}
