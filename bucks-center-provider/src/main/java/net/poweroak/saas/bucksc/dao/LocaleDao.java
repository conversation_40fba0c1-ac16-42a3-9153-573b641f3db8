package net.poweroak.saas.bucksc.dao;

import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.exception.ApplicationRuntimeException;
import net.poweroak.framework.data.mybatis.GenericRepository;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.framework.util.StringUtils;
import net.poweroak.saas.bucksc.BucksCenterError;
import net.poweroak.saas.bucksc.mapper.FaqMapper;
import net.poweroak.saas.bucksc.mapper.LocaleMapper;
import net.poweroak.saas.bucksc.model.PO.BuckLocale;
import net.poweroak.saas.bucksc.model.PO.DocumentsInfo;
import net.poweroak.saas.bucksc.model.PO.DocumentsType;
import net.poweroak.saas.bucksc.model.PO.Faq;
import net.poweroak.saas.bucksc.model.TO.DocumentsLocalePageTO;
import net.poweroak.saas.bucksc.model.VO.DocumentsInfoVO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2023/5/29 15:33
 * @description
 */
@Repository
@Slf4j
public class LocaleDao extends GenericRepository<BuckLocale, LocaleMapper, String> {

    @Resource
    private DocumentsInfoDao documentsInfoDao;

    public LocaleDao(LocaleMapper genericMapper) {
        super(genericMapper);
    }

    public Pagination<BuckLocale> pageList(DocumentsLocalePageTO request) {
        MybatisQuery mybatisQuery=new MybatisQuery();
        if(StringUtils.isNotEmpty(request.getLocaleCode())){
            mybatisQuery.addCriterion(Criterion.where(BuckLocale::getLocaleCode).regex(request.getLocaleCode()));
        }
        if(StringUtils.isNotEmpty(request.getLocaleName())){
            mybatisQuery.addCriterion(Criterion.where(BuckLocale::getLocaleName).regex(request.getLocaleName()));
        }
        mybatisQuery.addCriterion(Criterion.where(BuckLocale::getEnable).is(1));
        return this.find(mybatisQuery,request.getPageNumber(), request.getPageSize());
    }

    public Boolean add(BuckLocale request) {
        if(StringUtils.isEmpty(request.getLocaleName())){
            log.error("语种名称为空");
            throw new ApplicationRuntimeException(BucksCenterError.LOCALE_NAME_CANNOT_BE_EMPTY);
        }
        if(StringUtils.isEmpty(request.getLocaleCode())){
            log.error("语种代码为空");
            throw new ApplicationRuntimeException(BucksCenterError.LOCALE_CODE_CANNOT_BE_EMPTY);
        }

        MybatisQuery mybatisQuery=new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(BuckLocale::getEnable).is(1));
        mybatisQuery.addCriterion(Criterion.where(BuckLocale::getLocaleCode).is(request.getLocaleCode()));
        BuckLocale one = this.findOne(mybatisQuery);
        if(one!=null){

            log.error("语种代码重复");
            throw new ApplicationRuntimeException(BucksCenterError.LOCALE_CODE_CANNOT_BE_REPEATED);

        }

        BuckLocale buckLocale=new BuckLocale();
        buckLocale.setLocaleName(request.getLocaleName());
        buckLocale.setLocaleCode(request.getLocaleCode());
        buckLocale.create();
        this.insert(buckLocale);
        return true;


    }

    public BuckLocale getOne(String id) {
        Optional<BuckLocale> buckLocale = this.findById(id);
        if (buckLocale.isPresent()) {
            return buckLocale.get();
        } else {
            log.error("未查询到语种详情数据，id={}", id);
            throw new ApplicationRuntimeException(BucksCenterError.THE_LOCALE_DETAILS_DATA_WERE_NOT_QUERIED);
        }
    }

    public Boolean updateLocale(BuckLocale request) {
        MybatisQuery query=new MybatisQuery();
        query.addCriterion(Criterion.where(BuckLocale::getEnable).is(1));
        query.addCriterion(Criterion.where(BuckLocale::getLocaleCode).is(request.getLocaleCode()));
        query.addCriterion(Criterion.where(BuckLocale::getId).ne(request.getId()));
        BuckLocale one = this.findOne(query);
        if(one!=null){

            log.error("语种代码重复");
            throw new ApplicationRuntimeException(BucksCenterError.LOCALE_CODE_CANNOT_BE_REPEATED);

        }

        MybatisQuery mybatisQuery=new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(BuckLocale::getId).is(request.getId()));
        mybatisQuery.addUpdateItem(BuckLocale::getLocaleName,request.getLocaleName());
        mybatisQuery.addUpdateItem(BuckLocale::getLocaleCode,request.getLocaleCode());
        this.update(mybatisQuery);;
        return true;
    }

    public List<BuckLocale> list() {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(BuckLocale::getEnable).is(1));
        List<BuckLocale> buckLocales = this.find(query);
        return (buckLocales);

    }


    public Boolean deleteLocale(String id) {
        Optional<BuckLocale> byId = this.findById(id);
        if(byId.isPresent()){
            BuckLocale locale=byId.get();
            MybatisQuery query1=new MybatisQuery();
            query1.addCriterion(Criterion.where(DocumentsInfo::getEnable).is(1));
            query1.addCriterion(Criterion.where(DocumentsInfo::getLocaleCode).is(locale.getLocaleCode()));
            DocumentsInfo one = this.documentsInfoDao.findOne(query1);
            if(one!=null){
                log.error("存在该语种的文案信息,请先删除文案信息");
                throw new ApplicationRuntimeException(BucksCenterError.THERE_IS_A_LOCALE_FOR_THIS_DOCUMENTS_TYPE);
            }
        }

        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(BuckLocale::getId).is(id));
        query.addUpdateItem(BuckLocale::getEnable, 0);
        this.update(query);
        return true;
    }
}
