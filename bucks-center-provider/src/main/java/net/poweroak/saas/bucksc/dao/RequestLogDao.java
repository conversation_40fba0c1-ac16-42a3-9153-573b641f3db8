package net.poweroak.saas.bucksc.dao;

import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.data.mybatis.GenericRepository;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.framework.enums.OrderBy;
import net.poweroak.saas.bucksc.common.ActivityEarnCode;
import net.poweroak.saas.bucksc.mapper.RequestLogMapper;
import net.poweroak.saas.bucksc.model.PO.RequestLog;
import net.poweroak.saas.bucksc.model.TO.RequestLogSearchTO;
import net.poweroak.saas.bucksc.model.VO.RequestLogListVO;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/5/9 10:32
 */
@Repository
@Slf4j
public class RequestLogDao extends GenericRepository<RequestLog, RequestLogMapper, String> {


    public RequestLogDao(RequestLogMapper requestLogMapper) {
        super(requestLogMapper);
    }


    /**
     * 签到详情列表
     *
     * @return
     */
    public Pagination<RequestLogListVO> getList(RequestLogSearchTO requestLogSearchTO) {
        MybatisQuery query = new MybatisQuery();
        if (!Strings.isEmpty(requestLogSearchTO.getRequestParams())) {
            query.addCriterion(Criterion.where(RequestLog::getRequestParams).regex(".*" + requestLogSearchTO.getRequestParams() + ".*"));
        }
        if (!Strings.isEmpty(requestLogSearchTO.getErrMsg())) {
            query.addCriterion(Criterion.where(RequestLog::getErrMsg).regex(".*" + requestLogSearchTO.getErrMsg() + ".*"));
        }
        query.orderBy(RequestLog::getCreTime, OrderBy.DESC);
        Pagination<RequestLog> pagination = this.find(query, requestLogSearchTO.getPageNumber(), requestLogSearchTO.getPageSize());
        List<RequestLogListVO> logListVOS = new ArrayList<>();
        if (pagination.getSize() > 0) {
            pagination.getContent().forEach(item -> {
                RequestLogListVO requestLogListVO = new RequestLogListVO();
                BeanUtils.copyProperties(item, requestLogListVO);
                requestLogListVO.setTypeName(ActivityEarnCode.getNameByRule(item.getType()));
                requestLogListVO.setId(item.getId());
                logListVOS.add(requestLogListVO);
            });
        }
        return new Pagination<>(logListVOS, pagination.getPageable(), pagination.getTotalElements());
    }


    public String add(String params, String errMsg, int type, int subType) {
        try {
            RequestLog requestLog = new RequestLog();
            requestLog.create();
            requestLog.setRequestParams(params);
            requestLog.setType(type);
            requestLog.setEnable(0);
            requestLog.setErrMsg(errMsg);
            requestLog.setSubType(subType);
            this.insert(requestLog);
            return requestLog.getId();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public void update(String id, String errMsg) {
        try {

            Optional<RequestLog> optionalRequestLog = this.findById(id);
            if (optionalRequestLog.isPresent()) {
                RequestLog requestLog = new RequestLog();
                requestLog.setId(id);
                requestLog.setErrMsg(errMsg);
                this.updateById(requestLog);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void delete(String id) {
        this.deleteById(id);
    }


    /**
     * 时间查询Shopify日志
     * @param dateFormat 多久前 YYYY-MM-dd HH:mm:ss
     * @return
     */
    public List<RequestLog> getShopifyLog(String dateFormat) {
        MybatisQuery query = new MybatisQuery()
                .addCriterion(Criterion.where(RequestLog::getEnable).is(Boolean.TRUE))
                .addCriterion(Criterion.where(RequestLog::getSourceType).is(1))
                .addCriterion(Criterion.where(RequestLog::getMqStatus).in(0,2))
                .addCriterion(Criterion.where(RequestLog::getCreTime).lte(dateFormat));
        return this.find(query);
    }


}
