package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/7 17:37
 * @description
 */
@Data
public class ChannelUpdateTO {
    @ApiModelProperty(value = "渠道名称")
    private String name;
    @ApiModelProperty(value = "渠道编码")
    private String code;
    @ApiModelProperty(value = "id")
    private String id;

}
