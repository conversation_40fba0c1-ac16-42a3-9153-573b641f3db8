package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created by zx on 2022/5/31 15:43
 */
@Data
public class AbnDataRuleTO {

    private String id;

    @ApiModelProperty(value = "规则类型(1-自定义 2-积分基础规则)")
    private Integer ruleType;

    @ApiModelProperty(value = "适用的积分消耗规则：1-积分发放；2-积分回退")
    private Integer abnType;

    @ApiModelProperty(value = "时间单位 1-分钟 2-小时  3-天 ")
    private Integer timeUnit;

    @ApiModelProperty(value = "单位时间次数")
    private Integer unitTimeCount;
}
