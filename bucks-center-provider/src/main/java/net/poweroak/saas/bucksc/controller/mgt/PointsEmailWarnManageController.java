package net.poweroak.saas.bucksc.controller.mgt;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.data.perms.norm.RouteMenu;
import net.poweroak.framework.pedestal.annotate.RequirePermission;
import net.poweroak.framework.pedestal.annotate.RouteAction;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.dao.PointsEmailWarnDao;
import net.poweroak.saas.bucksc.model.TO.*;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Created by zx on 2022/7/6 08:39
 */

@ApiIgnore
@RestController
@Api(tags = "邮件预警人员管理")
@RequestMapping("/" + RouteEndpoint.MGT + "/emailWarn")
@RouteMenu(module = "emailWarn", label = "邮件预警人员管理", parent = PointsRiskMgtController.class)
public class PointsEmailWarnManageController {

    @Resource
    private PointsEmailWarnDao emailWarnDao;

    @PostMapping("/page")
    @RouteAction(action = "pageList",id = "181d0ffc406f79115229d154947", label = "邮件预警人员管理", view = "/BluBucksC/risk/email/list.vue", linkToModule = true)
    @RequirePermission
    public UnifyResponse<Pagination> listByPage(@RequestBody EmailWarnPageTO pageTO) {
        return new UnifyResponse(emailWarnDao.page(pageTO));
    }

    @PostMapping("/add")
    @RouteAction(action = "add",id = "182334cebbef79115229d154abd", label = "新增")
    @RequirePermission
    public UnifyResponse<Void> add(@RequestBody @Valid PointsEmailWarnTO emailWarnTO) {
        emailWarnDao.add(emailWarnTO);
        return new UnifyResponse();
    }

    @PostMapping("/update")
    @RouteAction(action = "update",id = "182334c6fedf79115229d154abb", label = "编辑")
    @RequirePermission
    public UnifyResponse<Void> update(@RequestBody @Valid PointsEmailWarnTO emailWarnTO) {
        emailWarnDao.update(emailWarnTO);
        return new UnifyResponse();
    }

    @ApiOperation(value = "启用、禁用 通知")
    @GetMapping("/enableOrDisable/{warnId}")
    @RouteAction(action = "enableOrDisable",id = "182334b358ef79115229d154ab7", label = "邮件启用、禁用 通知")
    @RequirePermission
    public UnifyResponse<Void> enableOrDisable(@PathVariable("warnId") String  warnId) {
        emailWarnDao.enableOrDisable(warnId);
        return new UnifyResponse();
    }


    @ApiOperation(value = "删除")
    @GetMapping("/del/{warnId}")
    @RouteAction(action = "del",id = "182334c1ff5f79115229d154aba", label = "邮件删除")
    @RequirePermission
    public UnifyResponse<Void> del(@PathVariable("warnId") String  warnId) {
        emailWarnDao.del(warnId);
        return new UnifyResponse();
    }


    @PostMapping("/sendMail")
    public UnifyResponse<Void> sendMail(@RequestBody EmailWarnTO emailWarnTO){
        emailWarnDao.sendMail(emailWarnTO);
        return new UnifyResponse();
    }

}
