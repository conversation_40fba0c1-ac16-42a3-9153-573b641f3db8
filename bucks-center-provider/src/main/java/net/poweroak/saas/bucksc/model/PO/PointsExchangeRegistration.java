package net.poweroak.saas.bucksc.model.PO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.DomainEntity;
import net.poweroak.framework.sqltag.Table;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2023/11/3
 */
@Data
@ApiModel("积分兑换登记")
@Table(name = "points_exchange_registration")
public class PointsExchangeRegistration extends DomainEntity<String> {
    @ApiModelProperty(value = "用户uid")
    private String uid;

    @ApiModelProperty(value = "用户提交邮箱")
    private String email;

    @ApiModelProperty(value = "购买平台")
    private String platform;

    @ApiModelProperty(value = "其他平台")
    private String otherPlatform;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty(value = "购买产品型号")
    private String model;

    @ApiModelProperty(value = "订单金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "状态（1:待审核，2：已发放，3：不发放）")
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String comment;

    @ApiModelProperty(value = "系统校验结果")
    private String verify;

    @ApiModelProperty(value = "用户注册国家的编码")
    private String countryCode;

    @ApiModelProperty(value = "用户的姓名")
    private String name;

    @ApiModelProperty(value = "用户账号邮箱")
    private String accountEmail;

    @ApiModelProperty(value = "用户账号电话")
    private String phone;

    @ApiModelProperty(value = "发放人员")
    private String operator;

    @ApiModelProperty(value = "发放人员姓名")
    private String operatorName;

    @ApiModelProperty(value = "发放时间")
    private Date operateTime;

    @ApiModelProperty(value = "发放积分数")
    private Integer distributePoints;

    @ApiModelProperty(value = "发放国家")
    private Long distributeCountryId;
}
