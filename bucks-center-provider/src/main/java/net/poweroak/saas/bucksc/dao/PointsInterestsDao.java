package net.poweroak.saas.bucksc.dao;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.data.mybatis.GenericRepository;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.saas.bucksc.mapper.PointsInterestsMapper;
import net.poweroak.saas.bucksc.model.PO.PointsInterests;
import net.poweroak.saas.bucksc.model.TO.PointsInterestsPageTO;
import net.poweroak.saas.bucksc.model.VO.PointsInterestsVO;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2024/4/30
 */
@Slf4j
@Repository
public class PointsInterestsDao extends GenericRepository<PointsInterests, PointsInterestsMapper, String> {
    public PointsInterestsDao(PointsInterestsMapper genericMapper) {
        super(genericMapper);
    }

    public Pagination<PointsInterestsVO> page(PointsInterestsPageTO params) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsInterests::getEnable).is(1))
                .addCriterion(Criterion.where(PointsInterests::getCountryId).is(params.getCountryId()), () -> null != params.getCountryId())
                .join(PointsInterestsVO.class);
        return this.findByJoin(query, params);
    }

    public PointsInterests byId(String id) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsInterests::getEnable).is(1))
                .addCriterion(Criterion.where(PointsInterests::getId).is(id));
        return this.findOne(query);
    }

    public List<PointsInterestsVO> voByIdListAndCountry(List<String> idList, Long countryId) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsInterests::getEnable).is(1))
                .addCriterion(Criterion.where(PointsInterests::getId).in(idList), () -> CollectionUtil.isNotEmpty(idList))
                .addCriterion(Criterion.where(PointsInterests::getCountryId).is(countryId))
                .addCriterion(Criterion.where(PointsInterests::getEffectiveStart).lte(DateUtil.formatDateTime(new Date())))
                .addCriterion(Criterion.where(PointsInterests::getEffectiveEnd).gte(DateUtil.formatDateTime(new Date())))
                .join(PointsInterestsVO.class);
        return this.findByJoin(query);
    }
}
