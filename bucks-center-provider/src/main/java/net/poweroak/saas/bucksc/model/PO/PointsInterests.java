package net.poweroak.saas.bucksc.model.PO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.DomainEntity;
import net.poweroak.framework.sqltag.Table;

import java.util.Date;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2024/4/30
 */
@Data
@ApiModel("积分权益")
@Table(name = "points_interests")
public class PointsInterests extends DomainEntity<String> {
    @ApiModelProperty(value = "国家Id")
    private Long countryId;

    @ApiModelProperty(value = "权益名称")
    private String name;

    @ApiModelProperty(value = "权益图标链接")
    private String iconUrl;

    @ApiModelProperty(value = "权益介绍")
    private String instructions;

    @ApiModelProperty(value = "起始有效期")
    private Date effectiveStart;

    @ApiModelProperty(value = "结束有效期")
    private Date effectiveEnd;
}
