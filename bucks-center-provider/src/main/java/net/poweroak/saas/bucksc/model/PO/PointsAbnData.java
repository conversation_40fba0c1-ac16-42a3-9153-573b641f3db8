package net.poweroak.saas.bucksc.model.PO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.DomainEntity;
import net.poweroak.framework.sqltag.Table;

import java.util.Date;

/**
 * Created by zx on 2022/5/31 11:14
 */

@Data
@ApiModel("积分异常数据")
@Table(name = "points_abn_data")
public class PointsAbnData extends DomainEntity<String> {


    @ApiModelProperty(value = "渠道标识")
    private String channelCode;

    @ApiModelProperty(value = "活动ID")
    private String activityId;

    @ApiModelProperty(value = "积分值")
    private Integer points;

//    @ApiModelProperty(value = "国家编码")
//    private String countryCode;

    @ApiModelProperty(value="国家Id")
    private Long countryId;

    @ApiModelProperty(value = "适用的积分消耗规则：1-积分发放；2-积分回退")
    private Integer abnType;

    @ApiModelProperty(value = "异常记录时间")
    private Date abnTime;

    @ApiModelProperty(value = "用户ID")
    private String uid;

    @ApiModelProperty(value = "积分流水ID")
    private String journalId;

    @ApiModelProperty(value = "订单ID")
    private String orderId;

    @ApiModelProperty(value = "异常说明")
    private String desc;




}
