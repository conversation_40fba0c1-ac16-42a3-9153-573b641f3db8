package net.poweroak.saas.bucksc.constants.mq;

import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Mailto <EMAIL>
 * @Create 2024/5/9 11:02
 **/
@Configuration
public class RmqConfig {

    public static final String DLK_EXCHANGE = "bucks_dlk.exchange";
    public static final String DLK_ROUTEKEY = "bucks_dlk.routeKey";
    public static final String DLK_QUEUE = "bucks_dlk.queue";

    public static final String BUCKS_EXCHANGE = "bucks.exchange";
    public static final String BUCKS_QUEUE = "bucks.queue";
    public static final String BUCKS_ROUTEKEY = "bucks.routeKey";


    @Bean
    public DirectExchange bucksExchange(){
        return new DirectExchange(BUCKS_EXCHANGE,true,false);
    }

    @Bean
    public Queue bucksQueue(){
        //只需要在声明业务队列时添加x-dead-letter-exchange，值为死信交换机
        Map<String,Object> map = new HashMap<>(1);
        map.put("x-dead-letter-exchange",DLK_EXCHANGE);
        //该参数x-dead-letter-routing-key可以修改该死信的路由key，不设置则使用原消息的路由key
        map.put("x-dead-letter-routing-key",DLK_ROUTEKEY);
        return new Queue(BUCKS_QUEUE,true,false,false,map);
    }

    @Bean
    public Binding bucksBind(){
        return BindingBuilder.bind(bucksQueue()).to(bucksExchange()).with(BUCKS_ROUTEKEY);
    }


}
