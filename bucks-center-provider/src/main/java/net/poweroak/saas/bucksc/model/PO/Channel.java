package net.poweroak.saas.bucksc.model.PO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.DomainEntity;
import net.poweroak.framework.sqltag.Table;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/7 16:10
 * @description 来源渠道
 */
@Data
@ApiModel("积分来源渠道")
@Table(name = "channel")
public class Channel extends DomainEntity<String> {

    /**
     * 渠道名称
     */
    @ApiModelProperty(value = "渠道名称")
    private String name;

    /**
     * 渠道标识
     */
    @ApiModelProperty(value = "渠道标识")
    private String code;

    /**
     * 渠道类型
     */
    @ApiModelProperty(value = "渠道类型 1-appkey 2-店铺")
    private Integer type;


}
