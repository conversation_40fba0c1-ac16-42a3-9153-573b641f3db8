package net.poweroak.saas.bucksc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.text.StrJoiner;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.data.user.ContextUtils;
import net.poweroak.framework.api.exception.ApplicationRuntimeException;
import net.poweroak.framework.user.UserInfoForServer;
import net.poweroak.saas.bucksc.BucksCenterError;
import net.poweroak.saas.bucksc.dao.PointsInterveneDao;
import net.poweroak.saas.bucksc.enums.GenType;
import net.poweroak.saas.bucksc.model.PO.PointsIntervene;
import net.poweroak.saas.bucksc.model.TO.PointsEarnSendTO;
import net.poweroak.saas.bucksc.model.TO.PointsInterveneExcelTO;
import net.poweroak.saas.bucksc.model.VO.PointsEarnCommonVO;
import net.poweroak.saas.bucksc.model.VO.PointsInterveneExcelVO;
import net.poweroak.saas.bucksc.model.excel.PointsInterveneExcel;
import net.poweroak.saas.bucksc.request.PointsSpendRequest;
import net.poweroak.saas.bucksc.response.PointsSpendResponse;
import net.poweroak.saas.bucksc.service.IPointsChange;
import net.poweroak.saas.bucksc.service.IPointsEarnService;
import net.poweroak.saas.bucksc.service.PointsInterveneService;
import net.poweroak.saas.bucksc.utils.EasyExcelUtils;
import net.poweroak.saas.uc.client.UserClient;
import net.poweroak.saas.uc.request.UcMemberPageRequest;
import net.poweroak.saas.uc.request.UserTO;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
@RequiredArgsConstructor
public class PointsInterveneServiceImpl implements PointsInterveneService {

    private final IPointsEarnService pointsEarnService;
    private final IPointsChange pointsChange;
    private final UserClient userClient;
    private final PointsInterveneDao pointsInterveneDao;

    @Override
    public void downloadImportTemplate(HttpServletResponse response) throws IOException {
        TimeInterval timer = DateUtil.timer();

        String fileName = "points_import";
        EasyExcelUtils.excelWriteResponse(fileName, response);

        EasyExcel.write(response.getOutputStream(), PointsInterveneExcel.class)
                .head(PointsInterveneExcel.class)
                .sheet("Points")
                .doWrite(new ArrayList<>());

        log.info("导出下载导入模板 花费{}秒", timer.intervalSecond());
    }

    @Override
    public void importPoints(PointsInterveneExcelTO pointsInterveneExcelTO, HttpServletResponse response) throws IOException {
        TimeInterval timer = DateUtil.timer();

        if (Strings.isEmpty(pointsInterveneExcelTO.getFile().getOriginalFilename())) {
            throw new ApplicationRuntimeException(BucksCenterError.FILE_IS_EMPTY);
        }

        List<PointsInterveneExcel> list = new ArrayList<>();
        try {
            EasyExcel.read(pointsInterveneExcelTO.getFile().getInputStream(), PointsInterveneExcel.class,
                    new PageReadListener<PointsInterveneExcel>(list::addAll)).sheet().headRowNumber(1).doRead();
        } catch (IOException e) {
            log.error("批量导入积分模板文件解析失败！");
            throw new ApplicationRuntimeException(BucksCenterError.MATERIAL_IMPORT_FILE_IS_ERROR);
        }
        if (CollUtil.isEmpty(list)) {
            throw new ApplicationRuntimeException(BucksCenterError.MATERIAL_IMPORT_FILE_IS_ERROR);
        }

        List<PointsInterveneExcelVO> result = new ArrayList<>();

        for (PointsInterveneExcel excel : list) {
            if (BeanUtil.isEmpty(excel)) {
                continue;
            }

            log.info("{}", JSONUtil.toJsonStr(excel));
            PointsInterveneExcelVO pointsInterveneExcelVO = processingData(excel, pointsInterveneExcelTO);
            result.add(pointsInterveneExcelVO);
        }
        //写出数据
        String excelFileName = "Import_results";
        EasyExcelUtils.excelWriteResponse(excelFileName, response);
        EasyExcel.write(response.getOutputStream(), PointsInterveneExcelVO.class)
                .head(PointsInterveneExcelVO.class)
                .sheet("Points")
                .doWrite(result);

        log.info("导出批量导入积分结果 花费{}秒", timer.intervalSecond());
    }

    private PointsInterveneExcelVO processingData(PointsInterveneExcel excel, PointsInterveneExcelTO pointsInterveneExcelTO) {
        PointsInterveneExcelVO vo = new PointsInterveneExcelVO();
        StrJoiner errorMes = new StrJoiner(",");
        BeanUtil.copyProperties(excel, vo);
        PointsIntervene intervene = new PointsIntervene();
        if (StrUtil.isEmpty(excel.getEmail())) {
            vo.setImportResults("FAIL");
            vo.setFailureReason(BucksCenterError.BALANCE_IS_NOT_EXISTS.getMessage());
            return vo;
        }
        //根据邮箱查uid
        UcMemberPageRequest ucMemberPageRequest = new UcMemberPageRequest();
        UserTO userTO = new UserTO();
        userTO.setEmail(excel.getEmail());
        ucMemberPageRequest.setMember(userTO);
        ucMemberPageRequest.setPage(1);
        ucMemberPageRequest.setSize(1);
        Pagination<UserInfoForServer> userData = userClient.list(ucMemberPageRequest).getData();
        List<UserInfoForServer> userList = userData.getContent();
        if (CollUtil.isEmpty(userList)) {
            vo.setImportResults("FAIL");
            vo.setFailureReason(BucksCenterError.BALANCE_IS_NOT_EXISTS.getMessage());
            return vo;
        }
        Optional<UserInfoForServer> first = userList.stream().filter(user -> StrUtil.equals(user.getEmail(), vo.getEmail())).findFirst();
        if (!first.isPresent()) {
            errorMes.append(BucksCenterError.EMAIL_ERROR.getMessage());
        }
        if (StrUtil.isEmpty(excel.getPoints())) {
            errorMes.append(BucksCenterError.EARN_CANNOT_BE_EMPTY.getMessage());
        }
        //判断输入的数字是否合法
        if (!NumberUtil.isInteger(excel.getPoints()) || Integer.parseInt(excel.getPoints()) <= 0) {
            errorMes.append(BucksCenterError.THE_INTEGRAL_ENTERED_IS_INCORRECT.getMessage());
        }
        if (StrUtil.isNotEmpty(errorMes.toString())) {
            vo.setImportResults("FAIL");
            vo.setFailureReason(errorMes.toString());
            return vo;
        }
        UserInfoForServer user = first.get();
        //发放
        if (pointsInterveneExcelTO.getGenType() == GenType.EARN.getStatusCode()) {
            PointsEarnSendTO to = new PointsEarnSendTO();
            to.setPoints(Integer.valueOf(excel.getPoints()));
            to.setChannelCode(pointsInterveneExcelTO.getChannelCode());
            to.setCountryId(pointsInterveneExcelTO.getCountryId());
            to.setActivityCode(pointsInterveneExcelTO.getCode());
            to.setUid(user.getUid());
            PointsEarnCommonVO send = pointsEarnService.intervene(to);
            intervene.setJournalId(send.getJournalId());
        } else if (pointsInterveneExcelTO.getGenType() == GenType.SPEND.getStatusCode()) {
            //消耗
            PointsSpendRequest spend = new PointsSpendRequest();
            spend.setPoints(Integer.valueOf(excel.getPoints()));
            spend.setChannelCode(pointsInterveneExcelTO.getChannelCode());
            spend.setCountryId(pointsInterveneExcelTO.getCountryId());
            spend.setActivityCode(pointsInterveneExcelTO.getCode());
            spend.setUid(user.getUid());
            PointsSpendResponse response = pointsChange.other(spend);
            intervene.setJournalId(response.getJournalId());
        }
        intervene.setTime(new Date());
        intervene.setUid(ContextUtils.get().getUID());
        intervene.setReason(pointsInterveneExcelTO.getReason());
        intervene.create();
        this.pointsInterveneDao.insert(intervene);
        vo.setImportResults("OK");
        return vo;
    }

}
