package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Builder
public class PointsInterveneExcelTO {

    @ApiModelProperty(value = "活动code")
    @NotBlank(message = "活动code不能为空")
    private String code;

    @ApiModelProperty(value = "渠道编码")
    @NotBlank(message = "渠道编码不能为空")
    private String channelCode;

    @ApiModelProperty(value = "国家编码")
    @NotNull(message = "国家编码不能为空")
    private Long countryId;

    @ApiModelProperty(value = "类型 1-增加；2-消耗")
    @NotNull(message = "类型不能为空")
    private Integer genType;

    @ApiModelProperty(value = "原因")
    private String reason;

    @ApiModelProperty(value = "文件")
    private MultipartFile file;
}
