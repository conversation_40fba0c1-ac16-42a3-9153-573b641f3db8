package net.poweroak.saas.bucksc.model.TO;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2024/5/7
 */
@Data
public class PointsInterestsTO {
    private String id;

    @ApiModelProperty(value = "国家Id")
    @NotNull
    private Long countryId;

    @ApiModelProperty(value = "权益名称")
    @NotBlank
    private String name;

    @ApiModelProperty(value = "权益图标链接")
    @NotBlank
    private String iconUrl;

    @ApiModelProperty(value = "权益介绍")
    @NotBlank
    private String instructions;

    @ApiModelProperty(value = "起始有效期")
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date effectiveStart;

    @ApiModelProperty(value = "结束有效期")
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date effectiveEnd;
}
