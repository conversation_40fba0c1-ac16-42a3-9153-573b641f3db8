package net.poweroak.saas.bucksc.shedule;

import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.util.ApplicationUtil;
import net.poweroak.framework.cache.lock.DistributedLock;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.saas.bucksc.dao.PointsAbnDataDao;
import net.poweroak.saas.bucksc.dao.PointsAbnDataRuleDao;
import net.poweroak.saas.bucksc.dao.PointsEmailWarnDao;
import net.poweroak.saas.bucksc.dao.PointsJournalDao;
import net.poweroak.saas.bucksc.enums.AbnRuleType;
import net.poweroak.saas.bucksc.enums.TimeUnitType;
import net.poweroak.saas.bucksc.model.PO.PointsAbnDataRule;
import net.poweroak.saas.bucksc.model.TO.EmailWarnTO;
import net.poweroak.saas.bucksc.model.VO.PointsAbnDataVO;
import net.poweroak.saas.bucksc.model.VO.PointsAccountVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by zx on 2022/6/1 17:12 异常数据扫描
 */

@Component
@Slf4j
public class AbnDataTask {

    public static final String TASK = String.join(":", ApplicationUtil.getApplicationAbbr(), "DLK", "ABN_DATA");

    @Resource
    private PointsAbnDataRuleDao dataRuleDao;
    @Resource
    private PointsAbnDataDao abnDataDao;
    @Resource
    private PointsJournalDao journalDao;
    @Resource
    private PointsEmailWarnDao emailWarnDao;

    /**
     * 1分钟执行一次
     */
   /* @Scheduled(cron = "0 0/1 * * * ?")*/
    public void abnDataTask() {

        try {

            final boolean lock = DistributedLock.getInstance().tryLock(TASK);

            if (lock) {

                log.info("-------------异常数据扫描---------------");
                //异常规则
                final List<PointsAbnDataRule> dataRules = dataRuleDao.find(MybatisQuery.where()
                        .addCriterion(Criterion.where(PointsAbnDataRule::getRuleType).is(AbnRuleType.CUSTOMIZE_EARN.getCode()))
                        .addCriterion(Criterion.where(PointsAbnDataRule::getEnable).is(true)));

                if (CollectionUtils.isNotEmpty(dataRules)) {

                    //根据积分流水统计积分账户
                    final List<PointsAccountVO> accountList = journalDao.findAccount();

                    dataRules.forEach(dataRule -> {

                            //积分发放
                            if (AbnRuleType.CUSTOMIZE_EARN.getCode() == dataRule.getAbnType()) {

                                accountList.forEach(accountVO -> {

                                    //查询单个账户异常数据
                                    List<PointsAbnDataVO> list = journalDao.findAbnDataListByAccount(accountVO, second(dataRule.getTimeUnit()), 1);

                                    //异常数据条数在单位时间是否超过当前限制
                                    if (dataRule.getUnitTimeCount() != null && dataRule.getUnitTimeCount() <= list.size()) {

                                        list.forEach(abnData -> {
                                            abnData.setAbnType(AbnRuleType.CUSTOMIZE_EARN.getCode());
                                            abnData.setDesc("定时任务根据异常规则扫描出积分发放异常数据");
                                            abnDataDao.earlyWarn(abnData);

                                        });
                                        //邮件通知
                                        emailWarnDao.sendMail(new EmailWarnTO(accountVO.getUid(),"定时任务根据异常规则扫描出积分发放异常数据"));
                                    }
                                });
                            }

                            //积分回退
                            else {
                                accountList.forEach(accountVO -> {

                                    //查询单个账户异常数据
                                    List<PointsAbnDataVO> list = journalDao.findAbnDataListByAccount(accountVO, second(dataRule.getTimeUnit()), 3);

                                    //异常数据条数在单位时间是否超过当前限制
                                    if (dataRule.getUnitTimeCount() != null && dataRule.getUnitTimeCount() <= list.size()) {
                                        list.forEach(abnData -> {
                                            abnData.setAbnType(AbnRuleType.RULE_BACK.getCode());
                                            abnData.setDesc("定时任务根据异常规则扫描出积分回退异常数据");
                                            abnDataDao.earlyWarn(abnData);

                                        });

                                        //邮件通知
                                        emailWarnDao.sendMail(new EmailWarnTO(accountVO.getUid(),"定时任务根据异常规则扫描出积分回退异常数据"));
                                    }
                                });
                            }
                    });
                }
            }
        } finally {

            DistributedLock.getInstance().unlock(TASK);
        }

    }

    private Integer second(Integer timeUnit) {
        return timeUnit == TimeUnitType.MINUTE.getCode() ? 60 : (TimeUnitType.HOUR.getCode() == timeUnit ? 3600 : 86400);
    }
}
