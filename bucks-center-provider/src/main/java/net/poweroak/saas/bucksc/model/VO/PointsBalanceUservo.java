package net.poweroak.saas.bucksc.model.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.saas.bucksc.model.PO.PointsBalance;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/19 16:39
 * @description
 */
@Data
public class PointsBalanceUservo extends PointsBalance {
    private String username;
    private String email;
    private Boolean isInBlackList;

    @ApiModelProperty(value = "消费金额")
    private String amount;

    @ApiModelProperty(value = "币种")
    private String currency;
}
