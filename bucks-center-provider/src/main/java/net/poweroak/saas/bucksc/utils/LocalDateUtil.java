package net.poweroak.saas.bucksc.utils;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.iso.CountryCode;
import net.poweroak.iso.vo.country.CountryRich;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2024/6/14
 */
@Slf4j
public class LocalDateUtil {

    public static Date getLocalDate(Long countryId) {
        return getLocalDate(countryId, new Date());
    }

    public static Date getLocalDate(Long countryId, Date date) {
        String format = format(date, countryId);
        return DateUtil.parse(format);
    }


    public static DateTime getLocalDateTime(Long countryId) {
        return getLocalDateTime(countryId, new Date());
    }

    public static DateTime getLocalDateTime(Long countryId, Date date) {
        CountryRich countryRich = CountryCode.getByGeoId(countryId);
        CountryCode countryCode = CountryCode.getByAlpha2Code(countryRich.getCountryCode());
        DateTime dateTime = DateTime.of(date);
        dateTime.setTimeZone(countryCode.getTimeZone());
        return dateTime;
    }

    /**
     * 返回根据当地时区格式化后的时间字符串
     *
     * @return
     */
    public static String format(Date date, Long countryId) {
        CountryRich byGeoId = CountryCode.getByGeoId(countryId);
        CountryCode rich = CountryCode.getByAlpha2Code(byGeoId.getCountryCode());
        log.info("获取当地时间的国家为：{}", rich.getNameZh());
        SimpleDateFormat sdf = new SimpleDateFormat(DatePattern.NORM_DATETIME_PATTERN);
        sdf.setTimeZone(rich.getTimeZone());
        return sdf.format(date);
    }

    public static String format(Date date, Long countryId, String pattern) {
        CountryRich byGeoId = CountryCode.getByGeoId(countryId);
        CountryCode rich = CountryCode.getByAlpha2Code(byGeoId.getCountryCode());
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        sdf.setTimeZone(rich.getTimeZone());
        return sdf.format(date);
    }
}
