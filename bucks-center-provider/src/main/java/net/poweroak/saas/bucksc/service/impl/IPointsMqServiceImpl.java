package net.poweroak.saas.bucksc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.data.user.ContextUtils;
import net.poweroak.framework.api.data.user.UserContext;
import net.poweroak.framework.api.data.user.UserInfo;
import net.poweroak.framework.api.exception.ApplicationRuntimeException;
import net.poweroak.framework.util.StringUtils;
import net.poweroak.iso.CountryCode;
import net.poweroak.iso.vo.country.CountryRich;
import net.poweroak.saas.bucksc.BucksCenterError;
import net.poweroak.saas.bucksc.common.ActivityEarnCode;
import net.poweroak.saas.bucksc.common.GenSubType;
import net.poweroak.saas.bucksc.constants.mq.RmqMessageProducer;
import net.poweroak.saas.bucksc.dao.ActivityDao;
import net.poweroak.saas.bucksc.dao.ChannelDao;
import net.poweroak.saas.bucksc.dao.PointsJournalDao;
import net.poweroak.saas.bucksc.dao.RequestLogDao;
import net.poweroak.saas.bucksc.enums.GenType;
import net.poweroak.saas.bucksc.model.PO.Activity;
import net.poweroak.saas.bucksc.model.PO.Channel;
import net.poweroak.saas.bucksc.model.PO.PointsJournal;
import net.poweroak.saas.bucksc.model.PO.RequestLog;
import net.poweroak.saas.bucksc.model.TO.PointsEranCommTO;
import net.poweroak.saas.bucksc.request.*;
import net.poweroak.saas.bucksc.response.*;
import net.poweroak.saas.bucksc.service.IPointsChange;
import net.poweroak.saas.bucksc.service.IPointsEarnService;
import net.poweroak.saas.bucksc.service.IPointsMqService;
import net.poweroak.saas.bucksc.service.IPointsQueryService;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Mailto <EMAIL>
 * @Create 2024/5/9 15:29
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class IPointsMqServiceImpl implements IPointsMqService {

    private final IPointsQueryService pointsQueryServiceImpl;
    private final PointsJournalDao pointsJournalDao;
    private final ActivityDao activityDao;
    private final RmqMessageProducer rmqMessageProducer;
    private final RequestLogDao requestLogDao;
    private final IPointsEarnService earnPointsService;
    private final IPointsChange pointsChangeService;
    private final ChannelDao channelDao;


    @Override
    public ShopifyPointsQueryResponse getShopifyAccountInfo(IntegralQueryRequest request) {
        log.debug("Shopify积分账户信息查询：request = {}", JSONUtil.toJsonStr(request));

        checkParam(request.getCountryCode(), request.getChannelCode());

        ShopifyPointsQueryResponse response = new ShopifyPointsQueryResponse();

        // 调用老接口
        PointsQueryRequest pointsQueryRequest = new PointsQueryRequest();
        pointsQueryRequest.setUid(getUid());
        pointsQueryRequest.setCountryId(getCountryId(request.getCountryCode()));
        pointsQueryRequest.setChannelCode(request.getCountryCode());
        PointsBalanceTotalResponse pointsBalanceTotal = pointsQueryServiceImpl.querySummary(pointsQueryRequest);
        if (ObjectUtil.isEmpty(pointsBalanceTotal)) return response;

        // 返回数据处理
        response.setBucks(pointsBalanceTotal.getBalanceTotal());
        response.setEarnBucks(pointsBalanceTotal.getEarnTotal());
        response.setSpendBucks(pointsBalanceTotal.getSpendTotal());
        response.setValidBucks(pointsBalanceTotal.getValidTotal());

        return response;
    }

    @Override
    public Pagination<ShopifyBalanceJournalResponse> getBalanceJournalPage(IntegralPageRequest pageRequest){
        log.debug("Shopify积分明细分页查询：request = {}", JSONUtil.toJsonStr(pageRequest));

        checkParam(pageRequest.getCountryCode(), pageRequest.getChannelCode());
        checkTime(pageRequest.getBeginTime(),pageRequest.getEndTime());
        checkPageParam(pageRequest.getPageNo(), pageRequest.getPageSize());

        // 调用老接口
        PointsQueryJournalRequest request = new PointsQueryJournalRequest();
        BeanUtil.copyProperties(pageRequest,request);
        if (ObjectUtil.isNotEmpty(pageRequest.getBeginTime()) && ObjectUtil.isNotEmpty(pageRequest.getEndTime())) {
            request.setBeginTime(DatePattern.NORM_DATETIME_FORMAT.format(pageRequest.getBeginTime()));
            request.setEndTime(DatePattern.NORM_DATETIME_FORMAT.format(pageRequest.getEndTime()));
        }
        request.setCountryId(getCountryId(pageRequest.getCountryCode()));
        request.setOrderNo(pageRequest.getOrderNo());
        request.setUid(getUid());
        request.setPage(pageRequest.getPageNo());
        request.setSize(pageRequest.getPageSize());
        Pagination<PointsBalanceJournalResponse> journalPageRes = pointsQueryServiceImpl.getJournalPageRes(request);

        if (CollectionUtil.isEmpty(journalPageRes.getContent())){
            return new Pagination<>(new ArrayList<>(), journalPageRes.getPageable(),journalPageRes.getTotalElements());
        }

        // 返回数据
        List<PointsBalanceJournalResponse> content = journalPageRes.getContent();
        List<ShopifyBalanceJournalResponse> list = new ArrayList<>();
        for (PointsBalanceJournalResponse item : content) {
            ShopifyBalanceJournalResponse journal = new ShopifyBalanceJournalResponse();
            journal.setId(item.getId());
            journal.setAction(item.getActivityName());
            journal.setBucks(item.getBalance());
            journal.setGenTime(item.getGenTime());
            journal.setSymbol(item.getSymbol());
            journal.setOrderNo(item.getOrderNo());
            list.add(journal);
        }

        return new Pagination<>(list, journalPageRes.getPageable(),journalPageRes.getTotalElements());
    }

    @Override
    public ShopifyBalanceJournalResponse getBalanceJournalDetail(String id) {
        log.debug("Shopify积分明细查询：request = {}", id);

        getUid();

        if (StringUtils.isEmpty(id)) {
            throw new ApplicationRuntimeException(BucksCenterError.POINTS_PAGE_DETAIL_ID_EMPTY);
        }

        ShopifyBalanceJournalResponse response = new ShopifyBalanceJournalResponse();
        PointsJournal pointsJournal = pointsJournalDao.findById(id).orElse(null);
        if (ObjectUtil.isEmpty(pointsJournal)) return null;

        Map<String, String> actMap = activityDao.findByCountry(pointsJournal.getCountryId()).stream().collect(Collectors.toMap(Activity::getId, Activity::getName));

        // 参数处理
        response.setGenTime(DateUtil.format(pointsJournal.getGenTime(), DatePattern.NORM_DATETIME_FORMAT));
        response.setBucks(pointsJournal.getPoints());
        response.setOrderNo(pointsJournal.getOrderNo());
        response.setSymbol(pointsJournal.getGenType() == GenType.EARN.getStatusCode() ? 1 : 2);
        response.setId(pointsJournal.getId());

        String actName = actMap.get(pointsJournal.getActivityId());
        if (StringUtils.isNotEmpty(actName)) {
            response.setAction(actName);
        }

        return response;
    }



    @Override
    public void shopifyEarn(IntegralSpendRequest request) {
        log.debug("Shopify积分变动处理：request = {}", JSONUtil.toJsonStr(request));

        // 校验登录信息
        getUid();

        checkParam(request.getCountryCode(), request.getChannelCode());

        // 查询活动
        if (StringUtils.isEmpty(request.getAction())) {
            throw new ApplicationRuntimeException(BucksCenterError.POINTS_ACTIVITY_IS_EMPTY);
        }
        if (StringUtils.isEmpty(request.getOrderNo())) {
            throw new ApplicationRuntimeException(BucksCenterError.POINTS_ORDER_IS_EMPTY);
        }
        Activity activity = activityDao.findByActivityName(getCountryId(request.getCountryCode()), request.getAction());
        if (ObjectUtil.isEmpty(activity)) {
            throw new ApplicationRuntimeException(BucksCenterError.POINTS_ACTIVITY_IS_ERROR);
        }


        // 1、日志记录
        RequestLog requestLog = new RequestLog();
        requestLog.create();
        requestLog.setRequestParams(JSONUtil.toJsonStr(request));
        requestLog.setMqStatus(0);
        requestLog.setSourceType(1);
        requestLog.setType(ActivityEarnCode.SHOPIFY_ACTIVITY.getRuleType());
        // requestLog.setSubType(GenSubType.match(request.getGenSubType()).getStatusCode());
        requestLog.setSubType(GenSubType.OTHER.getStatusCode());
        requestLogDao.insert(requestLog);

        // 2、异步发送请求数据
        log.info("推送MQ信息：{}", requestLog.getId());
        rmqMessageProducer.send(requestLog.getId(),null);

    }

    @Override
    public Boolean processingMessages(String recordId){
        log.debug("获取MQ对应日志ID：request = {}", recordId);
        Boolean bool = Boolean.FALSE;
        IntegralSpendRequest spendRequestLog = null;

        // 1、获取对应的落库日志信息
        RequestLog requestLog = requestLogDao.findById(recordId).orElse(null);
        if (ObjectUtil.isEmpty(requestLog)){
            log.debug("未获取MQ对应日志数据：requestLogId = {}", recordId);
            return Boolean.FALSE;
        }

        try {
            // 2、解析数据
            String requestParams = requestLog.getRequestParams();
            IntegralSpendRequest spendRequest = JSONUtil.toBean(requestParams, IntegralSpendRequest.class);
            spendRequestLog = spendRequest;
            // 异步导致UID缺失处理
            String uid = ContextUtils.get().getUID();
            if (StringUtils.isEmpty(uid) && ObjectUtil.isNotEmpty(spendRequest) && StringUtils.isNotEmpty(spendRequest.getUid())) {
                UserInfo userInfo = new UserInfo();
                userInfo.setUid(spendRequest.getUid());
                UserContext userContext = new UserContext(userInfo);
                ContextUtils.set(userContext);
            } else if (StringUtils.isEmpty(uid)){
                requestLog.setMqStatus(2);
                requestLog.setErrMsg("未获取到用户信息");
                requestLogDao.updateById(requestLog);
                return Boolean.FALSE;
            }


            // 4、简单判断这条日志记录是否已经处理
            if (ObjectUtil.isNotEmpty(requestLog.getMqStatus()) && requestLog.getMqStatus().equals(1)) {
                return Boolean.TRUE;
            }

            // 5、请求通用接口
            ShopifyPointsResponse pointsEarnResponse = pointsSend(spendRequest);

            // 6、处理结果
            if (pointsEarnResponse.getIsSuccess()) {
                requestLog.setMqStatus(1);
                requestLog.setErrMsg("");
                bool = Boolean.TRUE;
            } else {
                requestLog.setMqStatus(2);
                requestLog.setErrMsg(pointsEarnResponse.getErrorMsg());
            }
            log.info("对应日志数据,处理结果：result = {},requestLogId = {}", bool ? "SUCCESS" : "FAIL",recordId);
            if (!bool){
                log.error("处理Shopify请求数据失败：err={}, requestLogId = {}, requestParam={}",requestLog.getErrMsg(),recordId,JSONUtil.toJsonStr(spendRequest));
            }
        } catch (Exception e) {
            log.error("处理Shopify请求数据失败：err={}, requestLogId = {}, requestParam={}",e.getMessage(),recordId,JSONUtil.toJsonStr(spendRequestLog));
            requestLog.setMqStatus(2);
            requestLog.setErrMsg(e.getMessage());
            e.printStackTrace();
        }
        requestLogDao.updateById(requestLog);
        return bool;
    }

    private ShopifyPointsResponse pointsSend(IntegralSpendRequest spendRequest) {
        ShopifyPointsResponse shopifyPointsResponse = new ShopifyPointsResponse();

        // 查询活动
        Activity activity = activityDao.findByActivityName(getCountryId(spendRequest.getCountryCode()), spendRequest.getAction());
        if (ObjectUtil.isEmpty(activity)) {
            shopifyPointsResponse.setIsSuccess(Boolean.FALSE);
            shopifyPointsResponse.setErrorMsg("活动不存在");
            return shopifyPointsResponse;
        }

        // 订单号是否已记录过积分
        PointsJournal byOrderNo = pointsJournalDao.findByOrderNo(spendRequest.getOrderNo());
        if (ObjectUtil.isNotEmpty(byOrderNo) && !Boolean.TRUE.equals(spendRequest.getIncrementalSymbol())) {
            shopifyPointsResponse.setIsSuccess(Boolean.FALSE);
            shopifyPointsResponse.setErrorMsg("订单积分已处理!");
            return shopifyPointsResponse;
        }

        // 校验国家、渠道
        checkParam(spendRequest.getCountryCode(),spendRequest.getChannelCode());


        // 积分大于0为发放积分
        if (spendRequest.getBucksSymbol()) {

            PointsEranCommTO pointsEranCommTO = new PointsEranCommTO();
            // 活动同步到表
            pointsEranCommTO.setActivityCode(activity.getCode());
            pointsEranCommTO.setActivityId(activity.getId());
            pointsEranCommTO.setChannelCode(spendRequest.getChannelCode());
            pointsEranCommTO.setCountryId(getCountryId(spendRequest.getCountryCode()));
            pointsEranCommTO.setAmount(spendRequest.getAmount());
            pointsEranCommTO.setPoints(spendRequest.getBucks());
            pointsEranCommTO.setOrderNo(spendRequest.getOrderNo());// 订单编码
            pointsEranCommTO.setAmountSymbol(spendRequest.getAmountSymbol());// 金额增减

            log.info("Shopify处理请求参数：{}", JSONUtil.toJsonStr(pointsEranCommTO));

            PointsEarnResponse pointsEarnResponse = earnPointsService.send(pointsEranCommTO, ActivityEarnCode.SHOPIFY_ACTIVITY, GenSubType.OTHER);

            log.info("Shopify新增积分返回参数：{}", JSONUtil.toJsonStr(pointsEarnResponse));

            if ((ObjectUtil.isNotEmpty(pointsEarnResponse) && StringUtils.isNotEmpty(pointsEarnResponse.getJournalId()))) {
                shopifyPointsResponse.setIsSuccess(Boolean.TRUE);
            } else {
                shopifyPointsResponse.setErrorMsg("处理数据异常!");
                shopifyPointsResponse.setIsSuccess(Boolean.FALSE);
            }
            shopifyPointsResponse.setPointsEarnResponse(pointsEarnResponse);
        }
        // 扣减积分：调用消耗接口
        else {

            // 构建消耗积分请求参数
            PointsSpendRequest pointsSpendRequest = new PointsSpendRequest();
            pointsSpendRequest.setResourceType(GenSubType.OTHER.getStatusCode());
            pointsSpendRequest.setOrderNo(spendRequest.getOrderNo());
            pointsSpendRequest.setActivityCode(activity.getCode());
            pointsSpendRequest.setChannelCode(spendRequest.getChannelCode());
            pointsSpendRequest.setCountryId(getCountryId(spendRequest.getCountryCode()));
            pointsSpendRequest.setPoints(spendRequest.getBucks());
            pointsSpendRequest.setAmount(spendRequest.getAmount());
            pointsSpendRequest.setActivityId(activity.getId());
            pointsSpendRequest.setAmountSymbol(spendRequest.getAmountSymbol());

            PointsSpendResponse response = pointsChangeService.openSpend(pointsSpendRequest);
            if ((ObjectUtil.isNotEmpty(response) && StringUtils.isNotEmpty(response.getJournalId()))) {
                shopifyPointsResponse.setIsSuccess(Boolean.TRUE);
            } else {
                shopifyPointsResponse.setErrorMsg("处理数据异常!");
                shopifyPointsResponse.setIsSuccess(Boolean.FALSE);
            }
            log.info("Shopify消耗积分返回参数：{}", JSONUtil.toJsonStr(response));
            shopifyPointsResponse.setPointsSpendResponse(response);
        }

        return shopifyPointsResponse;
    }





    private String getUid() {
        String uid = ContextUtils.get().getUID();
        if (uid == null) {
            throw new ApplicationRuntimeException(BucksCenterError.USER_IS_NOT_EXIST);
        }
        return uid;
    }

    private Long getCountryId(String countryCode) {
        Long countryId;
        if (countryCode == null) {
            String code = ContextUtils.get().getLocale().getCountry();
            if (Strings.isEmpty(code)) {
                throw new ApplicationRuntimeException(BucksCenterError.COUNTRY_CODE_CANNOT_BE_EMPTY);
            }
            CountryRich countryRich = CountryCode.getByCountryCode(code);
            countryId = countryRich.getGeoNameId();
        } else {
            CountryRich countryRich = CountryCode.getByCountryCode(countryCode);
            if (ObjectUtil.isEmpty(countryRich)) {
                throw new ApplicationRuntimeException(BucksCenterError.COUNTRY_CODE_CANNOT_BE_EMPTY);
            }
            countryId = countryRich.getGeoNameId();
        }
        return countryId;
    }

    private void checkParam(String countryCode,String channelCode) {
        if (StringUtils.isEmpty(countryCode)){
            throw new ApplicationRuntimeException(BucksCenterError.COUNTRY_CODE_CANNOT_BE_EMPTY);
        }
        if (StringUtils.isEmpty(channelCode)) {
            throw new ApplicationRuntimeException(BucksCenterError.POINTS_CHANNEL_CODE_EMPTY);
        }

        Channel byCode = channelDao.findByCode(channelCode);
        if (ObjectUtil.isEmpty(byCode)){
            throw new ApplicationRuntimeException(BucksCenterError.POINTS_CHANNEL_CODE_NOT_EXIST);
        }

        getCountryId(countryCode);

    }

    /**
     * 校验时间
     * @param beginTime
     * @param endTime
     */
    private void checkTime(Date beginTime, Date endTime) {

        if (ObjectUtil.isEmpty(beginTime) && ObjectUtil.isEmpty(endTime)) {
            return;
        } else if (ObjectUtil.isEmpty(beginTime) || ObjectUtil.isEmpty(endTime)){
            throw new ApplicationRuntimeException(BucksCenterError.POINTS_BEGIN_END_TIME_ERROR);
        }

        boolean after = beginTime.after(endTime);
        if (after) {
            throw new ApplicationRuntimeException(BucksCenterError.POINTS_BEGIN_TO_END_TIME_ERROR);
        }

    }

    private void checkPageParam(Integer page, Integer pageSize) {
        if (page == null || pageSize == null || page == 0 || pageSize == 0) {
            throw new ApplicationRuntimeException(BucksCenterError.POINTS_PAGE_PARAM_ERROR);
        }
        // 等于1框架默认给5
        if (pageSize < 2) {
            throw new ApplicationRuntimeException(BucksCenterError.POINTS_PAGE_SIZE_PARAM_ERROR);
        }
    }

}
