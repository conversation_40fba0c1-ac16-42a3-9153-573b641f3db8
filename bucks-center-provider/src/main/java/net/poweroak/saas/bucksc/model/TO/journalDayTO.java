package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.page.PageInfo;

import java.util.Date;
import java.util.List;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/17 9:56
 * @description
 */
@Data
public class journalDayTO extends PageInfo {
    @ApiModelProperty(value = "日期范围")
    private List<String> dates;

    @ApiModelProperty(value = "渠道code")
    private String channelCode;

    @ApiModelProperty(value = "活动id")
    private String activityId;
}
