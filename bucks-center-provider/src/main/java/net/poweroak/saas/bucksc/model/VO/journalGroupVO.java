package net.poweroak.saas.bucksc.model.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.saas.bucksc.model.PO.PointsJournal;

import java.math.BigDecimal;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/16 10:26
 * @description
 */
@Data
public class journalGroupVO extends PointsJournal {
    @ApiModelProperty(value = "持有人数")
    private Integer num;
    @ApiModelProperty(value = "总积分")
    private BigDecimal totalPoints;
    @ApiModelProperty(value = "渠道名称")
    private String channelName;
    @ApiModelProperty(value = "活动名称")
    private String activityName;



}
