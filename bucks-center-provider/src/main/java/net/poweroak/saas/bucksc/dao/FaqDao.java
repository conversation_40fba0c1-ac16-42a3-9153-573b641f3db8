package net.poweroak.saas.bucksc.dao;

import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.exception.ApplicationRuntimeException;
import net.poweroak.framework.data.mybatis.GenericRepository;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.framework.enums.OrderBy;
import net.poweroak.framework.util.StringUtils;
import net.poweroak.saas.bucksc.BucksCenterError;
import net.poweroak.saas.bucksc.mapper.FaqMapper;
import net.poweroak.saas.bucksc.model.PO.Faq;
import net.poweroak.saas.bucksc.model.TO.FaqPageTO;
import net.poweroak.saas.bucksc.model.TO.FaqSaveTO;
import net.poweroak.saas.bucksc.request.FaqRequest;
import net.poweroak.saas.bucksc.response.RulesResponse;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;


/**
 * <AUTHOR>
 * @date 2022/5/9 10:32
 */
@Repository
@Slf4j
public class FaqDao extends GenericRepository<Faq, FaqMapper, String> {


    public FaqDao(FaqMapper faqMapper) {
        super(faqMapper);
    }


    /**
     * 接口列表
     *
     * @return
     */
    public List<RulesResponse> getResList(FaqRequest request) {
        List<Faq> list = getList(request.getCountryId());
        List<RulesResponse> responseList = new ArrayList<>(list.size());
        list.forEach(item -> {
            RulesResponse rulesResponse = new RulesResponse();
            BeanUtils.copyProperties(item, rulesResponse);
            responseList.add(rulesResponse);
        });
        return responseList;
    }


    /**
     * 列表
     *
     * @return
     */
    public List<Faq> getList(Long countryId) {
        MybatisQuery query = new MybatisQuery();
        if (countryId != null) {
            query.addCriterion(Criterion.where(Faq::getCountryId).is(countryId));
        }
        query.addCriterion(Criterion.where(Faq::getEnable).is(1));
        return this.find(query);
    }


    /**
     * 列表
     *
     * @return
     */
    public Pagination<Faq> getPage(FaqPageTO faqPageTO) {
        MybatisQuery query = new MybatisQuery();
        if (!StringUtils.isEmpty(faqPageTO.getTitle())) {
            query.addCriterion(Criterion.where(Faq::getTitle).regex(".*" + faqPageTO.getTitle() + ".*"));
        }
        if (faqPageTO.getCountryId() != null) {
            query.addCriterion(Criterion.where(Faq::getCountryId).is(faqPageTO.getCountryId()));
        }
        query.orderBy(Faq::getCreTime, OrderBy.DESC);
        query.addCriterion(Criterion.where(Faq::getEnable).is(1));
        return this.find(query, faqPageTO.getPageNumber(), faqPageTO.getPageSize());
    }

    /**
     * 保存
     *
     * @param faqSaveTO
     */
    public void save(FaqSaveTO faqSaveTO) {
        Faq faq = null;
        if (Strings.isEmpty(faqSaveTO.getId())) {
            faq = new Faq();
            BeanUtils.copyProperties(faqSaveTO, faq);
            faq.create();
            this.insert(faq);
        } else {
            Optional<Faq> optionalFaq = this.findById(faqSaveTO.getId());
            if (optionalFaq.isPresent()) {
                faq = optionalFaq.get();
                BeanUtils.copyProperties(faqSaveTO, faq);
                faq.modify();
                this.updateById(faq);
            } else {
                throw new ApplicationRuntimeException(BucksCenterError.DATA_NOT_EXISTS);
            }
        }
    }

    public void delete(String id) {
        Optional<Faq> optionalFaq = this.findById(id);
        if (optionalFaq.isPresent()) {
            this.deleteById(id);
        }
    }
}
