package net.poweroak.saas.bucksc.shedule;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.cache.lock.DistributedLock;
import net.poweroak.saas.bucksc.constants.RedisConstant;
import net.poweroak.saas.bucksc.controller.mgt.PointsJournalByDaysController;
import net.poweroak.saas.bucksc.dao.RequestLogDao;
import net.poweroak.saas.bucksc.model.PO.RequestLog;
import net.poweroak.saas.bucksc.service.IPointsMqService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;


/**
 * 积分过期定时任务
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/17 9:50
 * @description
 */
@Component
@Slf4j
public class BucksScheduled {

    /**
     * 每天凌晨0时1分0秒执行
     */
    //@Scheduled(cron = "0 1 0 * * ?")
    //@Scheduled(cron = "0 0/1 * * * ?")

    /**
     * 每分钟执行一次
     */
    @XxlJob(value = "cleanValidBucksHandler", jobDesc = "积分系统-积分过期定时任务", cron = "0 0/1 * * * ? *")
    public void cleanValidBucksHandler() {
        String redisKey = RedisConstant.BUCKS_DAY_CLEAN;
        DistributedLock distributedLock = DistributedLock.getInstance();
        boolean locked = distributedLock.tryLock(redisKey);
        log.info("-------------清除过期积分---------------");
        try {
            if (locked) {
                controller.empty();
            } else {
                log.info("cleanValidBucks locked");
            }
        } catch (Exception e) {
            log.error("cleanValidBucks err, message:{}", e.getMessage());
            e.printStackTrace();
        } finally {
            distributedLock.unlock(redisKey);
        }
    }

    @Resource
    private PointsJournalByDaysController controller;
    @Resource
    private IPointsMqService iPointsMqServiceImpl;
    @Resource
    private RequestLogDao requestLogDao;


    @XxlJob(value = "executeRequestHandler", jobDesc = "SHOPIFY-MQ日志请求处理-定时任务", cron = "0 0 0,12 * * ? *")
    public void executeRequestHandler(){
        String redisKey = RedisConstant.SHOPIFY_POINTS_EXECUTE;
        DistributedLock distributedLock = DistributedLock.getInstance();
        boolean locked = distributedLock.tryLock(redisKey);

        log.info("-------------处理未执行的SHOPIFY MQ日志请求---------------");

        try {
            if (locked) {
                // 昨天前的未处理的数据
                String dateFormat = DatePattern.NORM_DATETIME_FORMAT.format(DateUtil.endOfDay(DateUtil.offsetDay(new Date(), -1)));
                List<RequestLog> shopifyLogList = requestLogDao.getShopifyLog(dateFormat);

                // 处理数据
                Optional.ofNullable(shopifyLogList).orElse(new ArrayList<>()).forEach(item->{
                    iPointsMqServiceImpl.processingMessages(item.getId());
                });

            } else {
                log.info("execute Request locked");
            }
        } catch (Exception e) {
            log.error("execute Request err, message:{}", e.getMessage());
            e.printStackTrace();
        } finally {
            distributedLock.unlock(redisKey);
        }
    }

}
