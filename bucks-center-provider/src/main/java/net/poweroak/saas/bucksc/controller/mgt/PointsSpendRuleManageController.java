package net.poweroak.saas.bucksc.controller.mgt;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.pedestal.annotate.RequirePermission;
import net.poweroak.framework.pedestal.annotate.RouteAction;
import net.poweroak.saas.bucksc.common.ActivitySpendCode;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.dao.PointsSpendRuleDao;
import net.poweroak.saas.bucksc.model.TO.SpendRulePageTO;
import net.poweroak.saas.bucksc.model.VO.RuleTypeVO;
import net.poweroak.saas.bucksc.model.VO.SpendRuleVO;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by zx on 2022/5/9 16:20
 */
@ApiIgnore
@RestController
@Api(tags = "积分消耗规则管理")
@RequestMapping("/" + RouteEndpoint.MGT + "/pointsSpendRule")
//@RouteMenu(module = "pointsSpendRule", label = "消耗规则管理", parent = BluBucksCenterApplication.class)
public class PointsSpendRuleManageController {

    @Resource
    private PointsSpendRuleDao pointsSpendRuleDao;

    @PostMapping("/page")
    @RouteAction(action = "pageList",id = "180a7f0507ff791157c8280ba5d", label = "积分消耗规则管理")
    @RequirePermission
    public UnifyResponse<Pagination> listByPage(@RequestBody SpendRulePageTO pageTO) {
        return new UnifyResponse(pointsSpendRuleDao.page(pageTO));
    }

    @PostMapping("/add")
    public UnifyResponse<Void> add(@RequestBody @Valid SpendRuleVO spendRuleVO) {
        pointsSpendRuleDao.update(spendRuleVO);
        return new UnifyResponse();
    }

    @PostMapping("/update")
    public UnifyResponse<Void> update(@RequestBody @Valid SpendRuleVO spendRuleVO) {
        pointsSpendRuleDao.update(spendRuleVO);
        return new UnifyResponse();
    }

    @ApiOperation(value = "启用、禁用")
    @GetMapping("/enableOrDisable/{ruleId}")
    public UnifyResponse<Void> enableOrDisable(@PathVariable("ruleId") String  ruleId) {
        pointsSpendRuleDao.enableOrDisable(ruleId);
        return new UnifyResponse();
    }

    @GetMapping("/ruleList")
    public UnifyResponse<List<RuleTypeVO>> ruleList() {
        ActivitySpendCode[] activitySpendCodes = ActivitySpendCode.values();
        List<RuleTypeVO> list = new ArrayList<>();
        for (ActivitySpendCode activitySpendCode : activitySpendCodes) {
            RuleTypeVO ruleTypeVO = new RuleTypeVO();
            ruleTypeVO.setCode(activitySpendCode.getCode());
            ruleTypeVO.setName(activitySpendCode.getName());
            ruleTypeVO.setRuleType(activitySpendCode.getRuleType() + "");
            list.add(ruleTypeVO);
        }
        return new UnifyResponse<>(list);
    }
}
