package net.poweroak.saas.bucksc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.data.user.ContextUtils;
import net.poweroak.framework.api.exception.ApplicationRuntimeException;
import net.poweroak.framework.user.UserInfoForServer;
import net.poweroak.framework.util.RegularUtils;
import net.poweroak.iso.CountryCode;
import net.poweroak.iso.vo.country.CountryRich;
import net.poweroak.saas.bucksc.BucksCenterError;
import net.poweroak.saas.bucksc.dao.PointsBalanceDao;
import net.poweroak.saas.bucksc.dao.PointsExchangeRegistrationDao;
import net.poweroak.saas.bucksc.enums.DistributeStatus;
import net.poweroak.saas.bucksc.enums.PlatformType;
import net.poweroak.saas.bucksc.model.PO.PointsExchangeRegistration;
import net.poweroak.saas.bucksc.model.TO.*;
import net.poweroak.saas.bucksc.model.VO.PointsExchangeRegistrationExportVO;
import net.poweroak.saas.bucksc.model.VO.PointsExchangeRegistrationVO;
import net.poweroak.saas.bucksc.model.VO.UserDefaultVO;
import net.poweroak.saas.bucksc.service.IPointsEarnService;
import net.poweroak.saas.bucksc.service.IPointsExchangeRegistrationService;
import net.poweroak.saas.uc.client.UserClient;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2023/11/6
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PointsExchangeRegistrationImpl implements IPointsExchangeRegistrationService {
    private final PointsExchangeRegistrationDao exchangeRegistrationDao;
    private final UserClient userClient;
    private final PointsBalanceDao pointsBalanceDao;
    private final IPointsEarnService pointsEarnService;
    private final PointsChangeImpl pointsChange;

    @Override
    public void add(PointsExchangeByOrderTO params) {
        if (!RegularUtils.matchEmailAddress(params.getEmail())) {
            throw new ApplicationRuntimeException(BucksCenterError.EMAIL_FORMAT_ERROR);
        }
        if (!PlatformType.toList().contains(params.getPlatform())) {
            throw new ApplicationRuntimeException(BucksCenterError.ILLEGAL_PARAMETER_PASSING, params.getPlatform());
        }
        if (!PlatformType.OTHER.getPlatform().equals(params.getPlatform())) {
            params.setOtherPlatform(null);
        } else if (Strings.isBlank(params.getOtherPlatform())) {
            throw new ApplicationRuntimeException(BucksCenterError.ILLEGAL_PARAMETER_PASSING, "其他平台不能为空");
        }
        String UID = ContextUtils.get().getUID();
        PointsExchangeRegistration pointsExchangeRegistration = BeanUtil.copyProperties(params, PointsExchangeRegistration.class);
        pointsExchangeRegistration.setStatus(DistributeStatus.REVIEW.getCode());
        pointsExchangeRegistration.setEnable(1);
        pointsExchangeRegistration.setUid(UID);
        if (exchangeRegistrationDao.checkOrderNumber(params)) {
            pointsExchangeRegistration.setVerify("订单号重复");
        }
        UserInfoForServer accountDetailVO = exchangeRegistrationDao.findAccountByUid(UID);
        pointsExchangeRegistration.setCountryCode(accountDetailVO.getCountry());
        pointsExchangeRegistration.setName(accountDetailVO.getNickname());
        pointsExchangeRegistration.setAccountEmail(accountDetailVO.getEmail());
        pointsExchangeRegistration.setPhone(accountDetailVO.getPhone());
        pointsExchangeRegistration.create();
        exchangeRegistrationDao.insert(pointsExchangeRegistration);
    }

    /**
     * 获取初始化信息(默认邮箱、币种)
     *
     * @return
     */
    @Override
    public UserDefaultVO initInfo() {
        UserDefaultVO userDefaultVO = new UserDefaultVO();
        if (pointsBalanceDao.findRegUserByUid(ContextUtils.get().getUID())) {
            UserInfoForServer userVO = userClient.list(Arrays.asList(ContextUtils.get().getUID())).getData().get(0);
            userDefaultVO.setEmail(userVO.getEmail());
            // 获取通用设置中的电价币种
//            if (!CollectionUtil.isEmpty(userVO.getExtraInfoMap())) {
//                userDefaultVO.setCurrency((String) userVO.getExtraInfoMap().get("electricCurrency"));
//            }
        }
        return userDefaultVO;
    }

    @Override
    public Pagination<PointsExchangeRegistrationVO> page(PointsExchangeRegistrationPageTO params) {
        return exchangeRegistrationDao.page(params);
    }

    /**
     * 不发放积分
     *
     * @param params
     */
    @Override
    public void refuse(PointsExchangeRegistrationRefuseTO params) {
        PointsExchangeRegistration exchangeRegistration = exchangeRegistrationDao.byId(params.getId());
        if (null == exchangeRegistration) {
            throw new ApplicationRuntimeException(BucksCenterError.REGISTRATION_NOT_EXISTS);
        }
        // 除了待审核状态的其他状态不能设为不发放状态
        if (!DistributeStatus.REVIEW.getCode().equals(exchangeRegistration.getStatus())) {
            throw new ApplicationRuntimeException(BucksCenterError.ERROR_STATUS,
                    DistributeStatus.getStatusByCode(exchangeRegistration.getStatus()) + "状态不能设置为不发放状态");
        }
        exchangeRegistration.setStatus(DistributeStatus.REFUSE.getCode());
        exchangeRegistration.setComment(params.getComment());
        exchangeRegistration.modify();
        exchangeRegistrationDao.updateById(exchangeRegistration);
    }

    /**
     * 发放积分
     *
     * @param params
     */
    @Override
    @Transactional
    public void issue(PointsExchangeRegistrationIssueTO params) {
        PointsExchangeRegistration exchangeRegistration = exchangeRegistrationDao.byId(params.getId());
        String UID = ContextUtils.get().getUID();
        if (null == exchangeRegistration) {
            throw new ApplicationRuntimeException(BucksCenterError.REGISTRATION_NOT_EXISTS);
        }
        // 除了待审核状态和不发放状态的其他状态不能发放
        if (!DistributeStatus.REVIEW.getCode().equals(exchangeRegistration.getStatus()) &&
                !DistributeStatus.REFUSE.getCode().equals(exchangeRegistration.getStatus())) {
            throw new ApplicationRuntimeException(BucksCenterError.ERROR_STATUS,
                    DistributeStatus.getStatusByCode(exchangeRegistration.getStatus()) + "状态不能发放");
        }
        exchangeRegistration.setStatus(DistributeStatus.ISSUED.getCode());
        exchangeRegistration.setComment(params.getComment());
        exchangeRegistration.setOperator(UID);

        // 从user表获取发放人员姓名
        String operatorName = UID;
        if (pointsBalanceDao.findRegUserByUid(UID)) {
            UserInfoForServer userVO = userClient.list(Arrays.asList(UID)).getData().get(0);
            if (Strings.isNotBlank(userVO.getDisplayName())) {
                operatorName = userVO.getDisplayName();
            }
        }

        exchangeRegistration.setOperatorName(operatorName);
        exchangeRegistration.setOperateTime(new Date());
        exchangeRegistration.setDistributePoints(params.getPoints());
        exchangeRegistration.setDistributeCountryId(params.getCountryId());
        exchangeRegistration.modify();
        exchangeRegistrationDao.updateById(exchangeRegistration);

        PointsEarnSendTO pointsEranCommTO = new PointsEarnSendTO();
        pointsEranCommTO.setUid(exchangeRegistration.getUid());
        pointsEranCommTO.setPoints(params.getPoints());
        pointsEranCommTO.setCountryId(params.getCountryId());
        // 发放积分
        pointsEarnService.registration(pointsEranCommTO, exchangeRegistration.getOrderNumber());
    }

    /**
     * 撤回已发放的积分
     *
     * @param id
     */
    @Override
    @Transactional
    public void retract(String id) {
        PointsExchangeRegistration exchangeRegistration = exchangeRegistrationDao.byId(id);
        if (null == exchangeRegistration) {
            throw new ApplicationRuntimeException(BucksCenterError.REGISTRATION_NOT_EXISTS);
        }
        // 除了已发放状态的其他状态不能撤回积分
        if (!DistributeStatus.ISSUED.getCode().equals(exchangeRegistration.getStatus())) {
            throw new ApplicationRuntimeException(BucksCenterError.ERROR_STATUS,
                    DistributeStatus.getStatusByCode(exchangeRegistration.getStatus()) + "状态不能撤回");
        }
        exchangeRegistration.setStatus(DistributeStatus.REVIEW.getCode());
        exchangeRegistration.modify();
        exchangeRegistrationDao.updateById(exchangeRegistration);

        // 撤回积分
        pointsChange.registrationPointsBack(exchangeRegistration);
    }

    /**
     * 导出表格
     *
     * @param params
     * @param response
     */
    @Override
    public void export(PointsExchangeRegistrationPageTO params, HttpServletResponse response) {
        String fileName = "PointsExchangeRegistration.xlsx";
        response.setContentType("application/vnd.ms-excel;");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

        List<PointsExchangeRegistrationExportVO> list = exchangeRegistrationDao.list(params);
        list.forEach(item -> {
            NumberFormat fmt = NumberFormat.getNumberInstance();
            fmt.setGroupingUsed(true);
            item.setCurrency(item.getCurrency() + " " + fmt.format(item.getAmount()));
            item.setStatusName(DistributeStatus.getStatusByCode(item.getStatus()));
            // 通过国家编码获取国家id
            if (null != item.getDistributeCountryId() && null != CountryCode.getByGeoId(item.getDistributeCountryId())) {
                item.setDistributeCountryCode(CountryCode.getByGeoId(item.getDistributeCountryId()).getCountryCode());
            }
        });
        try {
            ServletOutputStream out = response.getOutputStream();
            EasyExcel.write(out, PointsExchangeRegistrationExportVO.class).autoCloseStream(true).excelType(ExcelTypeEnum.XLSX).sheet("sheet0").doWrite(list);
            out.flush();
        } catch (IOException e) {
            throw new ApplicationRuntimeException(BucksCenterError.EXPORT_EXCEL_ERROR);
        }
    }

    /**
     * 导入表格
     *
     * @param file
     */
    @Override
    @Transactional
    public void importExcel(MultipartFile file) {
        List<PointsExchangeRegistrationIssueExcelTO> excelList = new ArrayList<>();
        try {
            EasyExcel.read(file.getInputStream(), PointsExchangeRegistrationIssueExcelTO.class, new PageReadListener<PointsExchangeRegistrationIssueExcelTO>(excelList::addAll)).sheet().doRead();
        } catch (Exception e) {
            log.error("解析导入库存的模板错误，err:{0}", e);
            throw new ApplicationRuntimeException(BucksCenterError.MATERIAL_IMPORT_FILE_IS_ERROR);
        }
        int i = 1;
        for (PointsExchangeRegistrationIssueExcelTO excelTO : excelList) {
            PointsExchangeRegistration pointsExchangeRegistration = exchangeRegistrationDao.getRegistration(excelTO);
            if (null == pointsExchangeRegistration) {
                throw new ApplicationRuntimeException(BucksCenterError.IMPORT_EXCEL_ERROR, "第" + i + "行数据不存在");
            }
            PointsExchangeRegistrationIssueTO issueTO = new PointsExchangeRegistrationIssueTO();
            issueTO.setId(pointsExchangeRegistration.getId());
            if (1 > excelTO.getDistributePoints()) {
                throw new ApplicationRuntimeException(BucksCenterError.ILLEGAL_PARAMETER_PASSING, "第" + i + "行积分必须为正整数");
            }
            issueTO.setPoints(excelTO.getDistributePoints());
            issueTO.setComment(excelTO.getComment());
            if (Strings.isBlank(excelTO.getDistributeCountryCode())) {
                throw new ApplicationRuntimeException(BucksCenterError.ILLEGAL_PARAMETER_PASSING, "第" + i + "行请输入正确的国家编码");
            }
            CountryRich countryRich;
            try {
                countryRich = CountryCode.getByCountryCode(excelTO.getDistributeCountryCode());
            } catch (Exception e) {
                throw new ApplicationRuntimeException(BucksCenterError.ILLEGAL_PARAMETER_PASSING, "第" + i + "行请输入正确的国家编码" + e.getMessage());
            }
            issueTO.setCountryId(countryRich.getGeoNameId());

            /* 调用发放接口 */
            try {
                this.issue(issueTO);
            } catch (Exception e) {
                throw new ApplicationRuntimeException(BucksCenterError.IMPORT_EXCEL_ERROR, "第" + i + "行数据发放时发生错误:" + e.getMessage());
            }
            i++;
        }
    }
}
