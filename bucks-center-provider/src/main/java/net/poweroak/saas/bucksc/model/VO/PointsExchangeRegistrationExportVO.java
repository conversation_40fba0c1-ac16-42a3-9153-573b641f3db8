package net.poweroak.saas.bucksc.model.VO;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import net.poweroak.framework.sqltag.TableField;
import net.poweroak.saas.bucksc.model.PO.PointsExchangeRegistration;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2023/11/8
 */
@Data
public class PointsExchangeRegistrationExportVO {
    @ColumnWidth(25)
    @TableField(table = PointsExchangeRegistration.class, field = "email")
    @ExcelProperty("登记邮箱")
    private String email;

    @TableField(table = PointsExchangeRegistration.class, field = "platform")
    @ExcelProperty("购买平台")
    private String platform;

    @TableField(table = PointsExchangeRegistration.class, field = "otherPlatform")
    @ExcelProperty("其他购买平台")
    private String otherPlatform;

    @ColumnWidth(25)
    @TableField(table = PointsExchangeRegistration.class, field = "orderNumber")
    @ExcelProperty("订单号")
    private String orderNumber;

    @TableField(table = PointsExchangeRegistration.class, field = "model")
    @ExcelProperty("购买产品型号")
    private String model;

    @TableField(table = PointsExchangeRegistration.class, field = "amount")
    @ExcelIgnore
    private BigDecimal amount;

    // 在它的基础上把订单金额拼接进去
    @ColumnWidth(15)
    @TableField(table = PointsExchangeRegistration.class, field = "currency")
    @ExcelProperty("订单金额")
    private String currency;

    // 需要将123转为状态
    @TableField(table = PointsExchangeRegistration.class, field = "status")
    @ExcelIgnore
    private Integer status;

    @ExcelProperty("状态")
    private String statusName;

    @ColumnWidth(25)
    @TableField(table = PointsExchangeRegistration.class, field = "comment")
    @ExcelProperty("备注")
    private String comment;

    @ColumnWidth(15)
    @TableField(table = PointsExchangeRegistration.class, field = "verify")
    @ExcelProperty("系统校验")
    private String verify;

    @TableField(table = PointsExchangeRegistration.class, field = "countryCode")
    @ExcelProperty("国家")
    private String countryCode;

    @ColumnWidth(15)
    @TableField(table = PointsExchangeRegistration.class, field = "name")
    @ExcelProperty("提交人")
    private String name;

    @ColumnWidth(25)
    @TableField(table = PointsExchangeRegistration.class, field = "accountEmail")
    @ExcelProperty("提交人邮箱")
    private String accountEmail;

    @ColumnWidth(15)
    @TableField(table = PointsExchangeRegistration.class, field = "phone")
    @ExcelProperty("提交人电话")
    private String phone;

    @ColumnWidth(20)
    @TableField(table = PointsExchangeRegistration.class, field = "creTime")
    @ExcelProperty("提交时间")
    private String creTime;

    @TableField(table = PointsExchangeRegistration.class, field = "operatorName")
    @ExcelProperty("发放人")
    private String operatorName;

    @ColumnWidth(20)
    @TableField(table = PointsExchangeRegistration.class, field = "operateTime")
    @ExcelProperty("发放时间")
    private String operateTime;

    @TableField(table = PointsExchangeRegistration.class, field = "distributePoints")
    @ExcelProperty("发放积分")
    private Integer distributePoints;

    // 需要将国家id转成国家编码
    @TableField(table = PointsExchangeRegistration.class, field = "distributeCountryId")
    @ExcelIgnore
    private Long distributeCountryId;

    @ExcelProperty("积分账户国家")
    private String distributeCountryCode;
}
