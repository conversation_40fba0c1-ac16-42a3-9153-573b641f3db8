package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/11 13:49
 * @description
 */
@Data
public class PointsBalanceEditTO {



    @ApiModelProperty(value = "ID")
    private String id;


    @ApiModelProperty(value = "用户ID")
    private String uid;

    @ApiModelProperty(value = "国家编码")
    private Long countryId;

    @ApiModelProperty(value = "累积消耗")
    private BigDecimal spend=BigDecimal.ZERO;

    @ApiModelProperty(value = "累积发放")
    private BigDecimal earn=BigDecimal.ZERO;

    @ApiModelProperty(value = "余额")
    private BigDecimal balance=BigDecimal.ZERO;
}
