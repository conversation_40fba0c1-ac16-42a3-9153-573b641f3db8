package net.poweroak.saas.bucksc.controller.mgt;

import net.poweroak.BluBucksCenterApplication;
import net.poweroak.framework.api.data.perms.norm.RouteMenu;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/10/28 9:27
 * @description
 */
@RestController
@ApiIgnore
@RouteMenu(module = "pointsCountMgt", label = "积分统计", parent = BluBucksCenterApplication.class,order = 6)
public class PointsCountController {
}
