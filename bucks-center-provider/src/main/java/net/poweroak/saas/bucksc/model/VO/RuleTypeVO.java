package net.poweroak.saas.bucksc.model.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.saas.bucksc.model.PO.Activity;


/**
 * <AUTHOR>
 * @date 2022/5/7 9:15
 */
@Data
public class RuleTypeVO {
    @ApiModelProperty(value = "code")
    private String code;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "规则")
    private String ruleType;

}
