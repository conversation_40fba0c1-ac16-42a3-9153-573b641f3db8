package net.poweroak.saas.bucksc.model.PO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.DomainEntity;
import net.poweroak.framework.sqltag.Table;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by zx on 2022/5/10 16:53
 */


@Data
@ApiModel("积分明细表")
@Table(name = "points_balance_detail")
public class PointsBalanceDetail extends DomainEntity<String> {

    @ApiModelProperty(value = "用户ID")
    private String uid;

    @ApiModelProperty(value = "国家编码")
    private String countryCode;

    @ApiModelProperty(value="国家Id")
    private Long countryId;

    @ApiModelProperty(value = "消耗")
    private Integer spend;

    @ApiModelProperty(value = "发放")
    private Integer earn;

    @ApiModelProperty(value = "剩余余额")
    private Integer balance;

    @ApiModelProperty(value = "回收清零时间")
    private Date recTime;

    @ApiModelProperty(value = "消耗时间")
    private Date speTime;

    @ApiModelProperty(value = "产生时间")
    private Date genTime;

    @ApiModelProperty(value = "是否已回收清零(Y-已，N-未)")
    private String isRec;

    @ApiModelProperty(value = "订单ID")
    private String orderId;

}
