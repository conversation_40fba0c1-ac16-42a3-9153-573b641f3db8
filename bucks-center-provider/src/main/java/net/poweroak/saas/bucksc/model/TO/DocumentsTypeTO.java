package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.page.PageInfo;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/7/21 10:10
 * @description
 */
@Data
@ApiModel("分页查询文案类型")
public class DocumentsTypeTO extends PageInfo {
    @ApiModelProperty(value = "渠道名称")
    private String name;
    @ApiModelProperty(value = "渠道编码")
    private String code;
}
