package net.poweroak.saas.bucksc.controller.mgt;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.BluBucksCenterApplication;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.data.perms.norm.RouteMenu;
import net.poweroak.framework.pedestal.annotate.RequirePermission;
import net.poweroak.framework.pedestal.annotate.RouteAction;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.dao.FaqDao;
import net.poweroak.saas.bucksc.model.PO.Faq;
import net.poweroak.saas.bucksc.model.TO.FaqPageTO;
import net.poweroak.saas.bucksc.model.TO.FaqSaveTO;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/6/18 9:36
 */
@Slf4j
@ApiIgnore
@RestController
@RequestMapping("/" + RouteEndpoint.MGT + "/faq")
@Api(tags = "faq")
@RouteMenu(module = "faqModule", label = "FAQ管理", parent = BluBucksCenterApplication.class,order = 998)
public class FaqController {
    @Resource
    private FaqDao faqDao;

    /**
     * 分页查询
     *
     * @param request
     * @return
     */
    @PostMapping("/page")
    @ApiOperation(value = "faq分页列表")
    @RouteAction(id = "18174750ff9f791157c8280bc86", action = "page", label = "FAQ管理", view = "/BluBucksC/faq/list.vue", linkToModule = true)
    public UnifyResponse<Pagination<Faq>> pageList(@RequestBody FaqPageTO request) {
        return new UnifyResponse<>(faqDao.getPage(request));
    }

    /**
     * 新增
     *
     * @param request
     * @return
     * @throws Exception
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增")
    @RouteAction(id = "1820501c41bf79115229d154a5a", action = "add", label = "新增")
    @RequirePermission
    public UnifyResponse<Boolean> add(@RequestBody FaqSaveTO request) {
        faqDao.save(request);
        return new UnifyResponse<>(true);
    }

    /**
     * 新增
     *
     * @param request
     * @return
     * @throws Exception
     */
    @PostMapping("/update")
    @ApiOperation(value = "编辑")
    @RouteAction(id = "18205029badf79115229d154a5c", action = "update", label = "编辑")
    @RequirePermission
    public UnifyResponse<Boolean> save(@RequestBody FaqSaveTO request) {
        faqDao.save(request);
        return new UnifyResponse<>(true);
    }

    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @PostMapping("/delete/{id}")
    @ApiOperation(value = "删除")
    @RouteAction(id = "1820501d730f79115229d154a5b", action = "delete", label = "删除")
    @RequirePermission
    public UnifyResponse<Boolean> delete(@PathVariable("id") String id) {
        faqDao.delete(id);
        return new UnifyResponse<>(true);
    }
}
