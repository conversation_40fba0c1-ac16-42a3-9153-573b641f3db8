package net.poweroak.saas.bucksc.mapper;

import net.poweroak.framework.data.mybatis.GenericMapper;
import net.poweroak.saas.bucksc.model.PO.PointsAbnData;
import net.poweroak.saas.bucksc.model.TO.PointsAbnDataTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by zx on 2022/5/31 11:28
 */
@Mapper
public interface PointsAbnDataMapper extends GenericMapper<PointsAbnData,String> {

    List<PointsAbnDataTO> list(@Param("start") int start,@Param("limit") int limit);

    int counter();

}
