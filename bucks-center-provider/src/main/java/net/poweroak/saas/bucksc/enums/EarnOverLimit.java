package net.poweroak.saas.bucksc.enums;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/9 9:08
 * @description 是否允许超限获取
 */
public enum EarnOverLimit {
    allowed("Y","允许"),
    notAllowed("N","不允许");

    private String code;
    private String name;

    EarnOverLimit()
    {

    }

    EarnOverLimit(String code, String name)
    {

        this.code = code;
        this.name = name;
    }



    public String getCode()
    {

        return code;
    }

    public String getName()
    {

        return name;
    }

}
