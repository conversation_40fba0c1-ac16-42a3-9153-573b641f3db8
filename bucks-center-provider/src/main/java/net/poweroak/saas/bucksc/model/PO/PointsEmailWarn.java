package net.poweroak.saas.bucksc.model.PO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.DomainEntity;
import net.poweroak.framework.sqltag.Table;


/**
 * Created by zx on 2022/7/6 08:27
 */
@Data
@ApiModel("邮件预警")
@Table(name = "points_email_warn")
public class PointsEmailWarn extends DomainEntity<String> {


    @ApiModelProperty(value = "邮件")
    private String email;

    @ApiModelProperty(value = "用户名称")
    private String name;

    @ApiModelProperty(value = "部门")
    private String department;

    @ApiModelProperty(value = "是否发送")
    private Boolean isSend;


}
