package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.page.PageInfo;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/7 16:44
 * @description
 */
@Data
public class ChannelTO extends PageInfo {
    @ApiModelProperty(value = "渠道名称")
    private String name;
    @ApiModelProperty(value = "渠道编码")
    private String code;

    @ApiModelProperty(value = "类型")
    private String type;
}
