package net.poweroak.saas.bucksc.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2023/11/7
 */
@Getter
public enum DistributeStatus {

    REVIEW(1, "待审核"),
    ISSUED(2, "已发放"),
    REFUSE(3, "不发放");


    private Integer code;
    private String status;

    DistributeStatus(int code, String status) {
        this.code = code;
        this.status = status;
    }


    public static String getStatusByCode(Integer code) {
        for (DistributeStatus item : DistributeStatus.values()) {
            if (item.code.equals(code)) {
                return item.status;
            }
        }
        return code.toString();
    }
}
