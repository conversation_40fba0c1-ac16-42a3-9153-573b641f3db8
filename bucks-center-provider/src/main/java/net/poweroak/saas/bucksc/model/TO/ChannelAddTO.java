package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/7 17:24
 * @description
 */

@Data
@ApiModel("来源")
public class ChannelAddTO {
    /**
     * 渠道名称
     */
    @ApiModelProperty(value = "渠道名称")
    private String name;

    /**
     * 渠道标识
     */
    @ApiModelProperty(value = "渠道标识")
    private String code;



    /**
     * 渠道类型
     */
    @ApiModelProperty(value = "渠道类型")
    private Integer type;

}
