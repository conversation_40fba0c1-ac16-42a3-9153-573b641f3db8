package net.poweroak.saas.bucksc.model.PO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.DomainEntity;
import net.poweroak.framework.sqltag.Table;

/**
 * Created by zx on 2022/5/31 16:14
 */

@Data
@ApiModel("积分异常数据规则")
@Table(name = "points_abn_data_rule")
public class PointsAbnDataRule extends DomainEntity<String> {

    @ApiModelProperty(value = "规则类型(1-自定义 2-积分基础规则)")
    private Integer ruleType;

    @ApiModelProperty(value = "适用的积分消耗规则：1-积分发放；2-积分回退")
    private Integer abnType;

    @ApiModelProperty(value = "时间单位 1-分钟 2-小时  3-天 ")
    private Integer timeUnit;

    @ApiModelProperty(value = "单位时间次数")
    private Integer unitTimeCount;

}
