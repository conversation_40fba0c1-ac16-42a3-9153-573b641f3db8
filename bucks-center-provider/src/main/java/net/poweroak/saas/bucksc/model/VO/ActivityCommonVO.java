package net.poweroak.saas.bucksc.model.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.sqltag.TableField;
import net.poweroak.saas.bucksc.model.PO.Activity;
import net.poweroak.saas.bucksc.model.PO.Channel;
import net.poweroak.saas.bucksc.model.PO.PointsIntervene;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/6/6 9:52
 * @description
 */
@Data
public class ActivityCommonVO{

    @TableField(table = Activity.class, field = "id")
    @ApiModelProperty(value = "活动id")
    private String activityId;

    @TableField(table = Activity.class, field = "code")
    @ApiModelProperty(value = "活动编码")
    private String activityCode;

    @TableField(table = Activity.class, field = "name")
    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @TableField(table = Activity.class, field = "channelCode")
    @ApiModelProperty(value = "渠道编码")
    private String channelCode;

    @TableField(table = Channel.class, field = "name")
    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    @TableField(table = Activity.class, field = "countryId")
    @ApiModelProperty(value = "国家编码")
    private Long countryId;

    @ApiModelProperty(value = "国家名称")
    private String countryName;
    @ApiModelProperty(value = "发放规则")
    @TableField(table = Activity.class, field = "earnRule")
    private String earnRule;

    @ApiModelProperty(value = "消耗规则")
    @TableField(table = Activity.class, field = "spendRule")
    private String spendRule;


}
