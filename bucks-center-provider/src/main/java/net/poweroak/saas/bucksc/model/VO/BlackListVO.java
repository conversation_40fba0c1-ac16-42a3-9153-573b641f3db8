package net.poweroak.saas.bucksc.model.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.saas.bucksc.model.PO.BlackList;

import java.util.Date;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/27 9:29
 * @description
 */
@Data
public class BlackListVO {
    @ApiModelProperty(value = "账号")
    private String account;
    @ApiModelProperty(value = "用户id")
    private String uid;
    @ApiModelProperty(value = "加入黑名单时间")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date joinTime;
    @ApiModelProperty(value = "加入原因")
    private String reason;
    @ApiModelProperty(value = "用户名称")
    private String uname;
    @ApiModelProperty(value = "用户邮箱")
    private String uemail;
    @ApiModelProperty(value = "用户手机号")
    private String uphone;
    @ApiModelProperty(value = "国家编码")
    private Long countryId;
}
