package net.poweroak.saas.bucksc.dao;

import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.exception.ApplicationRuntimeException;
import net.poweroak.framework.data.mybatis.GenericRepository;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.enums.OrderBy;
import net.poweroak.framework.util.StringUtils;
import net.poweroak.saas.bucksc.BucksCenterError;
import net.poweroak.saas.bucksc.mapper.PointsBalanceDetailMapper;
import net.poweroak.saas.bucksc.model.PO.PointsBalanceDetail;
import net.poweroak.saas.bucksc.model.TO.BalanceDetailAddTO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by zx on 2022/5/10 17:00
 */

@Repository
@Slf4j
public class PointsBalanceDetailDao extends GenericRepository<PointsBalanceDetail, PointsBalanceDetailMapper, String> {
    public PointsBalanceDetailDao(PointsBalanceDetailMapper genericMapper) {
        super(genericMapper);
    }


    /**
     * 新增
     *
     * @param balanceDetailAddTO
     */
    public void add(BalanceDetailAddTO balanceDetailAddTO) {
        PointsBalanceDetail pointsBalanceDetail = new PointsBalanceDetail();
        BeanUtils.copyProperties(balanceDetailAddTO, pointsBalanceDetail);
        pointsBalanceDetail.create();
        this.insert(pointsBalanceDetail);
    }


    /**
     * 获取负数
     *
     * @return
     */
    public PointsBalanceDetail getNegative() {
        MybatisQuery mybatisQuery = new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(PointsBalanceDetail::getBalance).lt(0));
        mybatisQuery.orderBy(PointsBalanceDetail::getGenTime, OrderBy.ASC);

        List<PointsBalanceDetail> list = this.find(mybatisQuery);
        if (list.size() > 0) {
            return list.get(0);
        }
        return null;
    }


}
