package net.poweroak.saas.bucksc.controller.mgt;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.model.VO.PointsInterestsVO;
import net.poweroak.saas.bucksc.model.VO.PointsLevelUserDetailVO;
import net.poweroak.saas.bucksc.model.VO.PointsLevelUserGeneralVO;
import net.poweroak.saas.bucksc.service.IPointsInterestsService;
import net.poweroak.saas.bucksc.service.IPointsLevelService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2024/4/29
 */

@RequestMapping("/" + RouteEndpoint.MGT + "/pointsLevelH5")
@Api(tags = "积分等级H5接口")
@RestController
@RequiredArgsConstructor
public class PointsLevelH5Controller {

    private final IPointsLevelService levelService;
    private final IPointsInterestsService interestsService;

    @GetMapping("/v1/userDetail")
    @ApiOperation("根据国家id获取当前登录用户的积分等级详细信息（h5活动页）")
    public UnifyResponse<PointsLevelUserDetailVO> getPointsLevelUserDetail(Long countryId) {
        return new UnifyResponse<>(levelService.userDetail(countryId));
    }

    @GetMapping("/v1/interestsList")
    @ApiOperation("根据国家id获取权益列表")
    public UnifyResponse<List<PointsInterestsVO>> interestsList(Long countryId) {
        return new UnifyResponse<>(interestsService.list(countryId));
    }

    @GetMapping("/v1/userGeneral")
    @ApiOperation("根据国家id获取当前登录用户的积分等级概要信息（app）")
    public UnifyResponse<PointsLevelUserGeneralVO> getPointsLevelUserGeneral(Long countryId) {
        return new UnifyResponse<>(levelService.userGeneral(countryId));
    }

}
