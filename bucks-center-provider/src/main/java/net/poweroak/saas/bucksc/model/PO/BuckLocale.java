package net.poweroak.saas.bucksc.model.PO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.DomainEntity;
import net.poweroak.framework.sqltag.Table;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2023/5/29 15:29
 * @description
 */
@Data
@ApiModel("语种")
@Table(name = "Locale")
public class BuckLocale extends DomainEntity<String> {
    @ApiModelProperty(value = "渠道标识")
    private String localeName;

    @ApiModelProperty(value = "国家Id")
    private String localeCode;
}
