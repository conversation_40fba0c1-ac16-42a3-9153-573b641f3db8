package net.poweroak.saas.bucksc.controller.mgt;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.data.perms.norm.RouteMenu;
import net.poweroak.framework.pedestal.annotate.RequirePermission;
import net.poweroak.framework.pedestal.annotate.RouteAction;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.dao.BlackListDao;
import net.poweroak.saas.bucksc.model.TO.BlackListAddTO;
import net.poweroak.saas.bucksc.model.TO.BlackListTO;
import net.poweroak.saas.bucksc.model.VO.BlackListVO;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/27 8:50
 * @description
 */
@Slf4j
@ApiIgnore
@RequestMapping("/" + RouteEndpoint.MGT + "/blacklist")
@Api(tags = "黑名单")
@RestController
@RouteMenu(module = "blacklistModule", label = "黑名单", parent = PointsRiskMgtController.class)
public class BlackListController {
    @Resource
    BlackListDao blackListDao;


    /**
     * 分页查询黑名单
     * @param request
     * @return
     */
    @PostMapping("/pageList")
    @ApiOperation(value = "分页查询黑名单")
    @RouteAction(id = "1820599b2f1f79115229d154a68", action = "page", label = "黑名单查询", view = "/BluBucksC/blackList/list.vue", linkToModule = true)
    public UnifyResponse<Pagination<BlackListVO>> pageList(@RequestBody BlackListTO request) {
        return this.blackListDao.pageList(request);
    }

    @PostMapping("/join")
    @ApiOperation(value = "加入黑名单")
    @RouteAction(action = "join",id = "182053297f7f79115229d154a62", label = "加入黑名单")
    @RequirePermission
    public UnifyResponse<Boolean> join(@RequestBody BlackListAddTO request) {
        return this.blackListDao.join(request);
    }



    @PostMapping("/out")
    @ApiOperation(value = "移出黑名单")
    @RouteAction(action = "out",id = "1820589df51f79115229d154a65", label = "移出黑名单")
    @RequirePermission
    public UnifyResponse<Boolean> delete(@RequestBody BlackListAddTO request){
        return this.blackListDao.out(request);
    }

}
