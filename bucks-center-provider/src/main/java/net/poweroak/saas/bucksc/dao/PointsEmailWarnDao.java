package net.poweroak.saas.bucksc.dao;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.PageInfo;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.exception.ApplicationRuntimeException;
import net.poweroak.framework.api.util.ApplicationUtil;
import net.poweroak.framework.data.mybatis.GenericRepository;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.user.UserInfoForServer;
import net.poweroak.framework.util.JsonUtils;
import net.poweroak.midp.nc.api.reqt.MailSendRequest;
import net.poweroak.midp.nc.client.MailNotifyClient;
import net.poweroak.saas.bucksc.BucksCenterError;
import net.poweroak.saas.bucksc.mapper.PointsEmailWarnMapper;
import net.poweroak.saas.bucksc.model.PO.PointsEmailWarn;
import net.poweroak.saas.bucksc.model.TO.EmailWarnPageTO;
import net.poweroak.saas.bucksc.model.TO.EmailWarnTO;
import net.poweroak.saas.bucksc.model.TO.PointsEmailWarnTO;
import net.poweroak.saas.bucksc.model.VO.PointsEmailWarnPageVO;
import net.poweroak.saas.uc.client.UserClient;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;
import javax.annotation.Resource;
import javax.mail.internet.AddressException;
import javax.mail.internet.InternetAddress;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Created by zx on 2022/7/6 8:47
 */

@Slf4j
@Repository
public class PointsEmailWarnDao extends GenericRepository<PointsEmailWarn, PointsEmailWarnMapper, String> {

    public PointsEmailWarnDao(PointsEmailWarnMapper genericMapper) {
        super(genericMapper);
    }


    public Pagination<PointsEmailWarnPageVO> page(EmailWarnPageTO pageTO) {

        final MybatisQuery query = MybatisQuery.where().addCriterion(Criterion.where(PointsEmailWarn::getEnable).is(true));
        Pagination<PointsEmailWarn> pagination = this.find(query, pageTO.getPageNumber(), pageTO.getPageSize());
        List<PointsEmailWarnPageVO> list = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(pagination.getContent())) {
            pagination.getContent().forEach(warn -> {
                PointsEmailWarnPageVO rulePageVO = new PointsEmailWarnPageVO();
                BeanUtils.copyProperties(warn, rulePageVO);
                rulePageVO.setId(warn.getId());
                rulePageVO.setCreTime(warn.getCreTime());
                list.add(rulePageVO);
            });
        }
        return new Pagination(list, new PageInfo(pageTO.getPageNumber() - 1, pageTO.getPageSize()), pagination.getTotalElements());
    }

    public void add(PointsEmailWarnTO emailWarnTO) {

        final PointsEmailWarn emailWarn = this.findOne(MybatisQuery.where().addCriterion(Criterion.where(PointsEmailWarn::getEmail).is(emailWarnTO.getEmail())).addCriterion(Criterion.where(PointsEmailWarn::getEnable).is(true)));

        if (emailWarn != null) {
            throw new ApplicationRuntimeException(BucksCenterError.EMAIL_REPEAT);
        }

        PointsEmailWarn pointsEmailWarn = new PointsEmailWarn();
        pointsEmailWarn.setEmail(emailWarnTO.getEmail());
        pointsEmailWarn.setDepartment(emailWarnTO.getDepartment());
        pointsEmailWarn.setName(emailWarnTO.getName());
        pointsEmailWarn.setIsSend(true);
        pointsEmailWarn.create();
        this.insert(pointsEmailWarn);

    }

    public void update(PointsEmailWarnTO emailWarnTO) {

        final PointsEmailWarn emailWarn = this.findOne(MybatisQuery.where()
                .addCriterion(Criterion.where(PointsEmailWarn::getEmail).is(emailWarnTO.getEmail()))
                .addCriterion(Criterion.where(PointsEmailWarn::getId).ne(emailWarnTO.getId()))
                .addCriterion(Criterion.where(PointsEmailWarn::getEnable).is(true)));

        if (emailWarn != null) {
            throw new ApplicationRuntimeException(BucksCenterError.EMAIL_REPEAT);
        }

        PointsEmailWarn pointsEmailWarn = new PointsEmailWarn();
        pointsEmailWarn.setId(emailWarnTO.getId());
        pointsEmailWarn.setEmail(emailWarnTO.getEmail());
        pointsEmailWarn.setDepartment(emailWarnTO.getDepartment());
        pointsEmailWarn.setName(emailWarnTO.getName());

        pointsEmailWarn.modify();
        this.updateById(pointsEmailWarn);

    }

    public void enableOrDisable(String warnId) {
        final Optional<PointsEmailWarn> optional = this.findById(warnId);
        if (optional.isPresent()) {
            PointsEmailWarn emailWarn = optional.get();
            emailWarn.setIsSend(emailWarn.getIsSend() == true ? false : true);
            emailWarn.modify();
            this.updateById(emailWarn);
        }
    }

    public void del(String warnId) {
        final Optional<PointsEmailWarn> optional = this.findById(warnId);
        if (optional.isPresent()) {
            PointsEmailWarn emailWarn = optional.get();
            emailWarn.setEnabled(false);
            emailWarn.modify();
            this.updateById(emailWarn);
        }
    }

    @Resource
    private MailNotifyClient notifyClient;

    @Resource
    private UserClient userClient;


    String LOGO = "/webjars/global/logo/logo-bluetti-02.png";
    //模板code
    String TEMPLATE_CODE = "BUSK_CENTER_POINTS_WARN";

    public void sendMail(EmailWarnTO emailWarnTO) {
        final List<PointsEmailWarn> pointsEmailWarns = this.find(MybatisQuery.where().addCriterion(Criterion.where(PointsEmailWarn::getIsSend).is(true)).addCriterion(Criterion.where(PointsEmailWarn::getEnable).is(true)));

        if (CollectionUtils.isNotEmpty(pointsEmailWarns)) {
            pointsEmailWarns.forEach(pointsEmailWarn -> {
                //获取用户名
                final UnifyResponse<List<UserInfoForServer>> response = userClient.list(Arrays.asList(emailWarnTO.getUserId()));
                String userName = (response.isOK() && CollectionUtils.isNotEmpty(response.getData())) ? response.getData().iterator().next().getNickname() : "";

                Map<String, String> map = Maps.newHashMap();
                map.put("userName", userName);
                map.put("expMsg", emailWarnTO.getExpMsg());
                map.put("recipient", pointsEmailWarn.getName());
                map.put("logo", LOGO);

                MailSendRequest sendRequest = new MailSendRequest();
                sendRequest.setTemplateParameters(map);
                sendRequest.setSource(ApplicationUtil.getApplicationNameReal());
                sendRequest.setTemplateCode(TEMPLATE_CODE);
                try {
                    sendRequest.addReceiveAddress(new InternetAddress(pointsEmailWarn.getEmail()));
                } catch (AddressException e) {
                    e.printStackTrace();
                }
                final UnifyResponse<String> unifyResponse = notifyClient.sendMailDelay(sendRequest);
                log.info("邮件预警-- 发送邮件返回结果：{}", JsonUtils.toJSON(unifyResponse));
            });
        }
    }

}
