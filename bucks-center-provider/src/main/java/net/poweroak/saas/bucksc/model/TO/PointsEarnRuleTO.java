package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.page.PageInfo;

/**
 * <AUTHOR>
 * @date 2022/5/7 9:15
 */
@Data
@ApiModel("发放规则列表")
public class PointsEarnRuleTO extends PageInfo {

    @ApiModelProperty(value = "规则类型 1-签到；2-货币兑换；99-通用")
    private Integer ruleType;

    @ApiModelProperty(value = "活动Id")
    private String activityId;

    @ApiModelProperty(value = "国家编码")
    private Long countryId;

    @ApiModelProperty(value = "渠道编码")
    private String channelCode;

}
