package net.poweroak.saas.bucksc.controller.mgt;

import io.swagger.annotations.Api;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.data.perms.norm.RouteMenu;
import net.poweroak.framework.pedestal.annotate.RequirePermission;
import net.poweroak.framework.pedestal.annotate.RouteAction;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.dao.PointsAbnDataRuleDao;
import net.poweroak.saas.bucksc.model.TO.AbnDataRulePageTO;
import net.poweroak.saas.bucksc.model.TO.AbnDataRuleTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Created by zx on 2022/5/31 11:39
 */

@ApiIgnore
@RestController
@Api(tags = "异常数据规则管理")
@RequestMapping("/" + RouteEndpoint.MGT + "/pointsAbnDataRule")
@RouteMenu(module = "pointsAbnDataRule", label = "异常数据规则管理", parent = PointsRiskMgtController.class)
public class PointsAbnDataRuleManageController {

    @Resource
    private PointsAbnDataRuleDao abnDataRuleDao;

    @PostMapping("/page")
    @RouteAction(action = "pageList",id = "18118344a1cf791157c8280bb99", label = "异常数据规则管理", view = "/BluBucksC/risk/warn/list.vue", linkToModule = true)
    @RequirePermission
    public UnifyResponse<Pagination> listByPage(@RequestBody AbnDataRulePageTO pageTO) {
        return new UnifyResponse(abnDataRuleDao.page(pageTO));
    }

    @PostMapping("/add")
    @RouteAction(action = "add",id = "182335042b0f79115229d154ac1", label = "新增")
    @RequirePermission
    public UnifyResponse<Void> add(@RequestBody @Valid AbnDataRuleTO abnDataRuleTO) {
        abnDataRuleDao.add(abnDataRuleTO);
        return new UnifyResponse();
    }

    @PostMapping("/update")
    @RouteAction(action = "update",id = "182334f95e3f79115229d154abf", label = "编辑")
    @RequirePermission
    public UnifyResponse<Void> update(@RequestBody @Valid AbnDataRuleTO abnDataRuleTO) {
        abnDataRuleDao.update(abnDataRuleTO);
        return new UnifyResponse();
    }




}
