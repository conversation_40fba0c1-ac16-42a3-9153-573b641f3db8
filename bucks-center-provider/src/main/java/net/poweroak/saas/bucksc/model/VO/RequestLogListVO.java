package net.poweroak.saas.bucksc.model.VO;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.page.PageInfo;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/5/7 9:15
 */
@Data
public class RequestLogListVO {
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "请求参数")
    private String requestParams;
    @ApiModelProperty(value = "错误信息")
    private String errMsg;
    @ApiModelProperty(value = "请求时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date creTime;
    @ApiModelProperty(value = "是否重新执行0=否 1=是")
    private Integer enable;
    @ApiModelProperty(value = "类型名称")
    private String typeName;
}
