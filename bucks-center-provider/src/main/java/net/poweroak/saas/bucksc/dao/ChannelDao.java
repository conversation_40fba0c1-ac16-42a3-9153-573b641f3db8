package net.poweroak.saas.bucksc.dao;

import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.exception.ApplicationRuntimeException;
import net.poweroak.framework.data.mybatis.GenericRepository;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.enums.OrderBy;
import net.poweroak.framework.util.StringUtils;
import net.poweroak.saas.bucksc.BucksCenterError;
import net.poweroak.saas.bucksc.enums.ChannelType;
import net.poweroak.saas.bucksc.mapper.ChannelMapper;
import net.poweroak.saas.bucksc.model.PO.Activity;
import net.poweroak.saas.bucksc.model.PO.Channel;
import net.poweroak.saas.bucksc.model.TO.ChannelAddTO;
import net.poweroak.saas.bucksc.model.TO.ChannelTO;
import net.poweroak.saas.bucksc.model.TO.ChannelUpdateTO;
import net.poweroak.saas.bucksc.request.PointsChannelRequest;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/7 16:15
 * @description 来源渠道dao
 */
@Repository
@Slf4j
public class ChannelDao extends GenericRepository<Channel, ChannelMapper, String> {

    @Resource
    @Lazy
    private ActivityDao activityDao;

    public ChannelDao(ChannelMapper genericMapper) {
        super(genericMapper);
    }


    /**
     * 根据name获取单个渠道
     *
     * @param name
     * @return
     */
    public List<Channel> getByName(String name) {
        MybatisQuery mybatisQuery = new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(Channel::getEnable).is(true));
        mybatisQuery.addCriterion(Criterion.where(Channel::getName).regex(name));
        List<Channel> channels = this.find(mybatisQuery);
        return channels;

    }


    /**
     * 新增来源渠道
     *
     * @param request
     * @return
     */
    public UnifyResponse<Boolean> add(ChannelAddTO request) {
        if (StringUtils.isEmpty(request.getCode())) {
            log.error("渠道Code为空");
            throw new ApplicationRuntimeException(BucksCenterError.CHANNEL_CODE_CANNOT_BE_EMPTY);
        }
        if (StringUtils.isEmpty(request.getName())) {
            log.error("渠道名称为空");
            throw new ApplicationRuntimeException(BucksCenterError.CHANNEL_NAME_CANNOT_BE_EMPTY);
        }
        List<Channel> list = existsList().getData();

        list.forEach(tem -> {
            if (Objects.equals(tem.getName(), request.getName())) {
                log.error("渠道Name重复,channelName:{}", request.getName());
                throw new ApplicationRuntimeException(BucksCenterError.CHANNEL_NAME_CANNOT_BE_REPEATED);
            }
            if (Objects.equals(tem.getCode(), request.getCode())) {
                log.error("渠道标识重复,channelCode:{}",request.getCode());
                throw new ApplicationRuntimeException(BucksCenterError.CHANNEL_CODE_CANNOT_BE_REPEATED);
            }
        });

        Channel channel = new Channel();
        channel.setCode(request.getCode());
        channel.setName(request.getName());

        channel.setType(ChannelType.appkey.getCode());
        channel.create();
        this.insert(channel);
        return new UnifyResponse(Boolean.TRUE);
    }

    /**
     * 不分页查询列表
     *
     * @return
     */
    public UnifyResponse<List<Channel>> list() {
        MybatisQuery mybatisQuery = new MybatisQuery();
//        mybatisQuery.addCriterion(Criterion.where(Channel::getEnable).is(true));
        List<Channel> all = this.find(mybatisQuery);
        return new UnifyResponse(all);
    }


    public UnifyResponse<List<Channel>> existsList() {
        MybatisQuery mybatisQuery = new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(Channel::getEnable).is(true));
        List<Channel> all = this.find(mybatisQuery);
        return new UnifyResponse(all);
    }

    public UnifyResponse<List<Channel>> existsShopList() {
        MybatisQuery mybatisQuery = new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(Channel::getEnable).is(true));
        mybatisQuery.addCriterion(Criterion.where(Channel::getType).is(2));
        List<Channel> all = this.find(mybatisQuery);
        return new UnifyResponse(all);
    }


    /**
     * 删除来源渠道
     *
     * @param id
     * @return
     */
    public UnifyResponse<Boolean> deleteChannel(String id) {

        Optional<Channel> byId = this.findById(id);
        if(byId.isPresent()){
            Channel channel=byId.get();
            if(channel.getType()== ChannelType.shop.getCode()){
                log.error("店铺渠道无法删除");
                throw new ApplicationRuntimeException(BucksCenterError.SHOP_TYPE_CHANNEL_CANNOT_BE_DELETED);
            }


            MybatisQuery mybatisQuery=new MybatisQuery();
            mybatisQuery.addCriterion(Criterion.where(Activity::getChannelCode).is(channel.getCode()));
            mybatisQuery.addCriterion(Criterion.where(Activity::getEnable).is(1));
            Activity one = this.activityDao.findOne(mybatisQuery);
            if(one!=null){
                log.error("删除渠道失败,请先删除与之关联的积分活动,channelCode:{}", channel.getCode());
                throw new ApplicationRuntimeException(BucksCenterError.PLEASE_DELETE_THE_POINTS_ACTIVITY_ASSOCIATED_WITH_IT_FIRST);
            }
            MybatisQuery query = new MybatisQuery();
            query.addCriterion(Criterion.where(Channel::getId).is(id));
            query.addUpdateItem(Channel::getEnable, 0);
            this.update(query);

        }


        return new UnifyResponse<>(Boolean.TRUE);
    }

    /**
     * 更新来源渠道
     *
     * @param request
     * @return
     */
    public UnifyResponse<Boolean> updateChannel(ChannelUpdateTO request) {
        List<Channel> list = existsList().getData();


        for (Channel value : list) {
            if (!Objects.equals(value.getId(), request.getId())) {
                if (Objects.equals(value.getName(), request.getName())) {
                    log.error("渠道名称重复,channelName:{}", request.getName());
                    throw new ApplicationRuntimeException(BucksCenterError.CHANNEL_NAME_CANNOT_BE_REPEATED);
                }
//                if (Objects.equals(value.getCode(), request.getCode())) {
//                    log.error("渠道标识重复,channelCode:{}", request.getCode());
//                    throw new ApplicationRuntimeException(BucksCenterError.CHANNEL_CODE_CANNOT_BE_REPEATED);
//                }
            }

        }


        Channel channel = new Channel();
        channel.setId(request.getId());
        channel.setName(request.getName());
//        channel.setCode(request.getCode());
        channel.modify();
        this.updateById(channel);
        return new UnifyResponse<>(Boolean.TRUE);
    }

    /**
     * 分页查询来源渠道
     *
     * @param request
     * @return
     */
    public UnifyResponse<Pagination<Channel>> pageList(ChannelTO request) {

        MybatisQuery query = new MybatisQuery();
        List<Channel> result = new ArrayList<>();

        if (!StringUtils.isEmpty(request.getName())) {
            query.addCriterion(Criterion.where(Channel::getName).regex(request.getName()));
        }

        if (!StringUtils.isEmpty(request.getCode())) {
            query.addCriterion(Criterion.where(Channel::getCode).regex(request.getCode()));
        }

        if (!StringUtils.isEmpty(request.getType())) {
            query.addCriterion(Criterion.where(Channel::getType).regex(request.getType()));
        }

        query.orderBy(Channel::getModTime, OrderBy.DESC);
        query.addCriterion(Criterion.where(Channel::getEnable).is(true));

        return new UnifyResponse(this.find(query, request.getPageNumber(), request.getPageSize()));
    }

    /**
     * 根据id获取单个来源渠道
     *
     * @param id
     * @return
     */
    public UnifyResponse<Channel> getOne(String id) {
        Optional<Channel> channel = this.findById(id);
        if (channel.isPresent()) {
            return new UnifyResponse(channel.get());
        } else {
            log.error("未查询到来源渠道详情数据，id={}", id);
            throw new ApplicationRuntimeException(BucksCenterError.THE_SOURCE_CHANNEL_DETAILS_DATA_WERE_NOT_QUERIED);
        }
    }

    /**
     * 根据code获取单个来源渠道
     *
     * @param code
     * @return
     */
    public Channel findByCode(String code) {
        MybatisQuery mybatisQuery = new MybatisQuery();
        mybatisQuery.addCriterion(Criterion.where(Channel::getCode).is(code));
        return this.findOne(mybatisQuery);
    }

    /**
     * 新增店铺
     * @param request
     * @return
     */

    public UnifyResponse<Boolean> insertShop(PointsChannelRequest request) {
        if (StringUtils.isEmpty(request.getCode())) {
            log.error("渠道Code为空");
            throw new ApplicationRuntimeException(BucksCenterError.CHANNEL_CODE_CANNOT_BE_EMPTY);
        }
        if (StringUtils.isEmpty(request.getName())) {
            log.error("渠道名称为空");
            throw new ApplicationRuntimeException(BucksCenterError.CHANNEL_NAME_CANNOT_BE_EMPTY);
        }
        List<Channel> list = existsShopList().getData();

        list.forEach(tem -> {
            if (Objects.equals(tem.getName(), request.getName())) {
                log.error("渠道Name重复,channelName:{}", request.getName());
                throw new ApplicationRuntimeException(BucksCenterError.CHANNEL_NAME_CANNOT_BE_REPEATED);
            }
            if (Objects.equals(tem.getCode(), request.getCode())) {
                log.error("渠道标识重复,channelCode:{}",request.getCode());
                throw new ApplicationRuntimeException(BucksCenterError.CHANNEL_CODE_CANNOT_BE_REPEATED);
            }
        });

        Channel channel = new Channel();
        channel.setCode(request.getCode());
        channel.setName(request.getName());
        channel.setType(ChannelType.shop.getCode());
        channel.create();
        this.insert(channel);
        return new UnifyResponse(Boolean.TRUE);


    }


}
