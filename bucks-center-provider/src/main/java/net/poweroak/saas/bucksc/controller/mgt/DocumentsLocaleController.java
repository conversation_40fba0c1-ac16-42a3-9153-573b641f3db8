package net.poweroak.saas.bucksc.controller.mgt;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.data.perms.norm.RouteMenu;
import net.poweroak.framework.pedestal.annotate.RequirePermission;
import net.poweroak.framework.pedestal.annotate.RouteAction;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.dao.LocaleDao;
import net.poweroak.saas.bucksc.model.PO.BuckLocale;
import net.poweroak.saas.bucksc.model.TO.DocumentsLocalePageTO;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2023/5/29 15:34
 * @description
 */
@Slf4j
@ApiIgnore
@RequestMapping("/" + RouteEndpoint.MGT + "/DocumentsLocale")
@Api(tags = "文案语种")
@RestController
@RouteMenu(module = "DocumentsLocale", label = "文案语种", parent = DocumentsController.class)
public class DocumentsLocaleController {

    @Resource
    private LocaleDao  localeDao;

    /**
     * 分页查询
     *
     * @param request
     * @return
     */
    @PostMapping("/page")
    @ApiOperation(value = "文案语种分页列表")
    @RouteAction(id = "1886672b9ebf791157564c2ec7e", action = "page", label = "文案语种管理", view = "/BluBucksC/documentsLocale/list.vue", linkToModule = true)
    @RequirePermission
    public UnifyResponse<Pagination<BuckLocale>> pageList(@RequestBody DocumentsLocalePageTO request) {
        return new UnifyResponse(this.localeDao.pageList(request));
    }


    @PostMapping("/add")
    @ApiOperation(value = "新增文案语种")
    @RouteAction(id = "188667e4359f791157564c2ec7f", action = "add", label = "新增")
    @RequirePermission
    public UnifyResponse<Boolean> add(@RequestBody BuckLocale request) throws Exception{
        return new UnifyResponse(this.localeDao.add(request));
    }

    @GetMapping("/get/{id}")
    @ApiOperation(value = "单个查询文案语种")
    public UnifyResponse<BuckLocale> get(@PathVariable("id") String id) {
        return new UnifyResponse(this.localeDao.getOne(id));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新文案语种")
    @RouteAction(id = "188668d6038f791157564c2ec84", action = "update", label = "编辑")
    @RequirePermission
    public UnifyResponse<Boolean> update(@RequestBody BuckLocale request){
        return new UnifyResponse(this.localeDao.updateLocale(request));
    }

    /**
     * 删除文案类型
     * @param id
     * @return
     */
    @GetMapping("/delete/{id}")
    @ApiOperation(value = "删除文案语种")
    @RouteAction(id = "1886691974cf791157564c2ec85", action = "delete", label = "删除")
    @RequirePermission
    public UnifyResponse<Boolean> delete(@PathVariable("id") String id){
        return new UnifyResponse(this.localeDao.deleteLocale(id));
    }

    /**
     * 不分页查文案类型
     * @return
     */
    @GetMapping("/list")
    @ApiOperation(value = "不分页查询语种")
    public UnifyResponse<List<BuckLocale>> list(){
        return new UnifyResponse(this.localeDao.list());
    }

}
