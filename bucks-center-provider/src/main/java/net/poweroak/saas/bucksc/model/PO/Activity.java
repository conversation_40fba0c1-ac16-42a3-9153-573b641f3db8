package net.poweroak.saas.bucksc.model.PO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.DomainEntity;
import net.poweroak.framework.sqltag.Table;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/5/9 10:32
 */
@Data
@ApiModel("积分活动")
@Table(name = "activity")
public class Activity extends DomainEntity<String> {

    @ApiModelProperty(value = "渠道标识")
    private String channelCode;

//    @ApiModelProperty(value = "国家/地区编码")
//    private String country;

    @ApiModelProperty(value = "国家Id")
    private Long countryId;

    @ApiModelProperty(value = "活动名称")
    private String name;

    @ApiModelProperty(value = "适用的积分发放规则：1-签到；2-货币兑换；9-通用")
    private String earnRule;

    @ApiModelProperty(value = "适用的积分消耗规则：1-券卡兑换；2-物品兑换；3-货币抵扣；9-通用")
    private String spendRule;

    @ApiModelProperty(value = "活动编码")
    private String code;

    @ApiModelProperty("开始时间")
    private Date beginTime;

    @ApiModelProperty("结束时间")
    private Date endTime;

//    @ApiModelProperty(value = "单次获取上限")
//    private Integer earnLimit;
//
//    @ApiModelProperty(value = "是否允许超限获取：Y-允许；N-不允许")
//    private String earnOverLimit="Y";

}
