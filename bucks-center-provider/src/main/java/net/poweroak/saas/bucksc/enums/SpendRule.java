package net.poweroak.saas.bucksc.enums;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/9 9:06
 * @description 适用的积分消耗规则
 */
public enum SpendRule {
//    CouponCard(1,"券卡兑换"),
//    itemExchange(2,"货币兑换"),
//    currencyDeduction(3,"货币抵扣"),
//    commonUse(9,"通用");
//
//
//    private int code;
//    private String name;
//
//    SpendRule()
//    {
//
//    }
//
//    SpendRule(int code, String name)
//    {
//
//        this.code = code;
//        this.name = name;
//    }
//
//
//
//    public int getCode()
//    {
//
//        return code;
//    }
//
//    public String getName()
//    {
//
//        return name;
//    }

}
