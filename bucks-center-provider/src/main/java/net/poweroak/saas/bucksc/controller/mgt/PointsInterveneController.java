package net.poweroak.saas.bucksc.controller.mgt;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.data.perms.norm.RouteMenu;
import net.poweroak.framework.pedestal.annotate.RequirePermission;
import net.poweroak.framework.pedestal.annotate.RouteAction;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.dao.PointsInterveneDao;
import net.poweroak.saas.bucksc.model.TO.PointsInterveneAddTO;
import net.poweroak.saas.bucksc.model.TO.PointsInterveneExcelTO;
import net.poweroak.saas.bucksc.model.TO.PointsInterveneTO;
import net.poweroak.saas.bucksc.model.VO.PointsInterveneVO;
import net.poweroak.saas.bucksc.service.PointsInterveneService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/30 9:47
 * @description
 */

@ApiIgnore
@RestController
@RequestMapping("/" + RouteEndpoint.MGT + "/pointsIntervene")
@Api(tags = "人工后台干预")
@RouteMenu(module = "pointsInterveneMGT", label = "人工后台干预", parent = PointsRiskMgtController.class)
public class PointsInterveneController {
    @Resource
    private PointsInterveneDao pointsInterveneDao;

    @Resource
    private PointsInterveneService pointsInterveneService;

    /**
     * 人工后台干预列表
     *
     * @param request
     * @return
     */

    @PostMapping("/page")
    @RouteAction(id = "1811847e902f791157c8280bb9b", action = "page", label = "人工后台干预查询", view = "/BluBucksC/intervene/list.vue", linkToModule = true)
    @RequirePermission
    public UnifyResponse<Pagination<PointsInterveneVO>> page(@RequestBody PointsInterveneTO request) {
        return new UnifyResponse<>(pointsInterveneDao.getPage(request));
    }

    /**
     * 新增人工后台干预
     *
     * @param request
     * @return
     */

    @PostMapping("/add")
    @RouteAction(action = "add", id = "182058bd73ff79115229d154a67", label = "新增人工干预")
    @RequirePermission
    public UnifyResponse<Boolean> add(@RequestBody PointsInterveneAddTO request) {
        return pointsInterveneDao.add(request);

    }

    /**
     * 下载导入模板
     *
     * @return
     */
    @GetMapping("/downloadImportTemplate")
    @ApiOperation(value = "下载导入模板")
    @RequirePermission
    public void downloadImportTemplate(HttpServletResponse response) throws IOException {
        pointsInterveneService.downloadImportTemplate(response);
    }

    @PostMapping("/importPoints")
    @ApiOperation(value = "批量导入积分")
    @RequirePermission
    @RouteAction(action = "importPoints", id = "192f6360f7bbc91b2119698bf42", label = "批量导入积分")
    public void importPoints(@RequestParam("code") String code,
                             @RequestParam("channelCode") String channelCode,
                             @RequestParam(value = "reason", required = false) String reason,
                             @RequestParam("countryId") Long countryId,
                             @RequestParam("genType") Integer genType,
                             @RequestPart(value = "file") MultipartFile file, HttpServletResponse response) throws IOException {
        pointsInterveneService.importPoints(PointsInterveneExcelTO.builder()
                        .code(code)
                        .channelCode(channelCode)
                        .reason(reason)
                        .countryId(countryId)
                        .genType(genType)
                        .file(file)
                        .build(),
                response);
    }
}
