package net.poweroak.saas.bucksc.controller.web;

import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.data.user.ContextUtils;
import net.poweroak.framework.util.StringUtils;
import net.poweroak.saas.bucksc.client.PointsChangeClient;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.request.IntegralPageRequest;
import net.poweroak.saas.bucksc.request.IntegralQueryRequest;
import net.poweroak.saas.bucksc.request.IntegralSpendRequest;
import net.poweroak.saas.bucksc.response.ShopifyBalanceJournalResponse;
import net.poweroak.saas.bucksc.response.ShopifyPointsQueryResponse;
import net.poweroak.saas.bucksc.service.IPointsMqService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @Mailto <EMAIL>
 * @Create 2024/5/9 15:22
 **/
@Slf4j
@RestController
@Api(tags = "积分变动")
@AllArgsConstructor
@RequestMapping("/" + RouteEndpoint.API + "/pointsHandle")
public class PointsChangeController implements PointsChangeClient {

    private final IPointsMqService iPointsMqServiceImpl;

    @Override
    public UnifyResponse<Pagination<ShopifyBalanceJournalResponse>> shopifyPage(IntegralPageRequest pageRequest) {
        Pagination<ShopifyBalanceJournalResponse> balanceJournalPage = iPointsMqServiceImpl.getBalanceJournalPage(pageRequest);
        return new UnifyResponse<>(balanceJournalPage);
    }

    @Override
    public UnifyResponse<ShopifyBalanceJournalResponse> shopifyDetail(@RequestParam("id") String id) {
        ShopifyBalanceJournalResponse balanceJournalDetail = iPointsMqServiceImpl.getBalanceJournalDetail(id);
        return new UnifyResponse<>(balanceJournalDetail);
    }

    @Override
    public UnifyResponse<ShopifyPointsQueryResponse> shopifyAccount(IntegralQueryRequest integralQueryRequest) {
        ShopifyPointsQueryResponse shopifyAccountInfo = iPointsMqServiceImpl.getShopifyAccountInfo(integralQueryRequest);
        return new UnifyResponse<>(shopifyAccountInfo);
    }

    @Override
    public UnifyResponse<Boolean> shopifyEarn(IntegralSpendRequest integralSpendRequest) {
        if (StringUtils.isEmpty(integralSpendRequest.getUid()))integralSpendRequest.setUid(ContextUtils.get().getUID());
        iPointsMqServiceImpl.shopifyEarn(integralSpendRequest);
        return new UnifyResponse<>(Boolean.TRUE);
    }

    @GetMapping("/v1/reprocessing")
    public UnifyResponse<Boolean> shopifyReprocessing(@RequestParam("id") String id) {
        iPointsMqServiceImpl.processingMessages(id);
        return new UnifyResponse<>(Boolean.TRUE);
    }


}
