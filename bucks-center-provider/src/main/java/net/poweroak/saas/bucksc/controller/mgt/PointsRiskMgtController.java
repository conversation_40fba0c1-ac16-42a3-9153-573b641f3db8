package net.poweroak.saas.bucksc.controller.mgt;

import net.poweroak.BluBucksCenterApplication;
import net.poweroak.framework.api.data.perms.norm.RouteMenu;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * Created by zx on 2022/5/31 16:18
 */

@RestController
@ApiIgnore
@RouteMenu(module = "pointsRiskMgt", label = "风险控制", parent = BluBucksCenterApplication.class,order = 5)
public class PointsRiskMgtController {


}
