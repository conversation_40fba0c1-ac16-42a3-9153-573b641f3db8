package net.poweroak.saas.bucksc.model.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.saas.bucksc.common.GenSubType;
import net.poweroak.saas.bucksc.model.TO.PointsEranCommTO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.TreeMap;

/**
 * 发放积分
 *
 * <AUTHOR>
 * @date 2022/5/11 14:09
 */
@Data
@ApiModel("发放积分-通用-返回信息")
public class PointsEarnCommonVO implements Serializable {

    @ApiModelProperty(value = "流水ID")
    private String journalId;

    @ApiModelProperty(value = "兑换比例")
    private BigDecimal ratio = BigDecimal.ZERO;

    @ApiModelProperty(value = "发放积分")
    private Integer points;

    @ApiModelProperty(value = "用户积分余额")
    private Integer balance = 0;

    @ApiModelProperty(value = "持续签到天数")
    private Integer signDays;

    @ApiModelProperty(value = " 发放类型 1=一次性 2=无限次")
    private Integer earnType;

    @ApiModelProperty(value = "发放规则ID")
    private String pointsEarnRuleId;

    @ApiModelProperty(value = "额外奖励")
    private TreeMap<String,Integer> extraRewordMap;


    @ApiModelProperty(value = "适用的积分发放规则：1-签到；2-货币兑换；3-光伏发电;9-通用")
    private Integer ruleType;
    //    @ApiModelProperty(value = "活动Id")
//    private String activityId;
//
//    @ApiModelProperty(value = "渠道编码")
//    private String channelCode;
//
//    @ApiModelProperty(value = "国家编码")
//    private String countryCode;
//
    @ApiModelProperty(value = "uid")
    private String uid;

    @ApiModelProperty(value = "是否存在规则：true 存在")
    private Boolean ruleExist;

    private PointsEranCommTO pointsEranCommTO;

    private GenSubType genSubType;
}
