package net.poweroak.saas.bucksc.model.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.saas.bucksc.model.PO.Activity;

import java.util.Date;


/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/9 16:11
 * @description
 */
@Data
public class ActivityVO {



    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    @ApiModelProperty(value = "渠道标识")
    private String channelCode;

//    @ApiModelProperty(value = "国家/地区编码")
//    private String country;

    @ApiModelProperty(value = "国家Id")
    private Long countryId;

    @ApiModelProperty(value = "活动名称")
    private String name;

    @ApiModelProperty(value = "适用的积分发放规则：1-签到；2-货币兑换；9-通用")
    private String earnRule;

    @ApiModelProperty(value = "适用的积分消耗规则：1-券卡兑换；2-物品兑换；3-货币抵扣；9-通用")
    private String spendRule;

    @ApiModelProperty(value = "活动编码")
    private String code;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date beginTime;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("修改人")
    private String modBy;

    @ApiModelProperty("状态")
    private Integer enable;

    @ApiModelProperty("修改时间")
    private Date modTime;
}
