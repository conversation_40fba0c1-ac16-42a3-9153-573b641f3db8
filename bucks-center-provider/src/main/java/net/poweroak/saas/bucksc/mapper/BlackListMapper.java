package net.poweroak.saas.bucksc.mapper;

import net.poweroak.framework.data.mybatis.GenericMapper;
import net.poweroak.saas.bucksc.model.PO.BlackList;
import net.poweroak.saas.bucksc.model.PO.Channel;
import org.apache.ibatis.annotations.Mapper;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/26 17:59
 * @description
 */
@Mapper
public interface BlackListMapper extends GenericMapper<BlackList, String> {
}
