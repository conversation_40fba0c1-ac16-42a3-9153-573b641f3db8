package net.poweroak.saas.bucksc.service;

import net.poweroak.saas.bucksc.model.TO.PointsInterveneExcelTO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public interface PointsInterveneService {
    void downloadImportTemplate(HttpServletResponse response) throws IOException;

    void importPoints(PointsInterveneExcelTO pointsInterveneExcelTO, HttpServletResponse response) throws IOException;
}
