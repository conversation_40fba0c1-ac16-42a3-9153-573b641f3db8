package net.poweroak.saas.bucksc.mapper;

import net.poweroak.framework.data.mybatis.GenericMapper;
import net.poweroak.saas.bucksc.model.PO.PointsJournal;
import net.poweroak.saas.bucksc.model.VO.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by zx on 2022/5/10 17:03
 */
@Mapper
public interface PointsJournalMapper  extends GenericMapper<PointsJournal,String> {
    List<journalGroupVO> pageByGroup(@Param("channelCode") String channelCode,@Param("start") int start,@Param("limit") int limit);


    List<journalDayVO> pageByDay(@Param("begin")String begin, @Param("end")String end,@Param("start") int start,@Param("limit") int limit,@Param("activityId")String activityId,@Param("channelCode")String channelCode);

    List<journalDayVO> countByDay(@Param("begin")String begin, @Param("end")String end,@Param("activityId")String activityId,@Param("channelCode")String channelCode);

    List<PointsJournalEmptyListVO> countEmpty(@Param("date") String format);


//    List<PointsJournalEmptyListVO> emptyList(@Param("date")String format);
//
//    List<PointsJournalCumulativeVO> cumulativeList(@Param("date")String format);
//
//    PointsJournalCumulativeVO cumulative(@Param("date")String format,@Param("uid")String uid,@Param("countryId")Long countryId);

    /**
     * 获取某个时间内积分综合
     * @param date
     * @param type
     * @param
     * @param channelCode
     * @return
     */
    Integer sumPoints(@Param("date") String date, @Param("uid") String type, @Param("countryId") Long countryId, @Param("channelCode")String channelCode);


    //统计用户账号
    List<PointsAccountVO> findAccount();

    //根据当前积分账户 查询积分异常流水
    List<PointsAbnDataVO> findAbnDataListByAccount(@Param("account") PointsAccountVO accountVO, @Param("second") Integer second,@Param("genType")Integer genType);


    List<journalGroupVO> countGroup(@Param("channelCode") String channelCode);

    // 分日期统计积分导出
    List<JournalDayExportListVO> export(@Param("begin")String begin, @Param("end")String end, @Param("activityId")String activityId,@Param("channelCode")String channelCode);

    long getTotalAmountInRangeNum(@Param("countryId") Long countryId, @Param("rangeStart") BigDecimal rangeStart, @Param("rangeEnd") BigDecimal rangeEnd);
}
