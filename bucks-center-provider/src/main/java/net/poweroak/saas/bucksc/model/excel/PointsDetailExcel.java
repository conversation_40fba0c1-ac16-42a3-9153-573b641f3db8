package net.poweroak.saas.bucksc.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2024/4/30
 */
@Data
public class PointsDetailExcel {
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "用户邮箱")
    @ColumnWidth(20)
    private String email;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "国家/地区")
    @ColumnWidth(20)
    private String country;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "币种")
    @ColumnWidth(20)
    private String currency;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "来源渠道")
    @ColumnWidth(20)
    private String channelName;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "渠道活动")
    @ColumnWidth(20)
    private String activityName;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "变动类型")
    @ColumnWidth(20)
    private String genType;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "积分数额")
    @ColumnWidth(20)
    private String points;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "消费金额")
    @ColumnWidth(20)
    private String amount;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "时间")
    @ColumnWidth(20)
    private String genTime;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "业务单号")
    @ColumnWidth(20)
    private String orderId;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "积分说明")
    @ColumnWidth(20)
    private String desc;

}
