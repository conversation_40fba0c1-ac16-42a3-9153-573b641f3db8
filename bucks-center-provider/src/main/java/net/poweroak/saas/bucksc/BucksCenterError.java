package net.poweroak.saas.bucksc;

import net.poweroak.framework.api.AppMessage;
import net.poweroak.framework.api.util.ApplicationUtil;
import net.poweroak.framework.app.MicroServiceApplication;
import net.poweroak.framework.i18n.Internationalization;
import net.poweroak.framework.interfaces.MessageID;

/**
 * Bluetti SaaS 积分中心服务错误消息定义<br/>
 * 错误码范围：20112000 ~ 20112999
 *
 * <AUTHOR>
 * @Mailto <EMAIL>
 * @Create 2022-05-04 17:19:55
 */
@Internationalization(app = MicroServiceApplication.bluBucksCenter)
public enum BucksCenterError implements AppMessage, MessageID {

    /**
     * 渠道名称已存在
     */
    CHANNEL_NAME_CANNOT_BE_REPEATED("渠道名称已存在", "180abec80c4f791157c8280ba71",
            BucksCenterApp.I18N_CODE_PREFIX + "Channel_name_cannot_be_repeated"),


    PLEASE_DELETE_THE_POINTS_ACTIVITY_ASSOCIATED_WITH_IT_FIRST("删除渠道失败,请先删除与之关联的积分活动", "18239b28f46f79115229d154ae0", BucksCenterApp.I18N_CODE_PREFIX + "Please delete the points activity associated with it first"),
    SHOP_TYPE_CHANNEL_CANNOT_BE_DELETED("店铺渠道无法删除", "18239e5f162f79115229d154ae1", BucksCenterApp.I18N_CODE_PREFIX + "Shop type channels cannot be deleted"),

    USER_IS_NOT_EXIST("根据uid查询不到用户信息", "18104959a24f791157c8280bb8a", BucksCenterApp.I18N_CODE_PREFIX + "User_is_not_exist"),

    /**
     * 渠道标识已存在
     */
    CHANNEL_CODE_CANNOT_BE_REPEATED("渠道标识已存在", "180abecbb95f791157c8280ba72",
            BucksCenterApp.I18N_CODE_PREFIX + "Channel_CODE_cannot_be_repeated"),

    /**
     * 文案代码已存在
     */
    DOCUMENT_CODE_CANNOT_BE_REPEATED("文案代码已存在", "1821e9c4a7bf79115229d154a88",
            BucksCenterApp.I18N_CODE_PREFIX + "Document_code_cannot_be_repeated"),

    /**
     * 语种代码已存在
     */
    LOCALE_CODE_CANNOT_BE_REPEATED("语种代码已存在", "1886684d01cf791157564c2ec82",
            BucksCenterApp.I18N_CODE_PREFIX + "Locale_code_cannot_be_repeated"),

    /**
     * 未查询到来源渠道详情数据
     */
    THE_SOURCE_CHANNEL_DETAILS_DATA_WERE_NOT_QUERIED("未查询到来源渠道详情数据", "180abfcfe9ff791157c8280ba75",
            BucksCenterApp.I18N_CODE_PREFIX + "The_source_channel_details_data_were_not_queried"),


    /**
     * 未查询到文案相关数据
     */
    THE_SOURCE_DOCUMENT_DETAILS_DATA_WERE_NOT_QUERIED("未查询到文案相关数据", "1821ea5296ff79115229d154a89",
            BucksCenterApp.I18N_CODE_PREFIX + "The_source_document_details_data_were_not_queried"),

    /**
     * 未查询到语种相关数据
     */
    THE_LOCALE_DETAILS_DATA_WERE_NOT_QUERIED("未查询到语种相关数据", "188668ce712f791157564c2ec83",
            BucksCenterApp.I18N_CODE_PREFIX + "The_locale_details_data_were_not_queried"),

    /**
     * 存在该文案类型的文案信息,请先删除文案信息
     */
    THERE_IS_A_DOCUMENT_INFO_FOR_THIS_DOCUMENTS_TYPE("存在该文案类型的文案信息,请先删除文案信息", "182387b7607f79115229d154ad8",
            BucksCenterApp.I18N_CODE_PREFIX + "Ther_is_a_document_info_for_this_documents_type"),

    /**
     * 存在该语种的文案信息,请先删除文案信息
     */
    THERE_IS_A_LOCALE_FOR_THIS_DOCUMENTS_TYPE("存在该语种的文案信息,请先删除文案信息", "18866c0c99cf791157564c2ec8b",
            BucksCenterApp.I18N_CODE_PREFIX + "Ther_is_a_locale_for_this_documents_type"),

    /**
     * 来源渠道标识不能为空
     */
    CHANNEL_CODE_CANNOT_BE_EMPTY("渠道标识不能为空", "180ac14775ff791157c8280ba77",
            BucksCenterApp.I18N_CODE_PREFIX + "Channel_code_cannot_be_empty"),

    /**
     * 文案代码不能为空
     */
    DOCUMENT_CODE_CANNOT_BE_EMPTY("文案代码不能为空", "1821e95c34cf79115229d154a86",
            BucksCenterApp.I18N_CODE_PREFIX + "Document_code_cannot_be_empty"),

    /**
     * 文案代码不能为空
     */
    LOCALE_CODE_CANNOT_BE_EMPTY("语种代码不能为空", "18866807a46f791157564c2ec81",
            BucksCenterApp.I18N_CODE_PREFIX + "Locale_code_cannot_be_empty"),


    /**
     * 来源渠道名称不能为空
     */
    CHANNEL_NAME_CANNOT_BE_EMPTY("渠道名称不能为空", "180ac1446c0f791157c8280ba76",
            BucksCenterApp.I18N_CODE_PREFIX + "Channel_name_cannot_be_empty"),


    /**
     * 文案名称不能为空
     */
    DOCUMENT_NAME_CANNOT_BE_EMPTY("文案名称不能为空", "1821e96bdc5f79115229d154a87",
            BucksCenterApp.I18N_CODE_PREFIX + "Document_name_cannot_be_empty"),

    LOCALE_NAME_CANNOT_BE_EMPTY("语种名称不能为空", "188667ffc58f791157564c2ec80",
            BucksCenterApp.I18N_CODE_PREFIX + "Locale_name_cannot_be_empty"),

    /**
     * 文案不存在
     */
    DOCUMENT_NOT_EXISTS("文案不存在", "1823d71be30f79115229d154ae3", BucksCenterApp.I18N_CODE_PREFIX + "Document_not_exists"),
    DOCUMENT_CODE_ACT_NOT_EMPTY("文案代码和活动编码不能同时为空", "1823d74734df79115229d154ae4", BucksCenterApp.I18N_CODE_PREFIX + "Document_code_act_not_empty"),
    POINTS_VALUE_CHECK_ERROR("积分值校验失败", "180abe1a1e5b1f7911571c828ba1", BucksCenterApp.I18N_CODE_PREFIX + "points_value_check_error"),
    RULE_EXISTED("规则已存在", "180abea1e5bf791157c8280ba6c", BucksCenterApp.I18N_CODE_PREFIX + "rule_existed"),
    ABN_RULE_EXISTED("积分预警规则已存在", "180abea1e5bf791157c8280aa1c", BucksCenterApp.I18N_CODE_PREFIX + "abn_rule_existed"),
    POINTS_ACCOUNT_NOT_EXIST("积分账户不存在", "180ad689975f791157c8280ba84", BucksCenterApp.I18N_CODE_PREFIX + "points_account_not_exist"),
    SPEND_RULE_NOT_EXISTS("消耗规则不存在", "180b2425b8cf791157c82802a93", BucksCenterApp.I18N_CODE_PREFIX + "SPEND_RULE_NOT_EXISTS"),
    POINTS_BACK_ERROR("积分回退失败", "180b2425b8cf791157c31a9132", BucksCenterApp.I18N_CODE_PREFIX + "points_back_error"),
    MORE_THAN_AVAILABLE_POINTS("积分余额不足", "180b2425b8cf791157c3119233", BucksCenterApp.I18N_CODE_PREFIX + "more_than_available_points"),
    POINTS_ACTIVITY_DETAILS_ARE_NOT_QUERIED("未查询到积分活动详情数据", "180ac871ba9f791157c8280ba79", BucksCenterApp.I18N_CODE_PREFIX + "Points_activity_details_are_not_queried"),
    ACTIVITY_CHANNEL_ALREADY_EXISTS("同一国家已存在相同的活动渠道", "180ac875ecaf791157c8280ba7a", BucksCenterApp.I18N_CODE_PREFIX + "Activity_channel_already_exists"),
    ACTIVITY_NAME_ALREADY_EXISTS("国家或渠道已存在相同的活动名称", "180ac89fd66f791157c8280ba7b", BucksCenterApp.I18N_CODE_PREFIX + "Activity_name_already_exists"),
    ACTIVITY_NAME_ALREADY_EXISTS_IN_SAME_COUNTRY_AND_SAME_CHANNEL("同一国家、渠道已存在相同的活动名称", "180b0f304b8f791157c8280ba8a", BucksCenterApp.I18N_CODE_PREFIX + "Activity_name_already_exists_in_same_country_and_channel"),
    COUNTRY_REGION_CANNOT_BE_EMPTY("国家/地区不能为空", "180ac914c02f791157c8280ba7c", BucksCenterApp.I18N_CODE_PREFIX + "Country_region_cannot_be_empty"),
    SPEND_AND_EARN_RULES_CANNOT_BE_EMPTY_AT_THE_SAME_TIME("发放或消耗适用规则不能为空", "180ac948be0f791157c8280ba7d", BucksCenterApp.I18N_CODE_PREFIX + "Spend_and_earn_rules_cannot_be_empty_at_the_same_time"),
    ACTIVITY_NAME_CANNOT_BE_EMPTY("活动名称不能为空", "180ac9b28e9f791157c8280ba7e", BucksCenterApp.I18N_CODE_PREFIX + "Activity_name_cannot_be_empty"),
    COUNTRY_CANNOT_BE_EMPTY("国家不能为空", "180b14057e2f791157c8280ba8e", BucksCenterApp.I18N_CODE_PREFIX + "Country_cannot_be_empty"),
    BALANCE_CANNOT_BE_EMPTY("余额不能为空", "181eb02f73df79115229d154960", BucksCenterApp.I18N_CODE_PREFIX + "Balance_cannot_be_empty"),
    SPEND_CANNOT_BE_EMPTY("消耗不能为空", "181eb03ccdff79115229d154961", BucksCenterApp.I18N_CODE_PREFIX + "Spend_cannot_be_empty"),
    EARN_CANNOT_BE_EMPTY("发放不能为空", "181eb043e19f79115229d154962", BucksCenterApp.I18N_CODE_PREFIX + "Earn_cannot_be_empty"),
    UID_ALREADY_EXISTS_IN_SAME_COUNTRY("同一国家已存在相同uid", "181eada22def79115229d15495f", BucksCenterApp.I18N_CODE_PREFIX + "Uid_already_exists_in_same_country"),
    EARN_RULE_ACTIVITY_HAS_EXISTS("活动已经存此规则", "180ad23bafbf791157c8280ba81", BucksCenterApp.I18N_CODE_PREFIX + "earn_rule_activity_has_exists"),
    EARN_RULE_NOT_EXISTS("发放规则不存在", "180b2425b8cf791157c8280ba92", BucksCenterApp.I18N_CODE_PREFIX + "earn_rule_not_exists"),
    EARN_RULE_OVER_LIMIT("发放超过次数", "180b2a14baaf791157c8280ba93", BucksCenterApp.I18N_CODE_PREFIX + "earn_rule_over_limit"),
    EARN_RULE_HAS_SEND("已经发放过了", "18151ea953df791157c8280bc3a", BucksCenterApp.I18N_CODE_PREFIX + "earn_rule_has_send"),
    EARN_SIGN_HAS_EXISTS("今天已签到", "18138b0358ff791157c8280bbde", BucksCenterApp.I18N_CODE_PREFIX + "earn_sign_has_exists"),
    EARN_RULE_CONDITION_NOT_COMPLETE("不满足发放门槛", "180b2b39f64f791157c8280ba95", BucksCenterApp.I18N_CODE_PREFIX + "earn_rule_condition_not_complete"),
    CHANNEL_NOT_EXISTS("渠道不存在", "180b214f183f791157c8280ba91", BucksCenterApp.I18N_CODE_PREFIX + "channel_not_exists"),
    POINTS_BALANCE_HAS_BAN("积分账户被禁", "180b5e07acbf791157c8280ba9a", BucksCenterApp.I18N_CODE_PREFIX + "points_balance_has_ban"),
    REQUEST_SO_FAST("请求太频繁，请稍后再试。", "180db9bde44f791157c8280bafa", BucksCenterApp.I18N_CODE_PREFIX + "request_so_fast"),
    LOCALE_ALREADY_EXISTS_IN_SAME_ACTIVITY_AND_COUTNRY_AND_CHANNEL_AND_DOCUMENT_TYPE("同一国家，渠道，活动,文案类型已经存在相同语种", "1885c657851f791157564c2ec76", BucksCenterApp.I18N_CODE_PREFIX + "Locale_already_exists_in_same_activity_and_country_and_channel_and_document_type"),
    DOCUMENT_TYPE_ALREADY_EXISTS_IN_SAME_ACTIVITY_AND_COUTNRY_AND_CHANNEL("同一国家，渠道，活动已经存在相同文案类型", "18237fda786f79115229d154ad2", BucksCenterApp.I18N_CODE_PREFIX + "Document_type_already_exists_in_same_activity_and_country_and_channel"),
    ACTIVITY_IS_NOT_EXISTS("活动不存在", "180ad5da90df791157c8280ba83", BucksCenterApp.I18N_CODE_PREFIX + "Activity_is_not_exists"),
    ACTIVITY_IS_EXPIRE("活动已过期", "18d3a72af06f791151b3250177c", BucksCenterApp.I18N_CODE_PREFIX + "activity_is_expire"),
    ACTIVITY_NOT_START("活动尚未开始", "18d3aac36c1f791151b3250177d", BucksCenterApp.I18N_CODE_PREFIX + "activity_is_not_start"),
    DOCUMENT_TYPE_CANNOT_BE_EMPTY("文案类型不能为空", "18237fa03bef79115229d154ad1", BucksCenterApp.I18N_CODE_PREFIX + "Document_type_cannot_be_empty"),
    ACTIVITY_CODE_CANNOT_BE_EMPTY("活动不能为空", "182149c1647f79115229d154a7c", BucksCenterApp.I18N_CODE_PREFIX + "Activity_code_cannot_be_empty"),
    COUNTRY_CODE_CANNOT_BE_EMPTY("国家不能为空", "182149da35ff79115229d154a7d", BucksCenterApp.I18N_CODE_PREFIX + "Country_code_cannot_be_empty"),
    BALANCE_IS_NOT_EXISTS("用户不存在", "180b10ec8aff791157c8280ba8c", BucksCenterApp.I18N_CODE_PREFIX + "Balance is not exists"),
    UID_CANNOT_BE_EMPTY("uid不能为空", "180b13ed795f791157c8280ba8d", BucksCenterApp.I18N_CODE_PREFIX + "Uid_cannot_be_empty"),
    ACTIVITY_CODE_IS_ALREADY_EXISTS("已存在相同code值", "1811eb54064f791157c8280bbc8", BucksCenterApp.I18N_CODE_PREFIX + "Activity_code_is_already_exists"),

    DATA_NOT_EXISTS("数据不存在", "18174736412f791157c8280bc85", BucksCenterApp.I18N_CODE_PREFIX + "data_not_exists"),
    AMOUNT_AND_LEVEL_SORT_ERR("累计消费额不能一致", "18220128201f79115229d154a9b", BucksCenterApp.I18N_CODE_PREFIX + "amount_and_sort_err"),
    LEVEL_CANT_NOT_SAME("等级不能相同", "182201ad3e3f79115229d154a9d", BucksCenterApp.I18N_CODE_PREFIX + "level_not_same"),
    EARN_LIMIT_ERR("门槛不能一致", "1822012479af79115229d154a99", BucksCenterApp.I18N_CODE_PREFIX + "earn_limit_err"),
    RATE_LIMIT_ERR("比例不能一致", "1822012593af79115229d154a9a", BucksCenterApp.I18N_CODE_PREFIX + "earn_rate_limit_err"),
    DATE_FORMAT_ERROR("日期格式错误", "181ec4335bff79115229d154969", BucksCenterApp.I18N_CODE_PREFIX + "date_format_error"),

    EMAIL_REPEAT("邮件不能重复", "181d14c5543f79115229d15494a", BucksCenterApp.I18N_CODE_PREFIX + "email_repeat"),
    LOTTERY_SPEND_RULE_NOT_EXITS("抽奖消耗规则不存在", "181d14c5543f712151219d154f4b", BucksCenterApp.I18N_CODE_PREFIX + "lottery_spend_rule_not_exits"),

    DUPLICATE_SUBMISSION("请勿重复提交", "18ba3bc1fdff79115700ffbf69f", BucksCenterApp.I18N_CODE_PREFIX + "duplicate_submission"),
    EMAIL_FORMAT_ERROR("邮箱格式不正确", "18ba3d7e56ef79115700ffbf6a0", BucksCenterApp.I18N_CODE_PREFIX + "email_format_error"),
    ILLEGAL_PARAMETER_PASSING("非法入参 {0}", "18ba77f6b9df79115700ffbf6a3", BucksCenterApp.I18N_CODE_PREFIX + "illegal_parameter_passing"),
    REGISTRATION_NOT_EXISTS("申请不存在", "18ba8acc7a7f79115700ffbf6aa", BucksCenterApp.I18N_CODE_PREFIX + "registration_not_exists"),
    ERROR_STATUS("状态异常 {0}", "18ba8ba34cbf79115700ffbf6ab", BucksCenterApp.I18N_CODE_PREFIX + "error_status"),
    EXPORT_EXCEL_ERROR("导出表格失败", "18bb2ad4a07f79115700ffbf6d1", BucksCenterApp.I18N_CODE_PREFIX + "export_excel_error"),
    MATERIAL_IMPORT_FILE_IS_ERROR("无法识别导入字段，导入失败，请按模板导入", "18bb740f7d3f79115700ffbf6ef", BucksCenterApp.I18N_CODE_PREFIX + "Repertory.material_import_file_is_error"),
    IMPORT_EXCEL_ERROR("导入表格失败 {0}", "18bb75efbe6f79115700ffbf6f3", BucksCenterApp.I18N_CODE_PREFIX + "import_excel_error"),

    POINTS_LEVEL_COUNTRY_DUPLICATE("当前国家已配置了积分等级", "18f4ccf638ef791153a32a14109", BucksCenterApp.I18N_CODE_PREFIX + "points_level_country_duplicate"),
    POINTS_LEVEL_IS_EMPTY("积分等级最少需要有一个基础等级存在", "18fb8b92e4dbc91b2060c9d8c9e", BucksCenterApp.I18N_CODE_PREFIX + "at_least_one_base_level_must_exist"),
    CONSUMPTION_THRESHOLD_MUST_INCREASED("积分等级的消费门槛需要设置为递增", "18fe689e9eebc91b2060c9d8d16", BucksCenterApp.I18N_CODE_PREFIX + "consumption_threshold_must_increased"),


    POINTS_CHANNEL_CODE_EMPTY("渠道不能为空", "18f94972612f791153a32a142de", BucksCenterApp.I18N_CODE_PREFIX + "points_channel_code_empty"),
    POINTS_CHANNEL_CODE_NOT_EXIST("渠道不存在", "18f949e5b1df791153a32a142df", BucksCenterApp.I18N_CODE_PREFIX + "points_channel_code_not_exist"),
    POINTS_COUNTRY_CODE_NOT_EXIST("国家不存在", "18f94a1e1c0f791153a32a142e0", BucksCenterApp.I18N_CODE_PREFIX + "points_country_code_not_exist"),
    POINTS_BEGIN_END_TIME_ERROR("需同时输入开始和结束时间!", "18f98c93b50f791153a32a142e7", BucksCenterApp.I18N_CODE_PREFIX + "points_begin_end_time_error"),
    POINTS_BEGIN_TO_END_TIME_ERROR("开始时间要小于结束时间!", "18f98d5ed93f791153a32a142e8", BucksCenterApp.I18N_CODE_PREFIX + "points_begin_to_end_time_error"),
    POINTS_PAGE_PARAM_ERROR("缺少分页参数，或分页参数为0", "18f990b9151f791153a32a142e9", BucksCenterApp.I18N_CODE_PREFIX + "points_page_param_error"),
    POINTS_PAGE_NUMBER_PARAM_ERROR("当前页需大于0", "18f990f64b3f791153a32a142ea", BucksCenterApp.I18N_CODE_PREFIX + "points_page_number_param_error"),
    POINTS_PAGE_SIZE_PARAM_ERROR("页数据条数需大于1", "18f99100d46f791153a32a142eb", BucksCenterApp.I18N_CODE_PREFIX + "points_page_size_param_error"),
    POINTS_PAGE_DETAIL_ID_EMPTY("详情ID不能为空", "18f99b77267f791153a32a142ed", BucksCenterApp.I18N_CODE_PREFIX + "points_page_detail_id_empty"),
    POINTS_ACTIVITY_IS_ERROR("活动不存在", "18f9a56b3dff791153a32a142f1", BucksCenterApp.I18N_CODE_PREFIX + "points_activity_is_error"),
    POINTS_ACTIVITY_IS_EMPTY("活动不能为空", "18f9a593158f791153a32a142f2", BucksCenterApp.I18N_CODE_PREFIX + "points_activity_is_empty"),
    POINTS_ORDER_IS_EMPTY("订单号不能为空", "18f9a59aecff791153a32a142f3", BucksCenterApp.I18N_CODE_PREFIX + "points_order_is_empty"),

    //积分批量导入
    FILE_IS_EMPTY("文件为空", "192f4f4daf7bc91b2119698bf33", BucksCenterApp.I18N_CODE_PREFIX + "file_is_empty"),
    THE_INTEGRAL_ENTERED_IS_INCORRECT("输入的积分有误", "192f60ead6dbc91b2119698bf3f", BucksCenterApp.I18N_CODE_PREFIX + "the_integral_entered_is_incorrect"),
    EMAIL_ERROR("输入的邮箱有误", "192f610254abc91b2119698bf40", BucksCenterApp.I18N_CODE_PREFIX + "email_error"),
    ;


    private int code;
    private String i18nCode;
    private String messageZhCn;
    private String messageId;

    private static final int START_ERROR_CODE = 20112000;

    /**
     * 财务中心微服务国际化消息
     *
     * @param messageZhCn 业务错误消息的默认本地化(中文)文本消息
     * @param messageId   业务错误消息预定义的消息ID
     * @param i18nCode    业务错误消息的国际化Code
     */
    BucksCenterError(String messageZhCn, String messageId, String i18nCode) {
        this.messageZhCn = messageZhCn;
        this.messageId = messageId;
        this.i18nCode = i18nCode;
    }

    /**
     * 获取消息定义的全局唯一ID
     *
     * @return
     */
    @Override
    public String getMsgId() {
        return this.messageId;
    }

    /**
     * 获取消息错误码
     *
     * @return
     */
    @Override
    public int getMsgCode() {
        this.code = this.ordinal() + START_ERROR_CODE;
        return this.code;
    }

    /**
     * 获取消息的国际化Code
     *
     * @return
     */
    @Override
    public String getI18nCode() {
        return this.i18nCode;
    }

    /**
     * 获取消息的默认本地化文本消息
     *
     * @return
     */
    @Override
    public String getMessage() {
        return this.messageZhCn;
    }

    /**
     * 获取应用服务标识
     *
     * @return
     */
    @Override
    public String getAppId() {
        return ApplicationUtil.getApplicationNameReal();
    }
}
