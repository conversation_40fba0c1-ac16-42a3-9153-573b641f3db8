package net.poweroak.saas.bucksc.controller.web;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.enums.PlatformType;
import net.poweroak.saas.bucksc.model.TO.PointsExchangeByOrderTO;
import net.poweroak.saas.bucksc.model.VO.UserDefaultVO;
import net.poweroak.saas.bucksc.service.IPointsExchangeRegistrationService;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2023/11/3
 */
@Api(tags = "登记购买信息兑积分")
@RestController
@RequiredArgsConstructor
@RequestMapping("/" + RouteEndpoint.APP + "/pointsExchangeByOrder")
public class PointsExchangeByOrderController {

    private final IPointsExchangeRegistrationService registrationService;

    @PostMapping("/v1/add")
    @ApiOperation(value = "新增一条兑换申请记录", httpMethod = "POST")
    public UnifyResponse<Boolean> add(@RequestBody @Valid PointsExchangeByOrderTO params) {
        registrationService.add(params);
        return new UnifyResponse<>(Boolean.TRUE);
    }

    @GetMapping("/v1/platformList")
    @ApiOperation(value = "获取平台列表", httpMethod = "GET")
    public UnifyResponse<List<String>> platformList() {
        return new UnifyResponse<>(PlatformType.toList());
    }

    @GetMapping("/v1/getInfo")
    @ApiOperation(value = "获取初始化信息(默认邮箱、币种)", httpMethod = "GET")
    public UnifyResponse<UserDefaultVO> initInfo() {
        return new UnifyResponse<>(registrationService.initInfo());
    }
}
