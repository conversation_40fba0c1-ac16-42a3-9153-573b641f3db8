package net.poweroak.saas.bucksc.controller.mgt;

import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.BluBucksCenterApplication;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.data.perms.norm.RouteMenu;
import net.poweroak.framework.pedestal.annotate.RequirePermission;
import net.poweroak.framework.pedestal.annotate.RouteAction;
import net.poweroak.saas.bucksc.common.ActivityEarnCode;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.dao.RequestLogDao;
import net.poweroak.saas.bucksc.common.GenSubType;
import net.poweroak.saas.bucksc.model.PO.RequestLog;
import net.poweroak.saas.bucksc.model.TO.PointsEranCommTO;
import net.poweroak.saas.bucksc.model.TO.RequestLogSearchTO;
import net.poweroak.saas.bucksc.model.VO.RequestLogListVO;
import net.poweroak.saas.bucksc.request.PointsEarnCommRequest;
import net.poweroak.saas.bucksc.request.PointsEarnExchangeRequest;
import net.poweroak.saas.bucksc.request.PointsEarnPvPowerRequest;
import net.poweroak.saas.bucksc.request.PointsEarnSignRequest;
import net.poweroak.saas.bucksc.service.IPointsEarnService;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/6/18 9:36
 */
@Slf4j
@ApiIgnore
@RequestMapping("/" + RouteEndpoint.MGT + "/requestLog")
@Api(tags = "请求失败日志")
@RestController
@RouteMenu(module = "requestLogModule", label = "发放请求失败日志", parent = BluBucksCenterApplication.class, order = 999)
public class RequestLogController {
    @Resource
    RequestLogDao requestLogDao;
    @Resource
    IPointsEarnService pointsEarnService;

    /**
     * 请求失败日志
     *
     * @param request
     * @return
     */
    @PostMapping("/pageList")
    @ApiOperation(value = "请求失败日志")
    @RouteAction(id = "1818feaa7f8f791157c8280bcb0", action = "page", label = "请求日志", view = "/BluBucksC/requestLog/list.vue", linkToModule = true)
    @RequirePermission
    public UnifyResponse<Pagination<RequestLogListVO>> pageList(@RequestBody RequestLogSearchTO request) {
        return new UnifyResponse<>(requestLogDao.getList(request));
    }


    /**
     * 重新请求
     *
     * @param id
     * @return
     */
    @GetMapping("/request/{id}")
    @ApiOperation(value = "重新请求")
    public UnifyResponse<Boolean> request(@PathVariable("id") String id) {
        Optional<RequestLog> optionalRequestLog = requestLogDao.findById(id);
        if (optionalRequestLog.isPresent()) {
            RequestLog requestLog = optionalRequestLog.get();
            ActivityEarnCode activityEarnCode = ActivityEarnCode.get(requestLog.getType());
            if (activityEarnCode != null) {
                RequestLog requestLogUpdate = new RequestLog();
                requestLogUpdate.setEnable(1);
                requestLogUpdate.setId(requestLog.getId());
                requestLogDao.updateById(requestLogUpdate);

                Object request = null;
                if (activityEarnCode.getRuleType().equals(ActivityEarnCode.SIGN.getRuleType())) {
                    request = JSONUtil.toBean(requestLog.getRequestParams(), PointsEarnSignRequest.class);
                } else if (activityEarnCode.getRuleType().equals(ActivityEarnCode.CURRENCY_EXCHANGE.getRuleType())) {
                    request = JSONUtil.toBean(requestLog.getRequestParams(), PointsEarnExchangeRequest.class);
                } else if (activityEarnCode.getRuleType().equals(ActivityEarnCode.PV_POWER.getRuleType())) {
                    request = JSONUtil.toBean(requestLog.getRequestParams(), PointsEarnPvPowerRequest.class);
                } else if (activityEarnCode.getRuleType().equals(ActivityEarnCode.COMMON.getRuleType())) {
                    request = JSONUtil.toBean(requestLog.getRequestParams(), PointsEarnCommRequest.class);
                }
                PointsEranCommTO pointsEranCommTO = new PointsEranCommTO();
                BeanUtils.copyProperties(request, pointsEranCommTO);
                pointsEarnService.send(pointsEranCommTO, activityEarnCode, GenSubType.match(requestLog.getSubType()));
            }
        }
        return new UnifyResponse<>(true);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @PostMapping("/delete/{id}")
    @ApiOperation(value = "删除")
    @RouteAction(id = "18204ff9a1bf79115229d154a59", action = "delete", label = "删除")
    @RequirePermission
    public UnifyResponse<Boolean> delete(@PathVariable("id") String id) {
        requestLogDao.deleteById(id);
        return new UnifyResponse<>(true);
    }
}
