package net.poweroak.saas.bucksc.service;

import net.poweroak.saas.bucksc.common.ActivityEarnCode;
import net.poweroak.saas.bucksc.common.GenSubType;
import net.poweroak.saas.bucksc.model.TO.PointsEarnSendTO;
import net.poweroak.saas.bucksc.model.TO.PointsEranCommTO;
import net.poweroak.saas.bucksc.model.VO.PointsEarnCommonVO;
import net.poweroak.saas.bucksc.response.PointsEarnResponse;


/**
 * <AUTHOR>
 * @date 2022/5/31
 */
public interface IPointsEarnService {
    /**
     * 发放积分
     *
     * @param pointsEranCommTO
     * @param activityEarnCode
     * @param genSubType
     * @return
     */
    PointsEarnResponse send(PointsEranCommTO pointsEranCommTO, ActivityEarnCode activityEarnCode, GenSubType genSubType);

    /**
     * 风控
     *
     * @param pointsEranCommTO
     * @return
     */
    PointsEarnCommonVO intervene(PointsEarnSendTO pointsEranCommTO);


    /**
     * 登记
     *
     * @param pointsEranCommTO
     * @return
     */
    PointsEarnCommonVO registration(PointsEarnSendTO pointsEranCommTO, String orderId);

}
