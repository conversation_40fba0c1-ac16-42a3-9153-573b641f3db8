package net.poweroak.saas.bucksc.model.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/25 9:34
 * @description
 */
@Data
public class PointsJournalEmptyListVO {


    @ApiModelProperty(value = "用户id")
    private String uid;
    @ApiModelProperty(value = "国家编码")
    private Long countryId;
    @ApiModelProperty(value = "积分")
    private Integer points;




}
