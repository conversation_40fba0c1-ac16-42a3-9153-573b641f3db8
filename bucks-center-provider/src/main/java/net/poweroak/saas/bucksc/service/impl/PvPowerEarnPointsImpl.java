package net.poweroak.saas.bucksc.service.impl;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.exception.ApplicationRuntimeException;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.enums.OrderBy;
import net.poweroak.saas.bucksc.BucksCenterError;
import net.poweroak.saas.bucksc.common.ActivityEarnCode;
import net.poweroak.saas.bucksc.model.PO.PointsEarnRule;
import net.poweroak.saas.bucksc.model.TO.PointsEranCommTO;
import net.poweroak.saas.bucksc.model.VO.PointsEarnCommonVO;
import net.poweroak.saas.bucksc.service.AbstractEarnPointsService;
import net.poweroak.saas.bucksc.service.factory.PointsEarnFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/7/4 14:20
 */
@Slf4j
@Service
public class PvPowerEarnPointsImpl extends AbstractEarnPointsService {

    @Override
    public PointsEarnCommonVO calcPoints(PointsEranCommTO request) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsEarnRule::getActivityId).is(request.getActivityId()));
        query.addCriterion(Criterion.where(PointsEarnRule::getChannelCode).is(request.getChannelCode()));
        query.addCriterion(Criterion.where(PointsEarnRule::getRuleType).is(ActivityEarnCode.PV_POWER.getRuleType()));
        query.addCriterion(Criterion.where(PointsEarnRule::getEnable).is(1));
        if (request.getEarnLimit() == null) {
            request.setEarnLimit(BigDecimal.ZERO);
        }
        query.addCriterion(Criterion.where(PointsEarnRule::getEarnLimit).lte(request.getEarnLimit()));
        query.orderBy(PointsEarnRule::getEarnLimit, OrderBy.DESC);

        List<PointsEarnRule> pointsEarnRuleList = pointsEarnRuleDao.find(query);
        if (pointsEarnRuleList.size() == 0) {
            log.error("不满足发放门槛,pointsEarnRule == null,pv,参数：{}", JSONUtil.toJsonStr(request));
            throw new ApplicationRuntimeException(BucksCenterError.EARN_RULE_CONDITION_NOT_COMPLETE);
        }
        PointsEarnRule pointsEarnRule = pointsEarnRuleList.get(0);
        if (!pointsEarnRuleDao.checkEarnRuleCondition(request.getUid(), request.getCountryId(), pointsEarnRule)) {
            log.error("发放次数不足,pv,参数：{}", JSONUtil.toJsonStr(request));
            throw new ApplicationRuntimeException(BucksCenterError.EARN_RULE_OVER_LIMIT);
        }

        if (pointsJournalDao.hasSnExists(request.getSn(), request.getBeginTime(), request.getEndTime())) {
            log.error("此时间段已经发放过积分,pv,参数：{}", JSONUtil.toJsonStr(request));
            throw new ApplicationRuntimeException(BucksCenterError.EARN_RULE_HAS_SEND);
        }

        PointsEarnCommonVO pointsEarnCommonVO = new PointsEarnCommonVO();
        pointsEarnCommonVO.setPointsEarnRuleId(pointsEarnRule.getId());
        TreeMap<String, Integer> extraMap = new TreeMap<>();
        pointsEarnRuleList.stream().filter(item -> item.getExtraReward().compareTo(BigDecimal.ZERO) > 0).sorted(Comparator.comparing(PointsEarnRule::getExtraReward)).forEach(rule -> {
            if (pointsJournalDao.hasExtra(rule.getId(), request.getUid())) {
                extraMap.put(rule.getId(), rule.getExtraReward().intValue());
            }
        });
        pointsEarnCommonVO.setExtraRewordMap(extraMap);

        //向上取整
        BigDecimal points = pointsEarnRule.getPoints().multiply(request.getDegree()).setScale(0, RoundingMode.CEILING);
        pointsEarnCommonVO.setPoints(points.intValue());
        pointsEarnCommonVO.setRatio(pointsEarnRule.getPoints());
        return pointsEarnCommonVO;
    }

    @Override
    public void afterPropertiesSet() {
        PointsEarnFactory.register(ActivityEarnCode.PV_POWER.getModel(), this);
    }
}
