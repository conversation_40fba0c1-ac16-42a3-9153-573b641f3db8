package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.page.PageInfo;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2023/11/7
 */
@Data
public class PointsExchangeRegistrationPageTO extends PageInfo {

    @ApiModelProperty(value = "用户提交邮箱")
    private String email;

    @ApiModelProperty(value = "购买平台")
    private String platform;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty(value = "购买产品型号")
    private String model;

    @ApiModelProperty(value = "状态（1:待审核，2：已发放，3：不发放）")
    private Integer status;

    @ApiModelProperty(value = "用户注册国家的编码")
    private String countryCode;

}
