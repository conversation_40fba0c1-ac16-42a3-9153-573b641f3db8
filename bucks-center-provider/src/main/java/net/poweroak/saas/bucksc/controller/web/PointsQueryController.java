package net.poweroak.saas.bucksc.controller.web;

import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.data.user.ContextUtils;
import net.poweroak.framework.api.exception.ApplicationRuntimeException;
import net.poweroak.framework.pedestal.annotate.RouteAction;
import net.poweroak.iso.CountryCode;
import net.poweroak.iso.vo.country.CountryRich;
import net.poweroak.saas.bucksc.BucksCenterError;
import net.poweroak.saas.bucksc.client.PointsQueryClient;
import net.poweroak.saas.bucksc.common.ActivityEarnCode;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.dao.ActivityDao;
import net.poweroak.saas.bucksc.dao.FaqDao;
import net.poweroak.saas.bucksc.enums.JournalSubType;
import net.poweroak.saas.bucksc.model.PO.Activity;
import net.poweroak.saas.bucksc.model.TO.ActivitySearchTO;
import net.poweroak.saas.bucksc.model.VO.ActivityVO;
import net.poweroak.saas.bucksc.request.*;
import net.poweroak.saas.bucksc.response.*;
import net.poweroak.saas.bucksc.service.IPointsChange;
import net.poweroak.saas.bucksc.service.IPointsQueryService;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2022/5/12 14:09
 */
@RestController
@Api(tags = "积分查询api")
@AllArgsConstructor
@RequestMapping("/" + RouteEndpoint.API + "/query")
@Slf4j
public class PointsQueryController implements PointsQueryClient {

    private final IPointsQueryService pointsQuery;
    private final IPointsChange pointsChange;
    private final FaqDao faqDao;
    private final ActivityDao activityDao;

    /**
     * 分页查询积分活动
     * @param request
     * @return
     */
    @PostMapping("/pageList")
    @ApiOperation(value = "分页查询积分活动")
    public UnifyResponse<Pagination<ActivityVO>> pageList(@RequestBody ActivitySearchTO request) {
        return this.activityDao.pageList(request);
    }

    @Override
    public UnifyResponse<PointsBalanceTotalResponse> summary(PointsQueryRequest request) {
        request.setUid(getUid(request.getUid()));
        request.setCountryId(getCountryId(request.getCountryId()));
        return new UnifyResponse<>(pointsQuery.querySummary(request));
    }

    @Override
    public UnifyResponse<ActivityResponse> getActivity(@Valid ActivityRequest request) {
        ActivityResponse activityResponse = new ActivityResponse();
        Activity activity = activityDao.getActivity(request.getActivityCode(), request.getCountryId(), request.getChannelCode());
        if (activity != null) {
            BeanUtils.copyProperties(activity, activityResponse);
            return new UnifyResponse<>(activityResponse);
        }
        return new UnifyResponse<>();
    }

    @Override
    public UnifyResponse<PointsEarnRuleResponse> getEarnRule(@Valid PointsEarnRuleQueryRequest request) {
        return new UnifyResponse<>(pointsQuery.getEarnRule(request));
    }

    @Override
    public UnifyResponse<Pagination<PointsBalanceJournalResponse>> journal(PointsQueryJournalRequest request) {
        request.setUid(getUid(request.getUid()));
        request.setCountryId(getCountryId(request.getCountryId()));
        return new UnifyResponse<>(pointsQuery.getJournalPageRes(request));
    }

    @Override
    public UnifyResponse<PointsSpendResponse> deductRatio(DeductRatioRequest request) {
        return new UnifyResponse<>(pointsChange.deductRatio(request));
    }

    @Override
    public UnifyResponse<List<GenTypeResponse>> genTypeList(String lang) {
        if (log.isDebugEnabled()) {
            log.debug("传入语言:{}", lang);
        }
        return new UnifyResponse<>(pointsQuery.getGenTypeList(lang));
    }

//    @Override
//    public UnifyResponse<List<SignDaysResponse>> signDays(SignDaysRequest request) {
//        request.setUid(getUid(request.getUid()));
//        request.setCountryId(getCountryId(request.getCountryId()));
//        return new UnifyResponse<>(pointsQuery.getSignDays(getUid(request.getUid()), request.getCountryId(), request.getChannelCode(), DateUtil.parse(request.getDate())));
//    }

    @Override
    public UnifyResponse<SignInfoResponse> signInfo(SignInfoRequest request) {
        request.setUid(getUid(request.getUid()));
        request.setCountryId(getCountryId(request.getCountryId()));
        return new UnifyResponse<>(pointsQuery.getSignInfo(getUid(request.getUid()), request.getCountryId(), request.getChannelCode()));
    }

    @Override
    public UnifyResponse<Pagination<PointsBalanceJournalResponse>> signRecord(@Valid SignRecordRequest request) {
        request.setUid(getUid(request.getUid()));
        request.setCountryId(getCountryId(request.getCountryId()));
        PointsQueryJournalRequest pointsEarnRuleQueryRequest = new PointsQueryJournalRequest();
        BeanUtils.copyProperties(request, pointsEarnRuleQueryRequest);

        Activity activity = activityDao.getActivity(ActivityEarnCode.SIGN.getCode(), request.getCountryId(), request.getChannelCode());
        if (activity == null) {
            throw new ApplicationRuntimeException(BucksCenterError.ACTIVITY_IS_NOT_EXISTS);
        }
//        pointsEarnRuleQueryRequest.setActivityId(activity.getId());
        pointsEarnRuleQueryRequest.setSubType(JournalSubType.SIGN.getCode());
        pointsEarnRuleQueryRequest.setLang(ContextUtils.get().getLocale().toLanguageTag());
        pointsEarnRuleQueryRequest.setPage(request.getPageNumber());
        pointsEarnRuleQueryRequest.setSize(request.getPageSize());
        return new UnifyResponse<>(pointsQuery.getJournalPageRes(pointsEarnRuleQueryRequest));
    }

    @Override
    public UnifyResponse<List<RulesResponse>> rules(FaqRequest faqRequest) {
        return new UnifyResponse<>(faqDao.getResList(faqRequest));
    }

    @PostMapping("/v1/document")
    @ApiOperation("文案")
    public UnifyResponse<DocumentResponse> document(@RequestBody DocumentRequest documentRequest, @RequestHeader("Accept-Language") String languageParam) {
        log.info("请求参数：{},请求语言：{}", JSONUtil.toJsonStr(documentRequest), languageParam);
        String language = languageParam;
        if (languageParam.contains(",")) {
            language = languageParam.split(",")[0];
        }
        documentRequest.setCountryId(getCountryId(documentRequest.getCountryId()));
        return new UnifyResponse<>(pointsQuery.findOneDocument(documentRequest.getCountryId(), documentRequest.getChannelCode(), documentRequest.getDocumentCode(), documentRequest.getActivityCode(), language));
    }


    private String getUid(String uid) {
        if (Strings.isEmpty(uid)) {
            uid = ContextUtils.get().getUID();
            if (uid == null) {
                throw new ApplicationRuntimeException(BucksCenterError.USER_IS_NOT_EXIST);
            }
        }
        return uid;
    }

    private Long getCountryId(Long countryId) {
        if (countryId == null) {
            String countryCode = ContextUtils.get().getLocale().getCountry();
            if (Strings.isEmpty(countryCode)) {
                throw new ApplicationRuntimeException(BucksCenterError.COUNTRY_CODE_CANNOT_BE_EMPTY);
            }
            CountryRich countryRich = CountryCode.getByCountryCode(countryCode);
            countryId = countryRich.getGeoNameId();
        }
        return countryId;
    }

}
