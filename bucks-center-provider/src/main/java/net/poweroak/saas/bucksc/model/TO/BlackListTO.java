package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.page.PageInfo;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/27 8:59
 * @description
 */
@Data
public class BlackListTO extends PageInfo {
    @ApiModelProperty(value = "用户名字")
    private String uname;
    @ApiModelProperty(value = "用户邮箱")
    private String uemail;
    @ApiModelProperty(value = "用户联系电话")
    private String uphone;
    @ApiModelProperty(value = "国家编码")
    private Long countryId;

}
