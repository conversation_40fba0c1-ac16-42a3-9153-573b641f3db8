package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.saas.bucksc.model.PO.DocumentsInfo;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/7/25 17:16
 * @description
 */
@Data
public class DocumentsInfoUpdateTO {
    /**
     * 是否编辑
     */
    private boolean editFlag;

    @ApiModelProperty(value="活动编码")
    private String activityId;

    @ApiModelProperty(value="文案类型id")
    private String type;

    @ApiModelProperty(value="文案内容")
    private String content;


    @ApiModelProperty(value="渠道code")
    private String channelCode;

    @ApiModelProperty(value="国家code")
    private Long countryId;

    @ApiModelProperty(value="id")
    private String id;


    @ApiModelProperty(value="语种")
    private String localeCode;


}
