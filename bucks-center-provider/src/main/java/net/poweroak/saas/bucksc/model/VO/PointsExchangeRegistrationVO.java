package net.poweroak.saas.bucksc.model.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.sqltag.TableField;
import net.poweroak.saas.bucksc.model.PO.PointsExchangeRegistration;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2023/11/7
 */
@Data
public class PointsExchangeRegistrationVO {

    @TableField(table = PointsExchangeRegistration.class, field = "id")
    private String id;

    @TableField(table = PointsExchangeRegistration.class, field = "email")
    @ApiModelProperty(value = "用户提交邮箱")
    private String email;

    @TableField(table = PointsExchangeRegistration.class, field = "platform")
    @ApiModelProperty(value = "购买平台")
    private String platform;

    @TableField(table = PointsExchangeRegistration.class, field = "otherPlatform")
    @ApiModelProperty(value = "其他平台")
    private String otherPlatform;

    @TableField(table = PointsExchangeRegistration.class, field = "orderNumber")
    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @TableField(table = PointsExchangeRegistration.class, field = "model")
    @ApiModelProperty(value = "购买产品型号")
    private String model;

    @TableField(table = PointsExchangeRegistration.class, field = "amount")
    @ApiModelProperty(value = "订单金额")
    private BigDecimal amount;

    @TableField(table = PointsExchangeRegistration.class, field = "currency")
    @ApiModelProperty(value = "币种")
    private String currency;

    @TableField(table = PointsExchangeRegistration.class, field = "status")
    @ApiModelProperty(value = "状态（1:待审核，2：已发放，3：不发放）")
    private Integer status;

    @TableField(table = PointsExchangeRegistration.class, field = "comment")
    @ApiModelProperty(value = "备注")
    private String comment;

    @TableField(table = PointsExchangeRegistration.class, field = "verify")
    @ApiModelProperty(value = "系统校验结果")
    private String verify;

    @TableField(table = PointsExchangeRegistration.class, field = "countryCode")
    @ApiModelProperty(value = "用户注册国家的编码")
    private String countryCode;

    @TableField(table = PointsExchangeRegistration.class, field = "name")
    @ApiModelProperty(value = "用户的姓名")
    private String name;

    @TableField(table = PointsExchangeRegistration.class, field = "accountEmail")
    @ApiModelProperty(value = "用户账号邮箱")
    private String accountEmail;

    @TableField(table = PointsExchangeRegistration.class, field = "phone")
    @ApiModelProperty(value = "用户账号电话")
    private String phone;

}
