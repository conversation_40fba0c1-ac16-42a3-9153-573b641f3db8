package net.poweroak.saas.bucksc.controller.web;

import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.saas.bucksc.client.PointsChannelClient;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.controller.mgt.ChannelController;
import net.poweroak.saas.bucksc.dao.ChannelDao;
import net.poweroak.saas.bucksc.model.TO.ChannelAddTO;
import net.poweroak.saas.bucksc.request.PointsChannelRequest;
import net.poweroak.saas.bucksc.request.PointsEarnSignRequest;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/7/25 11:53
 * @description
 */
@RestController
@Api(tags = "渠道-api")
@AllArgsConstructor
@RequestMapping("/" + RouteEndpoint.API + "/PointsChannel")
public class PointsChannelController implements PointsChannelClient {
    private final ChannelDao channelDao;
    @Override
    public UnifyResponse<Boolean> add(PointsChannelRequest request) {
        return channelDao.insertShop(request);
    }
}
