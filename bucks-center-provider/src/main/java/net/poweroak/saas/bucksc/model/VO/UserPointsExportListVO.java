package net.poweroak.saas.bucksc.model.VO;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023/12/7 10:58
 */


@ApiModel(value = "用户积分导出的vo")
@Data
public class UserPointsExportListVO {

    @ExcelIgnore
    @ApiModelProperty(value = "用户ID")
    private String uid;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "用户昵称",index = 0)
    @ColumnWidth(20)
    @ApiModelProperty(value="用户昵称")
    private String username;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "用户邮箱",index = 1)
    @ColumnWidth(20)
    @ApiModelProperty(value="用户邮箱")
    private String email;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "注册时间",index = 2)
    @ColumnWidth(20)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value="注册时间")
    private String creTime;

    @ExcelIgnore
    @ApiModelProperty(value="国家/地区id")
    private Long countryId;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "国家/地区",index = 3)
    @ColumnWidth(20)
    @ApiModelProperty(value="国家/地区")
    private String countryName;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "累积发放",index = 4)
    @ColumnWidth(20)
    @ApiModelProperty(value = "累积发放")
    private Integer earn;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "累积消耗",index = 5)
    @ColumnWidth(20)
    @ApiModelProperty(value = "累积消耗")
    private Integer spend;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "余额",index = 6)
    @ColumnWidth(20)
    @ApiModelProperty(value = "余额")
    private Integer balance;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "消费金额")
    @ColumnWidth(20)
    private String amount;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "币种")
    @ColumnWidth(20)
    private String currency;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "累计过期积分")
    @ColumnWidth(20)
    private String expired;

    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "是否在黑名单")
    @ColumnWidth(20)
    @ApiModelProperty(value="是否在黑名单")
    private String isInBlackList;

}
