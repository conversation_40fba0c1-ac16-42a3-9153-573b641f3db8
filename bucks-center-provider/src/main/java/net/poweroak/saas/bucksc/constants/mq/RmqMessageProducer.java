package net.poweroak.saas.bucksc.constants.mq;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Mailto <EMAIL>
 * @Create 2024/5/9 11:00
 **/
@Component
@Slf4j
@RequiredArgsConstructor
public class RmqMessageProducer {

    private final static Integer DEFAULT_EXPIRATION_TIME = 3 * 24 * 3600;
    private final RabbitTemplate rabbitTemplate;

    /**
     * 生产着推送信息
     * @param message 推送信息
     * @param time 过期时间，单位秒：到过期时间会进入死信队列
     */
    @Async
    public void send(String message,Integer time){
        String ttl = String.valueOf(time == null ? DEFAULT_EXPIRATION_TIME * 1000 : time * 1000);
        rabbitTemplate.convertAndSend(RmqConfig.BUCKS_EXCHANGE, RmqConfig.BUCKS_ROUTEKEY,message, message1 -> {
            message1.getMessageProperties().setExpiration(ttl);
            return message1;
        });
        log.info("使用业务队列消息:{}发送成功,过期时间:{}秒。", message, time == null ? DEFAULT_EXPIRATION_TIME : time);
    }



}
