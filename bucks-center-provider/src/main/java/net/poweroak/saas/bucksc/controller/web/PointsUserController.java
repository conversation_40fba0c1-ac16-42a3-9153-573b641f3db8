package net.poweroak.saas.bucksc.controller.web;

import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.saas.bucksc.client.PointsUserClient;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.dao.PointsBalanceDao;
import net.poweroak.saas.bucksc.request.PointsQueryUserRequest;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @date 2022/5/12 14:09
 */
@RestController
@Api(tags = "积分用户-api")
@AllArgsConstructor
@RequestMapping("/" + RouteEndpoint.API + "/user")
public class PointsUserController implements PointsUserClient {

    private final PointsBalanceDao pointsBalanceDao;

    @Override
    public UnifyResponse<Boolean> reg(PointsQueryUserRequest request) {
        return new UnifyResponse<>(pointsBalanceDao.checkUserExists(request.getUid(), request.getCountryId()));
    }

}
