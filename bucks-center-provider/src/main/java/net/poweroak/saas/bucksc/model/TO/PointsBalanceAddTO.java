package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/11 11:49
 * @description
 */
@Data
public class PointsBalanceAddTO {

    public PointsBalanceAddTO() {
        this.shopAmount = BigDecimal.ZERO;
        this.pvAmount = BigDecimal.ZERO;
    }

    @ApiModelProperty(value = "用户ID")
    private String uid;

    @ApiModelProperty(value = "国家编码")
    private Long countryId;

    @ApiModelProperty(value = "累积消耗")
    private Integer spend;

    @ApiModelProperty(value = "累积发放")
    private Integer earn;

    @ApiModelProperty(value = "余额")
    private Integer balance;

    @ApiModelProperty(value = "零售商城金额")
    private BigDecimal shopAmount;

    @ApiModelProperty(value = "光伏发电度数")
    private BigDecimal pvAmount;

}
