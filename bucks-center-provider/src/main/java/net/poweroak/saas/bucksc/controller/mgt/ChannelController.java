package net.poweroak.saas.bucksc.controller.mgt;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.BluBucksCenterApplication;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.data.perms.norm.RouteMenu;
import net.poweroak.framework.pedestal.annotate.RequirePermission;
import net.poweroak.framework.pedestal.annotate.RouteAction;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.dao.ChannelDao;
import net.poweroak.saas.bucksc.model.PO.Channel;
import net.poweroak.saas.bucksc.model.TO.ChannelAddTO;
import net.poweroak.saas.bucksc.model.TO.ChannelTO;
import net.poweroak.saas.bucksc.model.TO.ChannelUpdateTO;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/7 16:13
 * @description
 */

@Slf4j
@ApiIgnore
@RestController
@RequestMapping("/" + RouteEndpoint.MGT + "/channel")
@Api(tags = "来源渠道")
@RouteMenu(module = "channelModule", label = "来源渠道管理", parent = BluBucksCenterApplication.class,order = 3)
public class ChannelController {

    @Resource
    private ChannelDao channelDao;


    /**
     * 分页查询渠道来源
     * @param request
     * @return
     */
    @PostMapping("/pageList")
    @ApiOperation(value = "分页查询渠道来源")
    @RouteAction(id = "180abb1f769f791157c8280ba67", action = "page", label = "积分来源渠道查询", view = "/BluBucksC/channel/list.vue", linkToModule = true)
    public UnifyResponse<Pagination<Channel>> pageList(@RequestBody ChannelTO request) {
        return this.channelDao.pageList(request);
    }

    /**
     * 新增渠道来源
     * @param request
     * @return
     * @throws Exception
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增来源渠道")
    @RouteAction(action = "add",id = "18205899764f79115229d154a64", label = "新增渠道来源")
    @RequirePermission
    public UnifyResponse<Boolean> add(@RequestBody ChannelAddTO request) throws Exception{
        return this.channelDao.add(request);
    }

    /**
     * 不分页查全部来源渠道
     * @return
     */
    @GetMapping("/list")
    @ApiOperation(value = "不分页查询来源渠道")
    public UnifyResponse<List<Channel>> list(){
        return this.channelDao.list();
    }


    /**
     * 不分页查存在的来源渠道
     * @return
     */
    @GetMapping("/existsList")
    @ApiOperation(value = "不分页查询来源渠道")
    public UnifyResponse<List<Channel>> existsList(){
        return this.channelDao.existsList();
    }

    /**
     * 删除来源渠道
     * @param id
     * @return
     */
    @GetMapping("/delete/{id}")
    @ApiOperation(value = "删除来源渠道")
    @RouteAction(action = "del",id = "18239a07cebf79115229d154adf", label = "删除渠道来源")
    @RequirePermission
    public UnifyResponse<Boolean> delete(@PathVariable("id") String id){
        return this.channelDao.deleteChannel(id);
    }

    /**
     * 更新来源渠道
     * @param request
     * @return
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新来源渠道")
    @RouteAction(action = "update",id = "182058aa01bf79115229d154a66", label = "更新渠道来源")
    @RequirePermission
    public UnifyResponse<Boolean> update(@RequestBody ChannelUpdateTO request){
        return this.channelDao.updateChannel(request);
    }

    /**
     * 根据id查询单个渠道
     * @param id
     * @return
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "来源渠道单个查询")
    public UnifyResponse<Channel> get(@PathVariable("id") String id) {
        return this.channelDao.getOne(id);
    }




}
