package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/7 9:15
 */
@Data
@ApiModel("规则列表")
public class PointsEarnRuleListTO {

    public PointsEarnRuleListTO() {
        this.sort = 1;
    }

    private String activityId;

    /**
     * 1= memberLevel 2=earnLimit 3=points
     */
    private Integer sort;
}
