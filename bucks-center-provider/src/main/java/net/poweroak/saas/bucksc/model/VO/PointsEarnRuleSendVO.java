package net.poweroak.saas.bucksc.model.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.saas.bucksc.model.PO.Activity;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @date 2022/5/7 9:15
 */
@Data
@ApiModel("积分发放结果")
public class PointsEarnRuleSendVO implements Serializable {

    @ApiModelProperty(value = "流水ID")
    private String journalId;

    @ApiModelProperty(value = "积分比例")
    private BigDecimal ratio;

    @ApiModelProperty(value = "发放积分")
    private Integer points;

    @ApiModelProperty(value = "用户积分余额")
    private Integer balance;
}
