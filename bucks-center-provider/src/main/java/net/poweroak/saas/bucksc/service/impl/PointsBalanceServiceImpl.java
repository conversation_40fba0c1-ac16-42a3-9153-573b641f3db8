package net.poweroak.saas.bucksc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.user.UserInfoForServer;
import net.poweroak.iso.CountryCode;
import net.poweroak.iso.vo.country.CountryRich;
import net.poweroak.saas.bucksc.dao.PointsJournalDao;
import net.poweroak.saas.bucksc.enums.GenType;
import net.poweroak.saas.bucksc.model.TO.PointsJournalTO;
import net.poweroak.saas.bucksc.model.VO.PointsJournalVO;
import net.poweroak.saas.bucksc.model.excel.PointsDetailExcel;
import net.poweroak.saas.bucksc.service.IPointsBalanceService;
import net.poweroak.saas.uc.client.UserClient;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2024/4/30
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PointsBalanceServiceImpl implements IPointsBalanceService {
    private final PointsJournalDao journalDao;
    private final UserClient userClient;

    /**
     * 积分详情导出
     */
    @Override
    public void exportDetail(PointsJournalTO params, HttpServletResponse response) {
        List<PointsJournalVO> detailList = journalDao.findDetailList(params);
        UnifyResponse<List<UserInfoForServer>> usersByIds = userClient.list(Arrays.asList(params.getUid()));
        final String email;
        if (usersByIds.isOK() && CollectionUtil.isNotEmpty(usersByIds.getData())) {
            email = usersByIds.getData().get(0).getEmail();
        } else {
            email = null;
        }
        CountryRich countryRich = CountryCode.getByGeoId(params.getCountryId());
        final String countryName;
        if (countryRich != null) {
            countryName = CountryCode.getByAlpha2Code(countryRich.getCountryCode()).getNameZh();
        } else {
            countryName = null;
        }
        List<PointsDetailExcel> excelList = detailList.stream().map(detail -> {
            PointsDetailExcel detailExcel = new PointsDetailExcel();
            detailExcel.setEmail(email);
            detailExcel.setCountry(countryName);
            detailExcel.setCurrency(detail.getCurrency());
            detailExcel.setChannelName(detail.getChannelName());
            detailExcel.setActivityName(detail.getActivityName());
            GenType genType = GenType.match(detail.getGenType());
            detailExcel.setGenType(genType != null ? genType.getMessageZhCn() : null);
            detailExcel.setPoints(detail.getPoints());
            detailExcel.setAmount(detail.getAmount() != null ? detail.getAmount().toString() : null);
            detailExcel.setGenTime(DateUtil.formatDateTime(detail.getGenTime()));
            detailExcel.setOrderId(detail.getOrderId());
            detailExcel.setDesc(detail.getDesc());
            return detailExcel;
        }).collect(Collectors.toList());


        try {
            excelWriteResponse("points-detail", response);
            EasyExcel.write(response.getOutputStream(), PointsDetailExcel.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("sheet1").doWrite(excelList);
        } catch (IOException e) {
            log.error("导出仓库收件失败", e);
        }
    }


    public static void excelWriteResponse(String fileName, HttpServletResponse response) throws UnsupportedEncodingException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", StrUtil.concat(Boolean.TRUE, "attachment;filename=", fileName, ExcelTypeEnum.XLSX.getValue()));
    }
}
