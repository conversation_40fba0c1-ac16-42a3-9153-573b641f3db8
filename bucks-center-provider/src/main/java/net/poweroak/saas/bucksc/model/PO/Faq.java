package net.poweroak.saas.bucksc.model.PO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.DomainEntity;
import net.poweroak.framework.sqltag.Table;

/**
 * <AUTHOR>
 * @date 2022/6/18 8:48
 */
@Data
@ApiModel("faq")
@Table(name = "faq")
public class Faq extends DomainEntity<String> {

    /**
     * title
     */
//    @ApiModelProperty(value = "国家编码")
//    private String countryCode;

    @ApiModelProperty(value="国家Id")
    private Long countryId;

    /**
     * title
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 内容
     */
    @ApiModelProperty(value = "内容")
    private String content;
}
