package net.poweroak.saas.bucksc.service;

import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.saas.bucksc.model.TO.PointsInterestsPageTO;
import net.poweroak.saas.bucksc.model.TO.PointsInterestsTO;
import net.poweroak.saas.bucksc.model.VO.PointsInterestsVO;

import java.util.List;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2024/5/7
 */
public interface IPointsInterestsService {
    void add(PointsInterestsTO params);

    Pagination<PointsInterestsVO> page(PointsInterestsPageTO params);

    void edit(PointsInterestsTO params);

    void delete(String id);

    List<PointsInterestsVO> list(Long countryId);
}
