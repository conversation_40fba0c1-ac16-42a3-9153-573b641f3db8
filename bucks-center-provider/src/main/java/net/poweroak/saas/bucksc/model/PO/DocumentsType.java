package net.poweroak.saas.bucksc.model.PO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.DomainEntity;
import net.poweroak.framework.sqltag.Table;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/7/21 9:55
 * @description
 */
@Data
@ApiModel("文案类型表")
@Table(name = "documents_type")
public class DocumentsType extends DomainEntity<String> {
    /**
     * 渠道名称
     */
    @ApiModelProperty(value = "文案名称")
    private String name;

    /**
     * 渠道标识
     */
    @ApiModelProperty(value = "文案代码")
    private String code;
}
