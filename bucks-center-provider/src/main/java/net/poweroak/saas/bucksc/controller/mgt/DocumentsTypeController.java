package net.poweroak.saas.bucksc.controller.mgt;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.data.perms.norm.RouteMenu;
import net.poweroak.framework.pedestal.annotate.RequirePermission;
import net.poweroak.framework.pedestal.annotate.RouteAction;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.dao.DocumentsTypeDao;
import net.poweroak.saas.bucksc.enums.DocumentType;
import net.poweroak.saas.bucksc.model.PO.DocumentsType;
import net.poweroak.saas.bucksc.model.TO.*;
import net.poweroak.saas.bucksc.model.VO.RuleTypeVO;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/7/21 10:01
 * @description
 */
@Slf4j
@ApiIgnore
@RequestMapping("/" + RouteEndpoint.MGT + "/DocumentsType")
@Api(tags = "文案类型")
@RestController
@RouteMenu(module = "DocumentsTypeModule", label = "文案类型", parent = DocumentsController.class)
public class DocumentsTypeController {


    @Resource
    private DocumentsTypeDao documentsTypeDao;

    /**
     * 分页查询
     *
     * @param request
     * @return
     */
    @PostMapping("/page")
    @ApiOperation(value = "文案类型分页列表")
    @RouteAction(id = "1821e7fbfb5f79115229d154a85", action = "page", label = "文案类型管理", view = "/BluBucksC/documentsType/list.vue", linkToModule = true)
    @RequirePermission
    public UnifyResponse<Pagination<DocumentsType>> pageList(@RequestBody DocumentsTypeTO request) {
        return this.documentsTypeDao.pageList(request);
    }
    /**
     * 新增
     *
     * @param request
     * @return
     */

    @PostMapping("/add")
    @ApiOperation(value = "新增文案类型")
    @RouteAction(id = "182335351e5f79115229d154ac2", action = "add", label = "新增")
    @RequirePermission
    public UnifyResponse<Boolean> add(@RequestBody DocumentsTypeAddTO request) throws Exception{
        return this.documentsTypeDao.add(request);
    }

    @GetMapping("/ruleList")
    public UnifyResponse<List<RuleTypeVO>> ruleList() {
        DocumentType[] activityEarnCodes = DocumentType.values();
        List<RuleTypeVO> list = new ArrayList<>();
        for (DocumentType activityEarnCode : activityEarnCodes) {
            RuleTypeVO ruleTypeVO = new RuleTypeVO();
            ruleTypeVO.setCode(activityEarnCode.getCode());
            ruleTypeVO.setName(activityEarnCode.getName());
            list.add(ruleTypeVO);
        }
        return new UnifyResponse<>(list);
    }




    /**
     * 根据id查询单个文案
     * @param id
     * @return
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "单个查询文案类型")
    public UnifyResponse<DocumentsType> get(@PathVariable("id") String id) {
        return this.documentsTypeDao.getOne(id);
    }

    /**
     * 更新文案类型
     * @param request
     * @return
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新文案类型")
    @RouteAction(id = "1823353ddf5f79115229d154ac4", action = "update", label = "编辑")
    @RequirePermission
    public UnifyResponse<Boolean> update(@RequestBody DocumentsTypeUpdateTO request){
        return this.documentsTypeDao.updateDocumentsType(request);
    }


    /**
     * 删除文案类型
     * @param id
     * @return
     */
    @GetMapping("/delete/{id}")
    @ApiOperation(value = "删除文案类型")
    @RouteAction(id = "18233540a91f79115229d154ac5", action = "delete", label = "删除")
    @RequirePermission
    public UnifyResponse<Boolean> delete(@PathVariable("id") String id){
        return this.documentsTypeDao.deleteDocumentsType(id);
    }



    /**
     * 不分页查文案类型
     * @return
     */
    @GetMapping("/list")
    @ApiOperation(value = "不分页查询文案类型")
    public UnifyResponse<List<DocumentsType>> list(){
        return this.documentsTypeDao.list();
    }


}
