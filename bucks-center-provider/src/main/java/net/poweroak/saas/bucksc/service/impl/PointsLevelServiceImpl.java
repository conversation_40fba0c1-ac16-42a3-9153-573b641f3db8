package net.poweroak.saas.bucksc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.data.user.ContextUtils;
import net.poweroak.framework.api.exception.ApplicationRuntimeException;
import net.poweroak.framework.user.UserInfoForServer;
import net.poweroak.iso.CountryCode;
import net.poweroak.iso.vo.country.CountryRich;
import net.poweroak.saas.bucksc.BucksCenterError;
import net.poweroak.saas.bucksc.dao.*;
import net.poweroak.saas.bucksc.model.PO.PointsBalance;
import net.poweroak.saas.bucksc.model.PO.PointsLevel;
import net.poweroak.saas.bucksc.model.PO.PointsLevelRule;
import net.poweroak.saas.bucksc.model.TO.PointsLevelPageTO;
import net.poweroak.saas.bucksc.model.TO.PointsLevelTO;
import net.poweroak.saas.bucksc.model.VO.*;
import net.poweroak.saas.bucksc.service.IPointsLevelService;
import net.poweroak.saas.bucksc.service.IPointsLevelUpgradeService;
import net.poweroak.saas.uc.client.UserClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.MathContext;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2024/5/6
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PointsLevelServiceImpl implements IPointsLevelService {

    private final PointsLevelDao levelDao;
    private final PointsLevelRuleDao levelRuleDao;
    private final PointsBalanceDao balanceDao;
    private final PointsJournalDao journalDao;
    private final PointsInterestsDao interestsDao;
    private final UserClient userClient;
    private final IPointsLevelUpgradeService levelUpgradeService;


    @Override
    @Transactional
    public void add(PointsLevelTO params) {
        params.setId(null);
        this.checkParams(params);
        PointsLevel level = BeanUtil.copyProperties(params, PointsLevel.class);
        level.setEnable(1);
        level.create();
        level = levelDao.insert(level);
        this.addRule(level.getId(), params.getRuleList());
    }

    @Override
    public Pagination<PointsLevelVO> page(PointsLevelPageTO params) {
        Pagination<PointsLevelVO> page = levelDao.page(params);
        if (CollUtil.isEmpty(page)) {
            return new Pagination<>(new ArrayList<>(), page.getPageable(),page.getTotalElements());
        }
        List<String> operatorUidList = page.stream().map(PointsLevelVO::getOperator).distinct().collect(Collectors.toList());
        Map<String, String> operatorMap = new HashMap<>();
        UnifyResponse<List<UserInfoForServer>> usersByIds = userClient.list(operatorUidList);
        if (usersByIds.hasData()) {
            operatorMap = usersByIds.getData().stream().collect(Collectors.toMap(UserInfoForServer::getUid, UserInfoForServer::getDisplayName));
        }
        for (PointsLevelVO pointsLevelVO : page) {
            CountryRich countryRich = CountryCode.getByGeoId(pointsLevelVO.getCountryId());
            pointsLevelVO.setCountryName(countryRich != null ? CountryCode.getByAlpha2Code(countryRich.getCountryCode()).getNameZh() : String.valueOf(pointsLevelVO.getCountryId()));
            pointsLevelVO.setUserNum(balanceDao.getUserNumByCountry(pointsLevelVO.getCountryId()));
            pointsLevelVO.setOperator(operatorMap.get(pointsLevelVO.getOperator()));
        }
        return page;
    }

    @Override
    public PointsLevelDetailVO detail(String id) {
        PointsLevel level = levelDao.byId(id);
        PointsLevelDetailVO detailVO = BeanUtil.copyProperties(level, PointsLevelDetailVO.class);
        CountryRich countryRich = CountryCode.getByGeoId(detailVO.getCountryId());
        detailVO.setCountryName(countryRich != null ? CountryCode.getByAlpha2Code(countryRich.getCountryCode()).getNameZh() : String.valueOf(detailVO.getCountryId()));

        /* 将List中的权益id json转为权益idList */
        List<PointsLevelRule> ruleList = levelRuleDao.byLevelId(id);
        List<PointsLevelDetailVO.RuleVO> ruleVOList = ruleList.stream().map(rule -> {
            PointsLevelDetailVO.RuleVO ruleVO = new PointsLevelDetailVO.RuleVO();
            ruleVO.setPointsLevelId(rule.getPointsLevelId());
            ruleVO.setIndex(rule.getIndex());
            ruleVO.setLogoUrl(rule.getLogoUrl());
            ruleVO.setName(rule.getName());
            ruleVO.setConsumptionThreshold(rule.getConsumptionThreshold().stripTrailingZeros().toPlainString());
            ruleVO.setGiftPoints(rule.getGiftPoints());
            ruleVO.setInterestsIdList(JSONUtil.toList(rule.getInterestsIdList(), String.class));
            ruleVO.setUserNum(this.getLevelUserNum(detailVO.getCountryId(), rule, ruleList));
            return ruleVO;
        }).collect(Collectors.toList());
        detailVO.setRuleList(ruleVOList);
        return detailVO;
    }

    @Override
    @Transactional
    public void edit(PointsLevelTO params) {
        this.checkParams(params);
        PointsLevel level = levelDao.byId(params.getId());
        if (null == level) {
            return;
        }
        BeanUtil.copyProperties(params, level);
        level.modify();
        levelDao.updateById(level);
        levelRuleDao.deleteByLevelId(params.getId());
        this.addRule(params.getId(), params.getRuleList());
    }

    @Override
    @Transactional
    public void delete(String id) {
        PointsLevel level = levelDao.byId(id);
        if (null == level) {
            return;
        }
        level.setEnable(0);
        level.modify();
        levelDao.updateById(level);
        levelRuleDao.deleteByLevelId(id);
    }

    @Override
    public PointsLevelUserDetailVO userDetail(Long countryId) {
        String uid = ContextUtils.get().getUID();
        log.debug("uid:【{}】", uid);
        try {
            levelUpgradeService.levelUpgrade(uid, countryId);
        } catch (Exception e) {
            log.error("等级升级错误", e);
        }

        PointsLevelUserDetailVO detailVO = new PointsLevelUserDetailVO();
        CountryRich countryRich = CountryCode.getByGeoId(countryId);
        detailVO.setCountryName(countryRich != null ? CountryCode.getByAlpha2Code(countryRich.getCountryCode()).getNameZh() : String.valueOf(detailVO.getCountryId()));
        detailVO.setAmount(balanceDao.getAmountByUidAndCountryId(uid, countryId, new PointsBalanceUservo()));
        detailVO.setLevelIndex(0);
        PointsBalance byUidAndCountry = balanceDao.findByUidAndCountry(uid, countryId);
        if (null != byUidAndCountry) {
            detailVO.setBalance(byUidAndCountry.getBalance());
        }

        PointsLevel level = levelDao.byCountryId(countryId);
        if (StrUtil.isBlank(uid) || ObjectUtil.isEmpty(level)) {
            return detailVO;
        }
        BeanUtil.copyProperties(level, detailVO);
        detailVO.setUpgradeLevel(levelUpgradeService.getLevelUpgradeIndex(uid, countryId, level));

        /* 将List中的权益id json转为权益idList */
        List<PointsLevelRule> ruleList = levelRuleDao.byLevelId(level.getId());
        List<PointsLevelUserDetailVO.LevelVO> ruleVOList = ruleList.stream().map(rule -> {
            PointsLevelUserDetailVO.LevelVO ruleVO = new PointsLevelUserDetailVO.LevelVO();
            ruleVO.setIndex(rule.getIndex());
            ruleVO.setLogoUrl(rule.getLogoUrl());
            ruleVO.setName(rule.getName());
            ruleVO.setConsumptionThreshold(rule.getConsumptionThreshold().stripTrailingZeros().toPlainString());
            ruleVO.setGiftPoints(rule.getGiftPoints());
            int flag = new BigDecimal(detailVO.getAmount()).compareTo(new BigDecimal(ruleVO.getConsumptionThreshold()));
            // 已获得
            if (flag >= 0) {
                ruleVO.setStatus(2);
                detailVO.setLevelIndex(rule.getIndex());
            }
            // 到达
            else if (detailVO.getLevelIndex() != null && detailVO.getLevelIndex().equals(rule.getIndex() - 1)) {
                ruleVO.setStatus(1);
            }
            // 未到达
            else {
                ruleVO.setStatus(0);
            }
            List<String> interestsIdList = JSONUtil.toList(rule.getInterestsIdList(), String.class);
            if (CollectionUtil.isNotEmpty(interestsIdList)) {
                ruleVO.setInterestsList(interestsDao.voByIdListAndCountry(interestsIdList, countryId));
            }
            return ruleVO;
        }).collect(Collectors.toList());
        detailVO.setRuleList(ruleVOList);

        return detailVO;
    }

    @Override
    public PointsLevelUserGeneralVO userGeneral(Long countryId) {
        String uid = ContextUtils.get().getUID();
        // 查询当前用户所处等级体系
        PointsLevel level = levelDao.byCountryId(countryId);
        if (StrUtil.isBlank(uid) || ObjectUtil.isEmpty(level)) {
            return null;
        }
        PointsLevelUserGeneralVO userGeneralVO = new PointsLevelUserGeneralVO();
        PointsBalance byUidAndCountry = balanceDao.findByUidAndCountry(uid, countryId);
        if (null != byUidAndCountry) {
            userGeneralVO.setBalance(byUidAndCountry.getBalance());
        }
        // 当前用户所消费的金额
        String amountString = balanceDao.getAmountByUidAndCountryId(uid, countryId, new PointsBalanceUservo());
        BigDecimal amount = new BigDecimal(amountString);
        userGeneralVO.setUpgradeProgress(0);
        List<PointsLevelRule> ruleList = levelRuleDao.byLevelId(level.getId());
        for (int i = ruleList.size() - 1; i >= 0; i--) {
            PointsLevelRule levelRule = ruleList.get(i);
            // 先比较是否达到当前等级要求
            int flag = amount.compareTo(levelRule.getConsumptionThreshold());
            // 达到
            if (flag >= 0) {
                userGeneralVO.setLevelName(levelRule.getName());
                userGeneralVO.setLogoUrl(levelRule.getLogoUrl());
                // 升级进度计算,最后一级时进度条满
                if (i == ruleList.size() - 1) {
                    userGeneralVO.setUpgradeProgress(100);
                }
                // 否则，使用 当前消费金额 / 下一级门槛
                else {
                    BigDecimal upgradeProgress = amount.divide(ruleList.get(i + 1).getConsumptionThreshold(), new MathContext(2))
                            .multiply(new BigDecimal("100"));
                    userGeneralVO.setUpgradeProgress(upgradeProgress.intValue());
                }
                break;
            }
        }
        return userGeneralVO;
    }

    private void addRule(String levelId, List<PointsLevelTO.RuleTO> ruleTOList) {
        List<PointsLevelRule> ruleList = new ArrayList<>();
        int index = 1;
        for (PointsLevelTO.RuleTO ruleTO : ruleTOList) {
            PointsLevelRule rule = BeanUtil.copyProperties(ruleTO, PointsLevelRule.class);
            rule.setConsumptionThreshold(new BigDecimal(ruleTO.getConsumptionThreshold()));
            rule.setPointsLevelId(levelId);
            rule.setIndex(index++);
            rule.setInterestsIdList(JSONUtil.toJsonStr(ruleTO.getInterestsIdList()));
            rule.setEnable(1);
            rule.create();
            ruleList.add(rule);
        }
        levelRuleDao.insert(ruleList);
    }

    private void checkParams(PointsLevelTO params) {
        if (levelDao.checkCountry(params)) {
            throw new ApplicationRuntimeException(BucksCenterError.POINTS_LEVEL_COUNTRY_DUPLICATE);
        }
        if (CollectionUtil.isEmpty(params.getRuleList())) {
            throw new ApplicationRuntimeException(BucksCenterError.POINTS_LEVEL_IS_EMPTY);
        }
        for (int i = 1; i < params.getRuleList().size(); i++) {
            BigDecimal threshold1 = new BigDecimal(params.getRuleList().get(i - 1).getConsumptionThreshold());
            BigDecimal threshold2 = new BigDecimal(params.getRuleList().get(i).getConsumptionThreshold());
            if (-1 != threshold1.compareTo(threshold2)) {
                throw new ApplicationRuntimeException(BucksCenterError.CONSUMPTION_THRESHOLD_MUST_INCREASED);
            }
        }
    }

    /**
     * 获取当前等级下的用户数量
     */
    private Long getLevelUserNum(Long countryId, PointsLevelRule rule, List<PointsLevelRule> ruleList) {
        /* 查找当前等级的下一级的消费门槛 */
        BigDecimal rangeEnd = null;
        for (int i = 0; i < ruleList.size(); i++) {
            if (rule.getIndex().equals(ruleList.get(i).getIndex()) && i < ruleList.size() - 1) {
                rangeEnd = ruleList.get(i + 1).getConsumptionThreshold();
                break;
            }
        }
        return journalDao.getTotalAmountInRangeNum(countryId, rule.getConsumptionThreshold(), rangeEnd);
    }
}
