package net.poweroak.saas.bucksc.model.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.sqltag.TableField;
import net.poweroak.saas.bucksc.model.PO.PointsLevel;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2024/5/6
 */
@Data
public class PointsLevelVO {
    @TableField(table = PointsLevel.class, field = "id")
    private String id;

    @ApiModelProperty(value = "国家ID")
    @TableField(table = PointsLevel.class, field = "countryId")
    private Long countryId;

    @ApiModelProperty(value = "当前国家的积分用户数量")
    private Long userNum;

    @ApiModelProperty(value = "国家名称")
    private String countryName;

    @ApiModelProperty(value = "币种")
    @TableField(table = PointsLevel.class, field = "currency")
    private String currency;

    @ApiModelProperty(value = "货币单位")
    @TableField(table = PointsLevel.class, field = "currencyUnit")
    private String currencyUnit;

    @ApiModelProperty(value = "等级说明")
    @TableField(table = PointsLevel.class, field = "instructions")
    private String instructions;

    @ApiModelProperty(value = "等级说明的html形式")
    @TableField(table = PointsLevel.class, field = "instructionsHtml")
    private String instructionsHtml;

    @ApiModelProperty(value = "操作人")
    @TableField(table = PointsLevel.class, field = "modBy")
    private String operator;
}
