package net.poweroak.saas.bucksc.controller.web;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.saas.bucksc.client.PointsEarnClient;
import net.poweroak.saas.bucksc.common.ActivityEarnCode;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.dao.PointsEarnRuleDao;
import net.poweroak.saas.bucksc.common.GenSubType;
import net.poweroak.saas.bucksc.model.TO.PointsEranCommTO;
import net.poweroak.saas.bucksc.request.*;
import net.poweroak.saas.bucksc.response.PointsEarnResponse;
import net.poweroak.saas.bucksc.response.PointsEarnSignResponse;
import net.poweroak.saas.bucksc.service.IPointsEarnService;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/11 14:09
 */
@RestController
@Api(tags = "积分发放api")
@AllArgsConstructor
@RequestMapping("/" + RouteEndpoint.API + "/pointsEarn")
@Slf4j
public class PointsEarnController implements PointsEarnClient {

    private final IPointsEarnService earnPointsService;
    private final PointsEarnRuleDao pointsEarnRuleDao;

    @Override
    public UnifyResponse<PointsEarnSignResponse> sign(PointsEarnSignRequest request) {
        if (Strings.isEmpty(request.getActivityCode())) {
            request.setActivityCode(ActivityEarnCode.SIGN.getCode());
        }
        log.info("签到请求参数：{}", JSONUtil.toJsonStr(request));
        PointsEranCommTO pointsEranCommTO = new PointsEranCommTO();
        BeanUtils.copyProperties(request, pointsEranCommTO);

        earnPointsService.send(pointsEranCommTO, ActivityEarnCode.SIGN, GenSubType.SIGN);

        PointsEarnSignResponse pointsEarnSignResponse = new PointsEarnSignResponse();
//        BeanUtils.copyProperties(pointsEarnResponse, pointsEarnSignResponse);

        //获取签到信息
//        Map<String, String> map = pointsEarnRuleDao.getSignDate(request.getCountryId(), request.getChannelCode(), pointsEranCommTO.getUid(), new Date());
//        pointsEarnSignResponse.setSignDays(map.size());
        pointsEarnSignResponse.setStatus(1);
        log.info("签到请求返回参数：{}", JSONUtil.toJsonStr(pointsEarnSignResponse));
        return new UnifyResponse<>(pointsEarnSignResponse);
    }

    @Override
    public UnifyResponse<PointsEarnResponse> pvPower(PointsEarnPvPowerRequest request) {
        if (Strings.isEmpty(request.getActivityCode())) {
            request.setActivityCode(ActivityEarnCode.PV_POWER.getCode());
        }
        log.info("光伏发电请求参数：{}", JSONUtil.toJsonStr(request));
        PointsEranCommTO pointsEranCommTO = new PointsEranCommTO();
        BeanUtils.copyProperties(request, pointsEranCommTO);
        PointsEarnResponse pointsEarnResponse = earnPointsService.send(pointsEranCommTO, ActivityEarnCode.PV_POWER, GenSubType.PV_POWER);
        log.info("光伏发电请求返回参数：{}", JSONUtil.toJsonStr(pointsEarnResponse));
        return new UnifyResponse<>(pointsEarnResponse);
    }

    @Override
    public UnifyResponse<PointsEarnResponse> exchange(@Valid PointsEarnExchangeRequest request) {
        if (Strings.isEmpty(request.getActivityCode())) {
            request.setActivityCode(ActivityEarnCode.CURRENCY_EXCHANGE.getCode());
        }
        log.info("下单请求参数：{}", JSONUtil.toJsonStr(request));
        PointsEranCommTO pointsEranCommTO = new PointsEranCommTO();
        BeanUtils.copyProperties(request, pointsEranCommTO);
        PointsEarnResponse pointsEarnResponse = earnPointsService.send(pointsEranCommTO, ActivityEarnCode.CURRENCY_EXCHANGE, GenSubType.ORDER);
        log.info("下单请求返回参数：{}", JSONUtil.toJsonStr(pointsEarnResponse));
        return new UnifyResponse<>(pointsEarnResponse);
    }

    @Override
    public UnifyResponse<PointsEarnResponse> preExchange(PointsEarnExchangeRequest request) {
        if (Strings.isEmpty(request.getActivityCode())) {
            request.setActivityCode(ActivityEarnCode.CURRENCY_EXCHANGE.getCode());
        }
        log.info("预下单请求参数：{}", JSONUtil.toJsonStr(request));
        PointsEranCommTO pointsEranCommTO = new PointsEranCommTO();
        BeanUtils.copyProperties(request, pointsEranCommTO);
        pointsEranCommTO.setSend(false);
        PointsEarnResponse pointsEarnResponse = earnPointsService.send(pointsEranCommTO, ActivityEarnCode.CURRENCY_EXCHANGE, GenSubType.ORDER);
        log.info("预下单请求返回参数：{}", JSONUtil.toJsonStr(pointsEarnResponse));
        return new UnifyResponse<>(pointsEarnResponse);
    }

    @Override
    public UnifyResponse<PointsEarnResponse> lottery(PointsEarnCommRequest request) {
        log.info("大转盘请求参数：{}", JSONUtil.toJsonStr(request));
        PointsEranCommTO pointsEranCommTO = new PointsEranCommTO();
        BeanUtils.copyProperties(request, pointsEranCommTO);
        PointsEarnResponse pointsEarnResponse = earnPointsService.send(pointsEranCommTO, ActivityEarnCode.NW, GenSubType.LOTTERY);
        log.info("大转盘请求返回参数：{}", JSONUtil.toJsonStr(pointsEarnResponse));
        return new UnifyResponse<>(pointsEarnResponse);
    }

    @Override
    public UnifyResponse<PointsEarnResponse> postLife(PointsEarnCommRequest request) {
        log.info("发帖请求参数：{}", JSONUtil.toJsonStr(request));
        if (Strings.isEmpty(request.getActivityCode())) {
            request.setActivityCode(ActivityEarnCode.POST_LIFE.getCode());
        }
        PointsEranCommTO pointsEranCommTO = new PointsEranCommTO();
        BeanUtils.copyProperties(request, pointsEranCommTO);
        PointsEarnResponse pointsEarnResponse = earnPointsService.send(pointsEranCommTO, ActivityEarnCode.POST_LIFE, GenSubType.POST_LIFE);
        log.info("发帖返回参数：{}", JSONUtil.toJsonStr(pointsEarnResponse));
        return new UnifyResponse<>(pointsEarnResponse);
    }


    @Override
    public UnifyResponse<PointsEarnResponse> common(@Valid PointsEarnCommRequest request) {
        String activityEarnCode = request.getActivityCode();
        Integer genSubTypeCode = request.getGenSubTypeCode();
        if (StrUtil.isBlank(activityEarnCode)) {
            activityEarnCode = ActivityEarnCode.COMMON.getCode();
        }
        if (genSubTypeCode == null) {
            genSubTypeCode = GenSubType.OTHER.getCode();
        }
        ActivityEarnCode activityEarnCodeEnum = ActivityEarnCode.getByCode(activityEarnCode);
        GenSubType genSubType = GenSubType.match(genSubTypeCode);

        log.info(activityEarnCodeEnum.getName() + "请求参数：{}", JSONUtil.toJsonStr(request));
        PointsEranCommTO pointsEranCommTO = new PointsEranCommTO();
        BeanUtils.copyProperties(request, pointsEranCommTO);

        PointsEarnResponse pointsEarnResponse = earnPointsService.send(pointsEranCommTO, activityEarnCodeEnum, genSubType);
        log.info(activityEarnCodeEnum.getName() + "请求返回参数：{}", JSONUtil.toJsonStr(pointsEarnResponse));
        return new UnifyResponse<>(pointsEarnResponse);
    }

}
