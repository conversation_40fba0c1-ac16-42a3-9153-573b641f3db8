package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.page.PageInfo;

/**
 * <AUTHOR>
 * @date 2022/5/7 9:15
 */
@Data
public class RequestLogSearchTO extends PageInfo {
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "请求参数")
    private String requestParams;
    @ApiModelProperty(value = "错误信息")
    private String errMsg;
    @ApiModelProperty(value = "类型")
    private Integer type;

}
