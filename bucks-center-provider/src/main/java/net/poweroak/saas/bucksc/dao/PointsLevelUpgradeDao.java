package net.poweroak.saas.bucksc.dao;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.data.mybatis.GenericRepository;
import net.poweroak.framework.data.mybatis.query.Criterion;
import net.poweroak.framework.data.mybatis.query.MybatisQuery;
import net.poweroak.framework.data.mybatis.query.join.JoinType;
import net.poweroak.framework.enums.OrderBy;
import net.poweroak.saas.bucksc.mapper.PointsLevelUpgradeMapper;
import net.poweroak.saas.bucksc.model.PO.PointsLevelRule;
import net.poweroak.saas.bucksc.model.PO.PointsLevelUpgrade;
import net.poweroak.saas.bucksc.model.VO.PointsLevelUpgradeVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2024/6/7
 */
@Slf4j
@Repository
public class PointsLevelUpgradeDao extends GenericRepository<PointsLevelUpgrade, PointsLevelUpgradeMapper, String> {
    public PointsLevelUpgradeDao(PointsLevelUpgradeMapper genericMapper) {
        super(genericMapper);
    }

    public List<PointsLevelUpgrade> byUidAndCountry(String uid, Long countryId) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsLevelUpgrade::getEnable).is(1))
                .addCriterion(Criterion.where(PointsLevelUpgrade::getUid).is(uid))
                .addCriterion(Criterion.where(PointsLevelUpgrade::getCountryId).is(countryId));
        return this.find(query);
    }

    /**
     * 通过uid和国家id查询当前用户升级到的等级
     */
    public Integer indexByUidAndCountry(String uid, Long countryId, String pointsLevelId) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsLevelUpgrade::getEnable).is(1))
                .addCriterion(Criterion.where(PointsLevelUpgrade::getHasInform).is(0))
                .addCriterion(Criterion.where(PointsLevelUpgrade::getUid).is(uid))
                .addCriterion(Criterion.where(PointsLevelUpgrade::getCountryId).is(countryId))
                .addCriterion(Criterion.where(PointsLevelUpgrade::getPointsLevelId).is(pointsLevelId))
                .orderBy(PointsLevelRule::getIndex, OrderBy.DESC)
                .join(PointsLevelUpgradeVO.class)
                .joinTable(JoinType.INNER, PointsLevelRule.class)
                .on(PointsLevelUpgrade::getLevelRuleId, PointsLevelRule::getId).complete()
                .build();
        List<PointsLevelUpgradeVO> list = this.findByJoin(query);
        if (CollectionUtil.isNotEmpty(list)) {
            return list.get(0).getIndex();
        }
        return null;
    }

    /**
     * 将指定用户的等级升级告知标志位更新为已告知
     */
    public void updateHasInform(String uid, Long countryId) {
        MybatisQuery query = new MybatisQuery();
        query.addCriterion(Criterion.where(PointsLevelUpgrade::getEnable).is(1))
                .addCriterion(Criterion.where(PointsLevelUpgrade::getUid).is(uid))
                .addCriterion(Criterion.where(PointsLevelUpgrade::getCountryId).is(countryId))
                .updateSet(PointsLevelUpgrade::getHasInform, 1);
        this.update(query);
    }
}
