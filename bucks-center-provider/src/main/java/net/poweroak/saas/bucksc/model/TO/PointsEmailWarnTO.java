package net.poweroak.saas.bucksc.model.TO;
import lombok.Data;

import javax.validation.constraints.NotBlank;


/**
 * Created by zx on 2022/7/6 08:27
 */
@Data
public class PointsEmailWarnTO  {

    private String id;

    @NotBlank(message = "邮件不能为空")
    private String email;

    @NotBlank(message = "姓名不能为空")
    private String name;

    @NotBlank(message = "部门不能为空")
    private String department;


}
