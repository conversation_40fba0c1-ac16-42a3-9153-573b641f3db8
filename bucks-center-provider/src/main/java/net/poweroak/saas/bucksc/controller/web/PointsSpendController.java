package net.poweroak.saas.bucksc.controller.web;

import io.swagger.annotations.Api;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.saas.bucksc.client.PointsSpendClient;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.request.LotteryRuleRequest;
import net.poweroak.saas.bucksc.request.PointsSpendRequest;
import net.poweroak.saas.bucksc.response.LotteryRuleResponse;
import net.poweroak.saas.bucksc.response.PointsSpendResponse;
import net.poweroak.saas.bucksc.service.IPointsChange;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Created by zx on 2022/5/10 15:46
 */

@RestController
@Api(tags = "积分消耗、回退、规则")
@RequestMapping("/" + RouteEndpoint.API + "/pointsSpendRule")
public class PointsSpendController implements PointsSpendClient {

    @Resource
    private IPointsChange pointsChange;

    @Override
    public UnifyResponse<PointsSpendResponse> lottery(PointsSpendRequest request) {
        PointsSpendResponse response = pointsChange.lottery(request);
        return new UnifyResponse(response);
    }

    @Override
    @Transactional
    public UnifyResponse<PointsSpendResponse> couponExchange(PointsSpendRequest request) {
        PointsSpendResponse response = pointsChange.couponExchange(request);
        return new UnifyResponse(response);
    }

    @Override
    public UnifyResponse<PointsSpendResponse> goodsExchange(PointsSpendRequest request) {
        PointsSpendResponse response = pointsChange.goodsExchange(request);
        return new UnifyResponse(response);
    }

    @Override
    public UnifyResponse<PointsSpendResponse> backGoodsExchange(PointsSpendRequest request) {
        PointsSpendResponse response = pointsChange.backGoodsExchange(request);
        return new UnifyResponse(response);
    }

    @Override
    public UnifyResponse<PointsSpendResponse> pointsBack(PointsSpendRequest request) {
        PointsSpendResponse response = pointsChange.pointsBack(request);
        return new UnifyResponse(response);
    }

    @Override
    public UnifyResponse<LotteryRuleResponse> lotteryRule(LotteryRuleRequest request) {
        LotteryRuleResponse response = pointsChange.lotteryRule(request);
        return new UnifyResponse<>(response);
    }


}
