package net.poweroak.saas.bucksc.model.TO;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.page.PageInfo;
import net.poweroak.saas.bucksc.model.PO.DocumentsInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/7/25 14:19
 * @description
 */
@Data
public class DocumentsInfoTO extends PageInfo {


    @ApiModelProperty(value="活动编码")
    private String activityId;


    @ApiModelProperty(value="渠道code")
    private String channelCode;

    @ApiModelProperty(value="国家code")
    private Long countryId;

    @ApiModelProperty(value="文案类型id")
    private String type;

    @ApiModelProperty(value="语种")
    private String localeCode;



}
