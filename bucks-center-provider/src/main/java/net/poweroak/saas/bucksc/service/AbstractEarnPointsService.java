package net.poweroak.saas.bucksc.service;

import net.poweroak.saas.bucksc.dao.PointsEarnRuleDao;
import net.poweroak.saas.bucksc.dao.PointsJournalDao;
import net.poweroak.saas.bucksc.model.TO.PointsEranCommTO;
import net.poweroak.saas.bucksc.model.VO.PointsEarnCommonVO;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2022/7/4 14:17
 */

public abstract class AbstractEarnPointsService implements InitializingBean {

    @Autowired
    protected PointsEarnRuleDao pointsEarnRuleDao;

    @Autowired
    protected PointsJournalDao pointsJournalDao;

    /**
     * 计算积分
     *
     * @param pointsEranCommTO
     * @return
     */
    public abstract PointsEarnCommonVO calcPoints(PointsEranCommTO pointsEranCommTO);

}
