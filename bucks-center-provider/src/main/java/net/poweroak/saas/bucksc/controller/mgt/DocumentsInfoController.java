package net.poweroak.saas.bucksc.controller.mgt;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.framework.api.data.page.Pagination;
import net.poweroak.framework.api.data.perms.norm.RouteMenu;
import net.poweroak.framework.pedestal.annotate.RequirePermission;
import net.poweroak.framework.pedestal.annotate.RouteAction;
import net.poweroak.saas.bucksc.constants.RouteEndpoint;
import net.poweroak.saas.bucksc.dao.DocumentsInfoDao;
import net.poweroak.saas.bucksc.dao.DocumentsTypeDao;
import net.poweroak.saas.bucksc.model.TO.DocumentsInfoTO;
import net.poweroak.saas.bucksc.model.TO.DocumentsInfoUpdateTO;
import net.poweroak.saas.bucksc.model.VO.DocumentsInfoVO;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/7/25 14:26
 * @description
 */
@Slf4j
@ApiIgnore
@RequestMapping("/" + RouteEndpoint.MGT + "/DocumentsInfo")
@Api(tags = "文案信息")
@RestController
@RouteMenu(module = "DocumentsInfoModule", label = "文案信息", parent = DocumentsController.class)
public class DocumentsInfoController {

    @Resource
    private DocumentsTypeDao documentsTypeDao;

    @Resource
    private DocumentsInfoDao documentsInfoDao;



    /**
     * 分页查询
     *
     * @param request
     * @return
     */
    @PostMapping("/page")
    @ApiOperation(value = "文案信息分页列表")
    @RouteAction(id = "182340c37f3f79115229d154ac7", action = "page", label = "文案信息管理", view = "/BluBucksC/documentsInfo/list.vue", linkToModule = true)
    @RequirePermission
    public UnifyResponse<Pagination<DocumentsInfoVO>> pageList(@RequestBody DocumentsInfoTO request) {
        return this.documentsInfoDao.pageList(request);
    }

    /**
     * 新增
     *
     * @param request
     * @return
     */

    @PostMapping("/add")
    @ApiOperation(value = "新增文案信息")
    @RouteAction(id = "182386eff04f79115229d154ad5", action = "新增", label = "新增")
    @RequirePermission
    public UnifyResponse<Boolean> add(@RequestBody DocumentsInfoUpdateTO request) throws Exception{
        return this.documentsInfoDao.add(request);
    }


    /**
     * 新增
     *
     * @param request
     * @return
     */

    @PostMapping("/edit")
    @ApiOperation(value = "编辑文案信息")
    @RouteAction(id = "182425355dff79115229d154aee", action = "编辑", label = "编辑")
    @RequirePermission
    public UnifyResponse<Boolean> edit(@RequestBody DocumentsInfoUpdateTO request) throws Exception{
        return this.documentsInfoDao.edit(request);
    }


    /**
     * 删除文案类型
     * @param id
     * @return
     */
    @GetMapping("/delete/{id}")
    @ApiOperation(value = "删除文案信息")
    @RouteAction(id = "1823873b2ccf79115229d154ad7", action = "delete", label = "删除")
    @RequirePermission
    public UnifyResponse<Boolean> delete(@PathVariable("id") String id){
        return this.documentsInfoDao.deleteDocumentsInfo(id);
    }
}
