package net.poweroak.saas.bucksc.controller.base;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.java.Log;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.framework.api.UnifyResponse;
import net.poweroak.saas.bucksc.enums.WeekCode;
import net.poweroak.saas.bucksc.model.VO.RegionCountryVO;
import net.poweroak.saas.bucksc.service.IBaseService;
import net.poweroak.saas.bucksc.utils.LocalDateUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * Created by zx on 2022/5/10 14:09
 */
@RestController
@RequestMapping("/base/info")
@Api(tags = "基础公共")
@Slf4j
public class BaseController {

    @Resource
    private IBaseService baseService;

    @GetMapping("/queryAllCountry")
    @ApiOperation(value = "查询所有国家的信息")
    public UnifyResponse<List<RegionCountryVO>> queryAllCountry() {
        return new UnifyResponse<>(baseService.queryAllCountry());
    }
}
