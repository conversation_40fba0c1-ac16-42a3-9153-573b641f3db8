package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.poweroak.framework.api.data.page.PageInfo;

/**
 * Created by zx on 2022/5/9 16:55
 */
@Data
public class SpendRulePageTO extends PageInfo {

    @ApiModelProperty(value = "渠道标识")
    private String channelCode;

    @ApiModelProperty(value = "活动ID")
    private String activityId;

    @ApiModelProperty(value = "1-券卡兑换；2-物品兑换；3-货币抵扣；4-抽奖；9-通用")
    private Integer ruleType;

    @ApiModelProperty(value = "国家编码")
    private Long countryId;

}
