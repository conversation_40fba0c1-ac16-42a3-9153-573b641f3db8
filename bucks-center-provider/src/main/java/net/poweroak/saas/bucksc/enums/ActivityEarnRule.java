package net.poweroak.saas.bucksc.enums;

/**
 * @Copyright poweroak
 * <AUTHOR>
 * @Email : chenhc@@poweroak.com
 * @Date ：Created in  2022/5/9 9:02
 * @description 适用的积分发放规则
 */


public enum ActivityEarnRule {
    signIn(1,"签到"),
    currencyExchange(2,"货币兑换"),
    commonUse(9,"通用");



    private int code;
    private String name;

    ActivityEarnRule()
    {

    }

    ActivityEarnRule(int code, String name)
    {

        this.code = code;
        this.name = name;
    }



    public int getCode()
    {

        return code;
    }

    public String getName()
    {

        return name;
    }
}
