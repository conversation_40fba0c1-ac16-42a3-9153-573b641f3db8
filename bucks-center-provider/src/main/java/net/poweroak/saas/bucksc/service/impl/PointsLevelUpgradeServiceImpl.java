package net.poweroak.saas.bucksc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.poweroak.saas.bucksc.BucksCenterApp;
import net.poweroak.saas.bucksc.common.ActivityEarnCode;
import net.poweroak.saas.bucksc.common.GenSubType;
import net.poweroak.saas.bucksc.dao.*;
import net.poweroak.saas.bucksc.model.PO.Activity;
import net.poweroak.saas.bucksc.model.PO.PointsLevel;
import net.poweroak.saas.bucksc.model.PO.PointsLevelRule;
import net.poweroak.saas.bucksc.model.PO.PointsLevelUpgrade;
import net.poweroak.saas.bucksc.model.TO.PointsEranCommTO;
import net.poweroak.saas.bucksc.model.VO.PointsBalanceUservo;
import net.poweroak.saas.bucksc.service.IPointsEarnService;
import net.poweroak.saas.bucksc.service.IPointsLevelUpgradeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @mailto <EMAIL>
 * @date 2024/6/11
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PointsLevelUpgradeServiceImpl implements IPointsLevelUpgradeService {
    private final PointsLevelDao levelDao;
    private final PointsLevelRuleDao levelRuleDao;
    private final PointsBalanceDao balanceDao;
    private final PointsLevelUpgradeDao levelUpgradeDao;
    private final IPointsEarnService pointsEarnService;
    private final ActivityDao activityDao;

    /**
     * 检查当前用户消费金额是否满足升级要求
     */
    @Override
    @Transactional
    public void levelUpgrade(String uid, Long countryId) {
        // 1.先查出金额
        BigDecimal amount = new BigDecimal(balanceDao.getAmountByUidAndCountryId(uid, countryId, new PointsBalanceUservo()));

        // 2.拿金额查已达到的规则
        PointsLevel level = levelDao.byCountryId(countryId);
        if (null == level) {
            return;
        }
        Activity activity = activityDao.byCodeAndCountryAndChannel(ActivityEarnCode.POINTS_LEVEL_GIFT.getCode(), countryId, BucksCenterApp.SERVICE_NAME);
        if (activity == null) {
            log.warn("没有在当前国家id：【{}】找到渠道为：【{}】的积分等级赠送活动，积分发放失败", countryId, BucksCenterApp.SERVICE_NAME);
            return;
        }
        List<PointsLevelRule> ruleList = levelRuleDao.byAmount(amount, level.getId());
        List<String> ruleIdList = ruleList.stream().map(PointsLevelRule::getId).collect(Collectors.toList());
        Map<String, PointsLevelRule> ruleMap = ruleList.stream().collect(Collectors.toMap(PointsLevelRule::getId, i -> i));

        // 3.用uid、国家id一起去升级表里查出已达成的规则id，用金额查出的id去掉已达成的id
        List<PointsLevelUpgrade> pointsLevelUpgradeList = levelUpgradeDao.byUidAndCountry(uid, countryId);
        List<String> ruleUpgradedIdList = pointsLevelUpgradeList.stream().map(PointsLevelUpgrade::getLevelRuleId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(ruleUpgradedIdList)) {
            ruleIdList.removeAll(ruleUpgradedIdList);
        }
        // 获取升级表中的下标集合，即使积分等级被修改也能过滤重复下标
        Set<Integer> indexSet = pointsLevelUpgradeList.stream().map(PointsLevelUpgrade::getLevelRuleIndex).filter(Objects::nonNull).collect(Collectors.toSet());

        // 4.对剩下的差集新增升级记录，并发放积分

        for (String ruleId : ruleIdList) {
            PointsLevelRule rule = ruleMap.get(ruleId);
            if (indexSet.contains(rule.getIndex())) {
                continue;
            }
            log.info("【{}】国家的用户【{}】升级到【{}】，赠送积分【{}】", countryId, uid, rule.getName(), rule.getGiftPoints());
            PointsLevelUpgrade pointsLevelUpgrade = new PointsLevelUpgrade();
            pointsLevelUpgrade.setUid(uid);
            pointsLevelUpgrade.setCountryId(countryId);
            pointsLevelUpgrade.setHasInform(0);
            pointsLevelUpgrade.setPointsLevelId(rule.getPointsLevelId());
            pointsLevelUpgrade.setLevelRuleId(ruleId);
            pointsLevelUpgrade.setGiftPoints(rule.getGiftPoints());
            pointsLevelUpgrade.setLevelRuleIndex(rule.getIndex());
            pointsLevelUpgrade.create();
            levelUpgradeDao.insert(pointsLevelUpgrade);

            /* 发放积分 */
            if (rule.getGiftPoints() > 0) {
                PointsEranCommTO pointsEranCommTO = new PointsEranCommTO();
                pointsEranCommTO.setUid(uid);
                pointsEranCommTO.setChannelCode(BucksCenterApp.SERVICE_NAME);
                pointsEranCommTO.setActivityCode(ActivityEarnCode.POINTS_LEVEL_GIFT.getCode());
                pointsEranCommTO.setActivityId(activity.getId());
                pointsEranCommTO.setCountryId(countryId);
                pointsEranCommTO.setPoints(rule.getGiftPoints());
                pointsEranCommTO.setDesc("积分等级，Lv" + rule.getIndex() + "升级赠送");
                pointsEarnService.send(pointsEranCommTO, ActivityEarnCode.POINTS_LEVEL_GIFT, GenSubType.POINTS_LEVEL);
            }
        }
    }

    /**
     * 查询指定用户是否升级了，若已升级，返回升级到的等级下标，若没升级，返回0
     */
    @Override
    public Integer getLevelUpgradeIndex(String uid, Long countryId, PointsLevel level) {
        Integer index = levelUpgradeDao.indexByUidAndCountry(uid, countryId, level.getId());
        if (index == null) {
            index = 0;
        }
        levelUpgradeDao.updateHasInform(uid, countryId);
        return index;
    }
}
