package net.poweroak.saas.bucksc.model.VO;

import lombok.Data;
import net.poweroak.framework.sqltag.TableField;
import net.poweroak.saas.bucksc.model.PO.Activity;
import net.poweroak.saas.bucksc.model.PO.Channel;
import net.poweroak.saas.bucksc.model.PO.PointsBalance;
import net.poweroak.saas.bucksc.model.PO.PointsEarnRule;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/6/11 9:15
 */
@Data
public class PointsEarnRuleConditionVO {
        private Activity activity;
        private Channel channel;
        private PointsBalance pointsBalance;
}
