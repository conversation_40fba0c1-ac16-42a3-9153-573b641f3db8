package net.poweroak.saas.bucksc.service;

import net.poweroak.saas.bucksc.request.PointsChangeRequest;
import net.poweroak.saas.bucksc.request.PointsRecordPageRequest;
import net.poweroak.saas.bucksc.request.TotalPointsRequest;
import net.poweroak.saas.bucksc.response.ActivityPointsRecordResponse;
import net.poweroak.saas.bucksc.response.PointsRecordResponse;

import java.util.List;

/**
 * Created by zx on 2022/8/11 10:32
 */
public interface IPointsAppletsUserService {

    /**
     * 积分变动
     */
    void pointsChange(List<PointsChangeRequest> changeTOS);

    /**
     * 根据类型查询不同类型总积分
     */
    Long totalPointsByType(TotalPointsRequest totalPointsRequest);

    /**
     *   我的积分(活动积分、租赁积分、消费积分)、积分明细分页
     */
    PointsRecordResponse pointsRecord(PointsRecordPageRequest recordPageTO);

    /**
     *  活动积分(打卡积分、互动积分、老带新积分)、积分明细分页
     */
    ActivityPointsRecordResponse activityPointsRecord(PointsRecordPageRequest recordPageTO);
}
