package net.poweroak.saas.bucksc.constants;

import net.poweroak.framework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/5/19 17:15
 */
public interface RedisConstant {
    String REDIS_EARN_LOCK = StringUtils.joinKeysForRedis("ERAN_LOCK:");

    String BUCKS_DAY_CLEAN = StringUtils.joinKeysForRedis("DAY_CLEAN");

    String ERAN_TIMES = StringUtils.joinKeysForRedis("ERAN_TIMES:");

    String ERAN_EXTRA = StringUtils.joinKeysForRedis("ERAN_EXTRA:");

    String ERAN_SIGN = StringUtils.joinKeysForRedis("ERAN_SIGN:");

    String SHOPIFY_POINTS_EXECUTE = StringUtils.joinKeysForRedis("SHOPIFY_POINTS_EXECUTE:");

    String BUCKS_SYNC = StringUtils.joinKeysForRedis("BUCKS_SYNC:");
}
