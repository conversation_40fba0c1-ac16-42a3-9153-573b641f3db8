package net.poweroak.saas.bucksc.service;

import net.poweroak.saas.bucksc.request.DeductRatioRequest;
import net.poweroak.saas.bucksc.request.LotteryRuleRequest;
import net.poweroak.saas.bucksc.request.PointsSpendRequest;
import net.poweroak.saas.bucksc.response.LotteryRuleResponse;
import net.poweroak.saas.bucksc.response.PointsSpendResponse;

/**
 * Created by zx on 2022/5/12 9:51 积分变动
 */
public interface IPointsChange {

    /**
     * 卡券兑换
     */
    PointsSpendResponse couponExchange(PointsSpendRequest request);

    /**
     * 积分回退 (购买商品赠送积分)
     */
    PointsSpendResponse pointsBack(PointsSpendRequest request);

    /**
     * 抽奖
     */
    PointsSpendResponse lottery(PointsSpendRequest request);

    /**
     * 物品兑换
     */
    PointsSpendResponse goodsExchange(PointsSpendRequest request);


    PointsSpendResponse other(PointsSpendRequest request);

    /**
     * 积分抵扣
     */
    PointsSpendResponse deductRatio(DeductRatioRequest request);

    /**
     * 抽奖规则
     */
    LotteryRuleResponse lotteryRule(LotteryRuleRequest request);

    /**
     * 活动校验
     */
    String checkActivity(String channelCode, String activityCode, Long countryId);

    /**
     * 积分回退    物品兑换积分
     */
    PointsSpendResponse backGoodsExchange(PointsSpendRequest request);


    PointsSpendResponse openSpend(PointsSpendRequest request);
}
