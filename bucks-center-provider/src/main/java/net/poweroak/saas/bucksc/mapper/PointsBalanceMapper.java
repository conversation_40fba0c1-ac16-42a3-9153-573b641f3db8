package net.poweroak.saas.bucksc.mapper;

import net.poweroak.framework.data.mybatis.GenericMapper;
import net.poweroak.saas.bucksc.model.PO.PointsBalance;
import net.poweroak.saas.bucksc.model.VO.PointsBalanceTotalVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
 * Created by zx on 2022/5/10 17:01
 */
@Mapper
public interface PointsBalanceMapper extends GenericMapper<PointsBalance, String> {

    /**
     * 修改积分
     *
     * @param points
     * @param earnPoints
     * @param spendPoints
     * @param uid
     * @param countryId
     * @return
     */
    Integer changeBalance(@Param("points") Integer points, @Param("earnPoints") Integer earnPoints, @Param("spendPoints") Integer spendPoints, @Param("uid") String uid, @Param("countryId") Long countryId);


    /**
     * 积分统计查询
     *
     * @param uid
     * @param countryId
     * @return
     */
    PointsBalanceTotalVO queryPointsTotal(@Param("uid") String uid, @Param("countryId") Long countryId);
}
