package net.poweroak.saas.bucksc.model.TO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @date 2022/5/7 9:15
 */
@Data
@ApiModel("积分明细查询接口")
public class PointsBalanceJournalTO implements Serializable {

    @ApiModelProperty(value = "发生时间")
    private String genTime;

    @ApiModelProperty(value = "积分")
    private BigDecimal balance;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "失效时间")
    private String invalidTime;
}
