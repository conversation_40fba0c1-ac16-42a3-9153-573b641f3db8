<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.poweroak.saas.bucksc.mapper.PointsBalanceMapper">

    <update id="changeBalance" >
            update points_balance set balance = balance + #{points},earn = earn + #{earnPoints},spend = spend + #{spendPoints} where uid = #{uid} and countryId = #{countryId}
    </update>

    <select id="queryPointsTotal" resultType="net.poweroak.saas.bucksc.model.VO.PointsBalanceTotalVO">
        select ifnull(earn,0) as earnTotal,ifnull(spend,0) as spendTotal,ifnull(balance,0) as balanceTotal from points_balance where uid = #{uid} and countryId = #{countryId}
    </select>
</mapper>