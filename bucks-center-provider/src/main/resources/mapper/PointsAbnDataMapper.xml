<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.poweroak.saas.bucksc.mapper.PointsAbnDataMapper">

    <select id="list" resultType="net.poweroak.saas.bucksc.model.TO.PointsAbnDataTO">
        SELECT
            DATE_FORMAT(abnTime, '%Y-%m-%d') abnDays,
            count(id) abnCount
        FROM
            points_abn_data
        GROUP BY
            abnDays
        ORDER BY
            abnDays DESC
            LIMIT #{start},#{limit}
    </select>

    <select id="counter" resultType="int">
        SELECT COUNT(a.days) FROM (SELECT
                                          DATE_FORMAT(creTime, '%Y-%m-%d') days
                                   FROM
                                       points_abn_data
                                   GROUP BY
                                       days) a
    </select>
</mapper>