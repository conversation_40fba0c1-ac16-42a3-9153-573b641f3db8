<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.poweroak.saas.bucksc.mapper.PointsJournalMapper">

<!--    <select id="cumulativeList" resultType="net.poweroak.saas.bucksc.model.VO.PointsJournalCumulativeVO">-->
<!--        select ifnull(sum(points),0) as points,-->
<!--               uid as uid,-->
<!--                countryId as countryId-->
<!--        from points_journal-->
<!--        where enable = 1 and genType = 1 and DATE_FORMAT(genTime,'%Y-%m-%d') &lt; DATE_FORMAT(now(),'%Y-%m-%d')-->
<!--        <if test="date != null and date != ''">-->
<!--            and DATE_FORMAT(genTime,'%Y-%m-%d') >= #{date}-->
<!--        </if>-->
<!--        GROUP BY uid,countryId-->

<!--    </select>-->



<!--    <select id="cumulative" resultType="net.poweroak.saas.bucksc.model.VO.PointsJournalCumulativeVO">-->
<!--        select ifnull(sum(points),0) as points,-->
<!--        points_journal.uid as uid,-->
<!--        points_journal.countryId as countryId,-->
<!--        points_balance.balance as balance-->
<!--        from points_journal-->
<!--        left join points_balance on points_balance.uid=points_journal.uid and-->
<!--        points_balance.`countryId`=points_journal.`countryId`-->
<!--        where points_journal.enable = 1 and points_journal.genType = 1 and DATE_FORMAT(points_journal.genTime,'%Y-%m-%d') &lt; DATE_FORMAT(now(),'%Y-%m-%d')-->
<!--        <if test="date != null and date != ''">-->
<!--            and DATE_FORMAT(points_journal.genTime,'%Y-%m-%d') >= #{date}-->
<!--        </if>-->
<!--        <if test="uid != null and uid != ''">-->
<!--            and points_journal.uid = #{uid}-->
<!--        </if>-->
<!--        <if test="countryId != null and countryId != ''">-->
<!--            and points_journal.countryId = #{countryId}-->
<!--        </if>-->
<!--        GROUP BY points_journal.uid,points_balance.balance,points_journal.countryId-->

<!--    </select>-->







    <select id="countEmpty" resultType="net.poweroak.saas.bucksc.model.VO.PointsJournalEmptyListVO">
        select ifnull(sum(points),0) as points,

        points_journal.`uid` as uid,
        points_journal.`countryId` as countryId
        from points_journal
        where points_journal.enable = 1 and points_journal.genType = 1
        <if test="date != null and date != ''">
            and DATE_FORMAT(points_journal.`genTime`,'%Y-%m-%d')  <![CDATA[>=]]> #{date}
        </if>
        GROUP BY points_journal.`uid`, points_journal.`countryId`
    </select>





<!--    <select id="emptyList" resultType="net.poweroak.saas.bucksc.model.VO.PointsJournalEmptyListVO">-->
<!--        select ifnull(sum(points),0) as points,-->

<!--        points_journal.`uid` as uid,-->
<!--        points_journal.`countryId` as countryId,-->
<!--        points_journal.`channelCode` as channelCode,-->
<!--        points_journal.`activityId` as activityId-->
<!--        from points_journal-->
<!--        where points_journal.enable = 1 and points_journal.genType = 1-->
<!--        <if test="date != null and date != ''">-->
<!--            and DATE_FORMAT(points_journal.`genTime`,'%Y-%m-%d') = #{date}-->
<!--        </if>-->
<!--        GROUP BY points_journal.`uid`, points_journal.`countryId`,points_journal.`channelCode`,points_journal.`activityId`-->
<!--    </select>-->



    <select id="countGroup" resultType="net.poweroak.saas.bucksc.model.VO.journalGroupVO">


        select ifnull(sum(points), 0) as totalPoints,
        count(DISTINCT points_journal.uid) as num,
        channel.`name` as channelName,
        activity.`name` as activityName,
        points_journal.countryId
        from points_journal
        LEFT JOIN activity on activity.id = points_journal.activityId
        LEFT JOIN channel on channel.`code` = points_journal.channelCode
        where 1 = 1
        <if test="channelCode != null and channelCode != ''">
            and points_journal.channelCode = #{channelCode}
        </if>

        and genType = 1
        GROUP BY channel.`name`, activity.`name`, points_journal.countryId


    </select>
    <select id="pageByGroup" resultType="net.poweroak.saas.bucksc.model.VO.journalGroupVO">


        select ifnull(sum(points), 0) as totalPoints,
        count(DISTINCT points_journal.uid) as num,
        channel.`name` as channelName,
        activity.`name` as activityName,
        points_journal.countryId
        from points_journal
        LEFT JOIN activity on activity.id = points_journal.activityId
        LEFT JOIN channel on channel.`code` = points_journal.channelCode
        where 1 = 1
        <if test="channelCode != null and channelCode != ''">
            and points_journal.channelCode = #{channelCode}
        </if>

        and genType = 1
        GROUP BY channel.`name`, activity.`name`, points_journal.countryId
        LIMIT #{start},#{limit}

    </select>



    <select id="countByDay" resultType="net.poweroak.saas.bucksc.model.VO.journalDayVO">
        SELECT
        DATE_FORMAT( `genTime`, '%Y-%m-%d' ) AS days,
        channel.`name` AS channelName,
        activity.`name` AS activityName,
        sum(
        IF
        ( genType = 1, points, 0 )) AS type1Points,
        count(
        DISTINCT uid,
        IF
        ( genType = 1, TRUE, NULL )) AS type1Num,
        sum(
        IF
        ( genType = 2, points, 0 )) AS type2Points,
        count(
        DISTINCT uid,
        IF
        ( genType = 2, TRUE, NULL )) AS type2Num,
        sum(
        IF
        ( genType = 3, points, 0 )) AS type3Points,
        count(
        DISTINCT uid,
        IF
        ( genType = 3, TRUE, NULL )) AS type3Num,
        (
        sum(
        IF
        ( genType = 2, points, 0 )))/(
        sum(
        IF
        ( genType = 1, points, 0 ))) AS ratio
        FROM
        points_journal
        LEFT JOIN activity ON activity.id = points_journal.activityId
        LEFT JOIN channel ON channel.`code` = points_journal.channelCode
        WHERE
        1 = 1
        <if test="begin != null and begin != ''">
            and points_journal.genTime &gt; #{begin}
        </if>

        <if test="end != null and end != ''">
            and points_journal.genTime &lt; #{end}
        </if>

        <if test="activityId != null and activityId != ''">
            and activity.id = #{activityId}
        </if>

        <if test="channelCode != null and channelCode != ''">
            and channel.code = #{channelCode}
        </if>

        GROUP BY
        DATE_FORMAT( `genTime`, '%Y-%m-%d' ),
        channel.`name`,
        activity.`name`
        ORDER BY
        DATE_FORMAT(
        `genTime`,
        '%Y-%m-%d'
        ) DESC

    </select>


    <select id="pageByDay" resultType="net.poweroak.saas.bucksc.model.VO.journalDayVO">
        SELECT
        DATE_FORMAT( `genTime`, '%Y-%m-%d' ) AS days,
        channel.`name` AS channelName,
        activity.`name` AS activityName,
        sum(
        IF
        ( genType = 1, points, 0 )) AS type1Points,
        count(
        DISTINCT uid,
        IF
        ( genType = 1, TRUE, NULL )) AS type1Num,
        sum(
        IF
        ( genType = 2, points, 0 )) AS type2Points,
        count(
        DISTINCT uid,
        IF
        ( genType = 2, TRUE, NULL )) AS type2Num,
        sum(
        IF
        ( genType = 3, points, 0 )) AS type3Points,
        count(
        DISTINCT uid,
        IF
        ( genType = 3, TRUE, NULL )) AS type3Num,
        (
        sum(
        IF
        ( genType = 2, points, 0 )))/(
        sum(
        IF
        ( genType = 1, points, 0 ))) AS ratio
        FROM
        points_journal
        LEFT JOIN activity ON activity.id = points_journal.activityId
        LEFT JOIN channel ON channel.`code` = points_journal.channelCode
        WHERE
        1 = 1
        <if test="begin != null and begin != ''">
            and points_journal.genTime &gt; #{begin}
        </if>

        <if test="end != null and begin != ''">
            and points_journal.genTime &lt; #{end}
        </if>

        <if test="activityId != null and activityId != ''">
            and activity.id = #{activityId}
        </if>

        <if test="channelCode != null and channelCode != ''">
            and channel.code = #{channelCode}
        </if>
        GROUP BY
        DATE_FORMAT( `genTime`, '%Y-%m-%d' ),
        channel.`name`,
        activity.`name`
        ORDER BY
        DATE_FORMAT(
        `genTime`,
        '%Y-%m-%d'
        ) DESC
         LIMIT #{start},#{limit}

    </select>

    <select id="sumPoints" resultType="integer">
      select ifnull(sum(points), 0) as totalPoints from points_journal where uid = #{uid} and countryId = #{countryId} and genType = 1
        <if test="date != null and date != ''">
            and genTime <![CDATA[ >= ]]> #{date, jdbcType=DATE}
        </if>
        <if test="channelCode != null and channelCode != ''">
            and channelCode = #{channelCode}
        </if>
    </select>


    <select id="findAccount" resultType="net.poweroak.saas.bucksc.model.VO.PointsAccountVO">
        SELECT DISTINCT
            uid,
            countryId
        FROM
            points_journal
        WHERE
            uid IS NOT NULL
          AND activityId IS NOT NULL
    </select>

    <select id="findAbnDataListByAccount" resultType="net.poweroak.saas.bucksc.model.VO.PointsAbnDataVO">

        SELECT
        A.uid,
        A.activityId,
        A.channelCode,
        A.countryId,
        B.genTime as abnTime,
        B.points,
        B.orderId
        FROM
        (
        SELECT
            h.uid,
            h.activityId,
            h.channelCode,
            h.countryId,
            h.genTime,
            h.points,
            h.orderId,
            (@i := @i + 1) AS ord_num
        FROM
        points_journal h,
        (SELECT @i := 1) b
        WHERE
            h.uid = #{account.uid} and h.countryId = #{account.countryId} and h.genType = #{genType}
        ORDER BY
        h.genTime DESC
        ) AS A
        LEFT JOIN (
        SELECT
            h.uid,
            h.activityId,
            h.channelCode,
            h.countryId,
            h.genTime,
            h.points,
            h.orderId,
            h.id AS journalId,
            (@j := @j + 1) AS ord_num
        FROM
        points_journal h,
        (SELECT @j := 0) c
        WHERE
            h.uid = #{account.uid} and h.countryId = #{account.countryId} and h.genType = #{genType}
        ORDER BY
        h.genTime DESC
        ) AS B ON A.ord_num = B.ord_num
        WHERE
        TIMESTAMPDIFF(SECOND, A.genTime, B.genTime) <![CDATA[ <= ]]> #{second}
         -- AND DATE_FORMAT(b.genTime, '%Y-%m-%d') = DATE_FORMAT(NOW(), '%Y-%m-%d')
          -- 当前时间与上一次扫描间隔时间内
        AND TIMESTAMPDIFF(SECOND, B.genTime, NOW()) <![CDATA[ <= ]]> 60
    </select>


<!--    分日期统计积分导出-->
    <select id="export" resultType="net.poweroak.saas.bucksc.model.VO.JournalDayExportListVO">
        SELECT
        DATE_FORMAT( `genTime`, '%Y-%m-%d' ) AS days,
        channel.`name` AS channelName,
        activity.`name` AS activityName,
        sum(
        IF
        ( genType = 1, points, 0 )) AS type1Points,
        count(
        DISTINCT uid,
        IF
        ( genType = 1, TRUE, NULL )) AS type1Num,
        sum(
        IF
        ( genType = 2, points, 0 )) AS type2Points,
        count(
        DISTINCT uid,
        IF
        ( genType = 2, TRUE, NULL )) AS type2Num,
        sum(
        IF
        ( genType = 3, points, 0 )) AS type3Points,
        count(
        DISTINCT uid,
        IF
        ( genType = 3, TRUE, NULL )) AS type3Num,
        (
        sum(
        IF
        ( genType = 2, points, 0 )))/(
        sum(
        IF
        ( genType = 1, points, 0 ))) AS ratio
        FROM
        points_journal
        LEFT JOIN activity ON activity.id = points_journal.activityId
        LEFT JOIN channel ON channel.`code` = points_journal.channelCode
        WHERE
        1 = 1
        <if test="begin != null and begin != ''">
            and points_journal.genTime &gt; #{begin}
        </if>

        <if test="end != null and begin != ''">
            and points_journal.genTime &lt; #{end}
        </if>

        <if test="activityId != null and activityId != ''">
            and activity.id = #{activityId}
        </if>

        <if test="channelCode != null and channelCode != ''">
            and channel.code = #{channelCode}
        </if>
        GROUP BY
        DATE_FORMAT( `genTime`, '%Y-%m-%d' ),
        channel.`name`,
        activity.`name`
        ORDER BY
        DATE_FORMAT(
        `genTime`,
        '%Y-%m-%d'
        ) DESC

    </select>

    <select id="getTotalAmountInRangeNum" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM (
        SELECT points_balance.uid,SUM(COALESCE(points_journal.amount, 0)) AS total_amount
        FROM points_balance
        LEFT JOIN points_journal ON points_balance.uid = points_journal.uid
        WHERE points_balance.countryId = #{countryId}
        GROUP BY points_balance.uid
        ) AS subquery
        WHERE total_amount &gt;= #{rangeStart}
        <if test="rangeEnd!=null">
            AND total_amount &lt; #{rangeEnd}
        </if>
    </select>

</mapper>